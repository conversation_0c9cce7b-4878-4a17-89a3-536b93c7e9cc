/**
 * Test class for AR_ConfigurationUtil
 * Tests configuration management functionality
 */
@IsTest
private class AR_ConfigurationUtilTest {

    @IsTest
    static void testGetOrgWideEmailAddressId() {
        Test.startTest();
        
        String emailId = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        
        Test.stopTest();
        
        // Should return default value since no custom metadata exists in test
        System.assertNotEquals(null, emailId, 'Should return a value');
        System.assert(emailId.length() > 0, 'Should return non-empty value');
    }

    @IsTest
    static void testGetP1EscalationQueueName() {
        Test.startTest();
        
        String queueName = AR_ConfigurationUtil.getP1EscalationQueueName();
        
        Test.stopTest();
        
        // Should return default value since no custom metadata exists in test
        System.assertNotEquals(null, queueName, 'Should return a queue name');
        System.assert(queueName.length() > 0, 'Should return non-empty queue name');
    }

    @IsTest
    static void testGetUnassignedQueueName() {
        Test.startTest();
        
        String queueName = AR_ConfigurationUtil.getUnassignedQueueName();
        
        Test.stopTest();
        
        // Should return default value since no custom metadata exists in test
        System.assertNotEquals(null, queueName, 'Should return a queue name');
        System.assert(queueName.length() > 0, 'Should return non-empty queue name');
    }

    @IsTest
    static void testGetRegionalManagerTitle() {
        Test.startTest();
        
        String title = AR_ConfigurationUtil.getRegionalManagerTitle();
        
        Test.stopTest();
        
        // Should return default value since no custom metadata exists in test
        System.assertNotEquals(null, title, 'Should return a title');
        System.assert(title.length() > 0, 'Should return non-empty title');
    }

    @IsTest
    static void testGetConfigValueWithDefault() {
        Test.startTest();
        
        String value = AR_ConfigurationUtil.getConfigValue('NonExistentKey', 'DefaultValue');
        
        Test.stopTest();
        
        // Should return default value for non-existent key
        System.assertEquals('DefaultValue', value, 'Should return default value');
    }

    @IsTest
    static void testGetConfigValueWithNullKey() {
        Test.startTest();
        
        String value = AR_ConfigurationUtil.getConfigValue(null, 'DefaultValue');
        
        Test.stopTest();
        
        // Should return default value for null key
        System.assertEquals('DefaultValue', value, 'Should return default value for null key');
    }

    @IsTest
    static void testGetConfigValueWithBlankKey() {
        Test.startTest();
        
        String value = AR_ConfigurationUtil.getConfigValue('', 'DefaultValue');
        
        Test.stopTest();
        
        // Should return default value for blank key
        System.assertEquals('DefaultValue', value, 'Should return default value for blank key');
    }

    @IsTest
    static void testRefreshCache() {
        Test.startTest();
        
        // Get initial value
        String initialValue = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        
        // Refresh cache
        AR_ConfigurationUtil.refreshCache();
        
        // Get value again
        String refreshedValue = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        
        Test.stopTest();
        
        // Values should be the same (since no metadata changes in test)
        System.assertEquals(initialValue, refreshedValue, 'Values should be consistent after cache refresh');
    }

    @IsTest
    static void testGetAllConfigurations() {
        Test.startTest();
        
        Map<String, String> allConfigs = AR_ConfigurationUtil.getAllConfigurations();
        
        Test.stopTest();
        
        // Should return a map (may be empty in test context)
        System.assertNotEquals(null, allConfigs, 'Should return a map');
    }

    @IsTest
    static void testValidateRequiredConfigurations() {
        Test.startTest();
        
        List<String> missingConfigs = AR_ConfigurationUtil.validateRequiredConfigurations();
        
        Test.stopTest();
        
        // Should return a list (may contain missing configs in test context)
        System.assertNotEquals(null, missingConfigs, 'Should return a list');
    }

    @IsTest
    static void testConfigurationConstants() {
        Test.startTest();
        
        // Test that constants are properly defined
        System.assertNotEquals(null, AR_ConfigurationUtil.ORG_WIDE_EMAIL_KEY, 'Constant should be defined');
        System.assertNotEquals(null, AR_ConfigurationUtil.P1_ESCALATION_QUEUE_KEY, 'Constant should be defined');
        System.assertNotEquals(null, AR_ConfigurationUtil.UNASSIGNED_QUEUE_KEY, 'Constant should be defined');
        System.assertNotEquals(null, AR_ConfigurationUtil.REGIONAL_MANAGER_TITLE_KEY, 'Constant should be defined');
        
        Test.stopTest();
        
        // Verify constants are not empty
        System.assert(AR_ConfigurationUtil.ORG_WIDE_EMAIL_KEY.length() > 0, 'Constant should not be empty');
        System.assert(AR_ConfigurationUtil.P1_ESCALATION_QUEUE_KEY.length() > 0, 'Constant should not be empty');
        System.assert(AR_ConfigurationUtil.UNASSIGNED_QUEUE_KEY.length() > 0, 'Constant should not be empty');
        System.assert(AR_ConfigurationUtil.REGIONAL_MANAGER_TITLE_KEY.length() > 0, 'Constant should not be empty');
    }

    @IsTest
    static void testErrorHandling() {
        Test.startTest();
        
        // Test error handling by calling methods that might fail
        try {
            String value = AR_ConfigurationUtil.getConfigValue('TestKey', 'TestDefault');
            AR_ConfigurationUtil.refreshCache();
            Map<String, String> configs = AR_ConfigurationUtil.getAllConfigurations();
            List<String> missing = AR_ConfigurationUtil.validateRequiredConfigurations();
            
            // If we reach here, error handling worked properly
            System.assert(true, 'Error handling should prevent exceptions');
            
        } catch (Exception e) {
            System.assert(false, 'Should not throw exceptions: ' + e.getMessage());
        }
        
        Test.stopTest();
    }

    @IsTest
    static void testCacheInitialization() {
        Test.startTest();
        
        // Call multiple methods to test cache initialization
        String email = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        String queue = AR_ConfigurationUtil.getP1EscalationQueueName();
        String title = AR_ConfigurationUtil.getRegionalManagerTitle();
        
        Test.stopTest();
        
        // All should return valid values
        System.assertNotEquals(null, email, 'Email should be retrieved');
        System.assertNotEquals(null, queue, 'Queue should be retrieved');
        System.assertNotEquals(null, title, 'Title should be retrieved');
    }

    @IsTest
    static void testMultipleConfigAccess() {
        Test.startTest();
        
        // Access same configuration multiple times to test caching
        String value1 = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        String value2 = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        String value3 = AR_ConfigurationUtil.getOrgWideEmailAddressId();
        
        Test.stopTest();
        
        // All values should be the same
        System.assertEquals(value1, value2, 'Cached values should be consistent');
        System.assertEquals(value2, value3, 'Cached values should be consistent');
    }
}

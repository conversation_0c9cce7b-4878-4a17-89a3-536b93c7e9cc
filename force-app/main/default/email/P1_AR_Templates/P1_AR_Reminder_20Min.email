<messaging:emailTemplate subject="🔥 URGENT: P1 AR Unassigned for 20 Minutes - {!relatedTo.Name}" 
                          recipientType="User" 
                          relatedToType="Assistance_Request__c">
<messaging:htmlEmailBody >
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P1 AR 20-Minute Critical Reminder</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; }
        .critical-banner { background-color: #bd2130; color: white; padding: 10px; text-align: center; font-weight: bold; font-size: 16px; animation: blink 1s infinite; }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.7; } }
        .content { padding: 20px; }
        .ar-details { background-color: #f8f9fa; border-left: 4px solid #dc3545; padding: 15px; margin: 15px 0; }
        .detail-row { margin: 8px 0; }
        .label { font-weight: bold; color: #333; }
        .value { color: #666; }
        .critical-warning { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 15px 0; }
        .action-button { display: inline-block; background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 15px 0; animation: pulse 2s infinite; }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; color: #666; font-size: 12px; }
        .timer { background-color: #dc3545; color: white; padding: 15px; border-radius: 4px; text-align: center; font-size: 20px; font-weight: bold; margin: 15px 0; }
        .countdown { background-color: #bd2130; color: white; padding: 10px; border-radius: 4px; text-align: center; font-size: 16px; font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="critical-banner">
            🔥 CRITICAL SLA ALERT - 20 MINUTES ELAPSED 🔥
        </div>
        
        <div class="header">
            <h1>P1 Critical Assignment Alert</h1>
        </div>
        
        <div class="content">
            <div class="timer">
                ⏰ 20 MINUTES UNASSIGNED
            </div>
            
            <div class="countdown">
                ⚠️ ESCALATION IN 10 MINUTES ⚠️
            </div>
            
            <p style="color: #dc3545; font-weight: bold; font-size: 16px;">
                This <strong>P1 (Critical)</strong> Assistance Request has been unassigned for <strong>20 minutes</strong> and is approaching critical escalation threshold.
            </p>
            
            <div class="ar-details">
                <h3 style="margin-top: 0; color: #dc3545;">Assistance Request Details</h3>
                
                <div class="detail-row">
                    <span class="label">AR Number:</span>
                    <span class="value">{!relatedTo.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Priority:</span>
                    <span class="value" style="color: #dc3545; font-weight: bold;">{!relatedTo.Priority__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value">{!relatedTo.Status__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Created:</span>
                    <span class="value">{!relatedTo.CreatedDate}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Subject:</span>
                    <span class="value">{!relatedTo.Subject__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Account:</span>
                    <span class="value">{!relatedTo.Account__r.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Contact:</span>
                    <span class="value">{!relatedTo.Contact__r.Name}</span>
                </div>
            </div>
            
            <div class="critical-warning">
                <h4 style="margin-top: 0; color: #721c24;">🔥 CRITICAL SLA STATUS</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Time Elapsed:</strong> 20 minutes</li>
                    <li><strong>Assignment Target:</strong> Within 15 minutes (🔥 SEVERELY BREACHED)</li>
                    <li><strong>Escalation:</strong> Will occur in 10 minutes if still unassigned</li>
                    <li><strong>Impact:</strong> Customer satisfaction at risk</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="{!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}" class="action-button">
                    🚨 ASSIGN IMMEDIATELY 🚨
                </a>
            </div>
            
            <div style="background-color: #dc3545; color: white; padding: 15px; border-radius: 4px; text-align: center; font-weight: bold; margin: 15px 0;">
                CRITICAL ACTION REQUIRED: This request must be assigned within the next 10 minutes to prevent escalation to the Queue Owner.
            </div>
        </div>
        
        <div class="footer">
            This is an automated critical SLA alert from the AR Management System.<br>
            Please do not reply to this email.
        </div>
    </div>
</body>
</html>
</messaging:htmlEmailBody>

<messaging:plainTextEmailBody >
🔥 CRITICAL SLA ALERT - 20 MINUTES ELAPSED 🔥

P1 Critical Assignment Alert

⏰ 20 MINUTES UNASSIGNED
⚠️ ESCALATION IN 10 MINUTES ⚠️

Assistance Request: {!relatedTo.Name}
Priority: {!relatedTo.Priority__c}
Status: {!relatedTo.Status__c}
Created: {!relatedTo.CreatedDate}

Subject: {!relatedTo.Subject__c}
Account: {!relatedTo.Account__r.Name}
Contact: {!relatedTo.Contact__r.Name}

🔥 CRITICAL SLA STATUS:
- Time Elapsed: 20 minutes
- Assignment Target: Within 15 minutes (🔥 SEVERELY BREACHED)
- Escalation: Will occur in 10 minutes if still unassigned
- Impact: Customer satisfaction at risk

This P1 (Critical) Assistance Request has been unassigned for 20 minutes and is approaching critical escalation threshold.

View and Assign Request: {!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}

🚨 CRITICAL ACTION REQUIRED: This request must be assigned within the next 10 minutes to prevent escalation to the Queue Owner.

---
This is an automated critical SLA alert from the AR Management System.
Please do not reply to this email.
</messaging:plainTextEmailBody>
</messaging:emailTemplate>

<messaging:emailTemplate subject="⚠️ REMINDER: P1 AR Unassigned for 15 Minutes - {!relatedTo.Name}" 
                          recipientType="User" 
                          relatedToType="Assistance_Request__c">
<messaging:htmlEmailBody >
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P1 AR 15-Minute Reminder</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background-color: #ff8c00; color: white; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; }
        .warning-banner { background-color: #ff6b35; color: white; padding: 10px; text-align: center; font-weight: bold; font-size: 16px; }
        .content { padding: 20px; }
        .ar-details { background-color: #f8f9fa; border-left: 4px solid #ff8c00; padding: 15px; margin: 15px 0; }
        .detail-row { margin: 8px 0; }
        .label { font-weight: bold; color: #333; }
        .value { color: #666; }
        .sla-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0; }
        .action-button { display: inline-block; background-color: #ff8c00; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; color: #666; font-size: 12px; }
        .timer { background-color: #ff6b35; color: white; padding: 10px; border-radius: 4px; text-align: center; font-size: 18px; font-weight: bold; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-banner">
            ⚠️ SLA REMINDER - 15 MINUTES ELAPSED ⚠️
        </div>
        
        <div class="header">
            <h1>P1 Assignment Reminder</h1>
        </div>
        
        <div class="content">
            <div class="timer">
                ⏰ 15 MINUTES UNASSIGNED
            </div>
            
            <p>This <strong>P1 (Critical)</strong> Assistance Request has been unassigned for <strong>15 minutes</strong> and requires immediate assignment.</p>
            
            <div class="ar-details">
                <h3 style="margin-top: 0; color: #ff8c00;">Assistance Request Details</h3>
                
                <div class="detail-row">
                    <span class="label">AR Number:</span>
                    <span class="value">{!relatedTo.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Priority:</span>
                    <span class="value" style="color: #d73527; font-weight: bold;">{!relatedTo.Priority__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value">{!relatedTo.Status__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Created:</span>
                    <span class="value">{!relatedTo.CreatedDate}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Subject:</span>
                    <span class="value">{!relatedTo.Subject__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Account:</span>
                    <span class="value">{!relatedTo.Account__r.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Contact:</span>
                    <span class="value">{!relatedTo.Contact__r.Name}</span>
                </div>
            </div>
            
            <div class="sla-warning">
                <h4 style="margin-top: 0; color: #856404;">⚠️ SLA Status</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Time Elapsed:</strong> 15 minutes</li>
                    <li><strong>Assignment Target:</strong> Within 15 minutes (⚠️ TARGET MISSED)</li>
                    <li><strong>Escalation:</strong> Will occur at 30 minutes if still unassigned</li>
                    <li><strong>Time Remaining:</strong> 15 minutes before escalation</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="{!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}" class="action-button">
                    Assign Request Now
                </a>
            </div>
            
            <p style="color: #d73527; font-weight: bold; text-align: center;">
                IMMEDIATE ACTION REQUIRED: Please assign this request to an available team member to prevent SLA breach.
            </p>
        </div>
        
        <div class="footer">
            This is an automated SLA reminder from the AR Management System.<br>
            Please do not reply to this email.
        </div>
    </div>
</body>
</html>
</messaging:htmlEmailBody>

<messaging:plainTextEmailBody >
⚠️ SLA REMINDER - 15 MINUTES ELAPSED ⚠️

P1 Assignment Reminder

⏰ 15 MINUTES UNASSIGNED

Assistance Request: {!relatedTo.Name}
Priority: {!relatedTo.Priority__c}
Status: {!relatedTo.Status__c}
Created: {!relatedTo.CreatedDate}

Subject: {!relatedTo.Subject__c}
Account: {!relatedTo.Account__r.Name}
Contact: {!relatedTo.Contact__r.Name}

⚠️ SLA Status:
- Time Elapsed: 15 minutes
- Assignment Target: Within 15 minutes (⚠️ TARGET MISSED)
- Escalation: Will occur at 30 minutes if still unassigned
- Time Remaining: 15 minutes before escalation

This P1 (Critical) Assistance Request has been unassigned for 15 minutes and requires immediate assignment.

View and Assign Request: {!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}

IMMEDIATE ACTION REQUIRED: Please assign this request to an available team member to prevent SLA breach.

---
This is an automated SLA reminder from the AR Management System.
Please do not reply to this email.
</messaging:plainTextEmailBody>
</messaging:emailTemplate>

<?xml version="1.0" encoding="UTF-8"?>
<EmailTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <available>true</available>
    <description>30-minute final reminder for unassigned P1 Assistance Requests before escalation</description>
    <encodingKey>UTF-8</encodingKey>
    <name>P1 AR 30-Minute Final Reminder</name>
    <style>none</style>
    <subject>💥 FINAL ALERT: P1 AR Unassigned for 30 Minutes - Escalating Now - {!Assistance_Request__c.Name}</subject>
    <textOnly>FINAL ALERT: P1 Assistance Request Unassigned for 30 Minutes - Escalating Now

Assistance Request: {!Assistance_Request__c.Name}
Priority: {!Assistance_Request__c.Priority__c}
Status: {!Assistance_Request__c.Status__c}
Created: {!Assistance_Request__c.CreatedDate}
Time Unassigned: 30 minutes

Subject: {!Assistance_Request__c.Subject__c}
Account: {!Assistance_Request__c.Account__r.Name}
Contact: {!Assistance_Request__c.Contact__r.Name}

💥 SLA BREACH ALERT: This P1 Assistance Request has been unassigned for 30 minutes and is now being escalated to the Queue Owner.

ESCALATION ACTIONS:
- Request is being escalated to Queue Owner
- SLA has been marked as breached
- Customer impact is now critical

This is the final automated reminder. The request has been escalated due to SLA breach.

View Assistance Request: {!Assistance_Request__c.Link}

Thank you,
AR SLA Management System</textOnly>
    <type>custom</type>
    <uiType>Aloha</uiType>
</EmailTemplate>

<?xml version="1.0" encoding="UTF-8"?>
<EmailTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <available>true</available>
    <description>Escalation notification sent to Queue Owner when P1 AR SLA is breached</description>
    <encodingKey>UTF-8</encodingKey>
    <name>P1 AR Escalation to Queue Owner</name>
    <style>none</style>
    <subject>🚨 SLA BREACH: P1 AR Escalated to You - {!Assistance_Request__c.Name}</subject>
    <textOnly>SLA BREACH: P1 Assistance Request Escalated to You

Assistance Request: {!Assistance_Request__c.Name}
Priority: {!Assistance_Request__c.Priority__c}
Status: {!Assistance_Request__c.Status__c}
Created: {!Assistance_Request__c.CreatedDate}
SLA Breach Time: 30+ minutes unassigned

Subject: {!Assistance_Request__c.Subject__c}
Description: {!Assistance_Request__c.Description__c}

Account: {!Assistance_Request__c.Account__r.Name}
Contact: {!Assistance_Request__c.Contact__r.Name}

🚨 SLA BREACH NOTIFICATION: This P1 Assistance Request has breached the assignment SLA and has been escalated to you as the Queue Owner.

ESCALATION DETAILS:
- Request was unassigned for 30+ minutes
- Multiple reminders were sent to queue members
- SLA has been marked as breached
- Customer satisfaction is at risk

REQUIRED ACTIONS:
1. Immediately review the request details
2. Assign to an available senior team member
3. Consider customer communication if needed
4. Review queue staffing and processes

View Assistance Request: {!Assistance_Request__c.Link}

Thank you,
AR SLA Management System</textOnly>
    <type>custom</type>
    <uiType>Aloha</uiType>
</EmailTemplate>

<messaging:emailTemplate subject="🚨 URGENT: P1 Assistance Request Assigned - {!relatedTo.Name}" 
                          recipientType="User" 
                          relatedToType="Assistance_Request__c">
<messaging:htmlEmailBody >
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P1 Assistance Request Assignment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background-color: #d73527; color: white; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; }
        .urgent-banner { background-color: #ff4444; color: white; padding: 10px; text-align: center; font-weight: bold; font-size: 16px; }
        .content { padding: 20px; }
        .ar-details { background-color: #f8f9fa; border-left: 4px solid #d73527; padding: 15px; margin: 15px 0; }
        .detail-row { margin: 8px 0; }
        .label { font-weight: bold; color: #333; }
        .value { color: #666; }
        .sla-info { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0; }
        .action-button { display: inline-block; background-color: #d73527; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 15px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="urgent-banner">
            🚨 CRITICAL PRIORITY - IMMEDIATE ACTION REQUIRED 🚨
        </div>
        
        <div class="header">
            <h1>P1 Assistance Request Assigned</h1>
        </div>
        
        <div class="content">
            <p>A <strong>P1 (Critical)</strong> Assistance Request has been assigned to the AR Special Priority Queue and requires immediate attention.</p>
            
            <div class="ar-details">
                <h3 style="margin-top: 0; color: #d73527;">Assistance Request Details</h3>
                
                <div class="detail-row">
                    <span class="label">AR Number:</span>
                    <span class="value">{!relatedTo.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Priority:</span>
                    <span class="value" style="color: #d73527; font-weight: bold;">{!relatedTo.Priority__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value">{!relatedTo.Status__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Created:</span>
                    <span class="value">{!relatedTo.CreatedDate}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Subject:</span>
                    <span class="value">{!relatedTo.Subject__c}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Account:</span>
                    <span class="value">{!relatedTo.Account__r.Name}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Contact:</span>
                    <span class="value">{!relatedTo.Contact__r.Name} ({!relatedTo.Contact__r.Email})</span>
                </div>
            </div>
            
            <div class="sla-info">
                <h4 style="margin-top: 0; color: #856404;">⏰ SLA Requirements</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Assignment Target:</strong> Within 15 minutes</li>
                    <li><strong>Work Start Target:</strong> Immediately upon assignment</li>
                    <li><strong>Escalation:</strong> After 30 minutes if unassigned</li>
                </ul>
            </div>
            
            <p><strong>Description:</strong></p>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                {!relatedTo.Description__c}
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="{!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}" class="action-button">
                    View Assistance Request
                </a>
            </div>
            
            <p style="color: #666; font-style: italic;">
                Please review this request immediately and assign to an appropriate team member. 
                Time is critical for P1 requests.
            </p>
        </div>
        
        <div class="footer">
            This is an automated message from the AR SLA Management System.<br>
            Please do not reply to this email.
        </div>
    </div>
</body>
</html>
</messaging:htmlEmailBody>

<messaging:plainTextEmailBody >
URGENT: P1 Assistance Request Assigned

🚨 CRITICAL PRIORITY - IMMEDIATE ACTION REQUIRED 🚨

Assistance Request: {!relatedTo.Name}
Priority: {!relatedTo.Priority__c}
Status: {!relatedTo.Status__c}
Created: {!relatedTo.CreatedDate}

Subject: {!relatedTo.Subject__c}
Description: {!relatedTo.Description__c}

Account: {!relatedTo.Account__r.Name}
Contact: {!relatedTo.Contact__r.Name} ({!relatedTo.Contact__r.Email})

⏰ SLA Requirements:
- Assignment Target: Within 15 minutes
- Work Start Target: Immediately upon assignment  
- Escalation: After 30 minutes if unassigned

This is a P1 (Critical) Assistance Request that requires immediate attention. Please review and assign to an appropriate team member as soon as possible.

View Assistance Request: {!URLFOR($Action.Assistance_Request__c.View, relatedTo.Id)}

Please review this request immediately and assign to an appropriate team member. Time is critical for P1 requests.

---
This is an automated message from the AR SLA Management System.
Please do not reply to this email.
</messaging:plainTextEmailBody>
</messaging:emailTemplate>

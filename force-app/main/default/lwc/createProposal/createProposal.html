<template>
    <lightning-card>
        <header class="slds-modal__header">
            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Proposal</h2>
        </header>
        <lightning-record-edit-form id="ProposalForm" name="ProposalForm" record-id={recordId} object-api-name="Proposal__c" onsuccess={handleSuccess} onsubmit={handleSubmit}>

            <div class="slds-grid slds-wrap slds-align_absolute-center" style="margin-top:10px">
                <lightning-button 
                    class="slds-p-around_xx-small" 
                    variant="destructive"
                    icon-name="utility:close"
                    label="Cancel"
                    onclick={redirectToHomePage}>
                </lightning-button> 
                <lightning-button 
                    class="slds-p-around_xx-small" 
                    icon-name="utility:home"
                    label="Save and Return to Home"
                    onclick={returnHome}
                    type="submit" >
                </lightning-button>
                <lightning-button 
                    class="slds-p-around_xx-small" 
                    icon-name="utility:save"
                    label="Save Proposal"
                    onclick={saveProposal}
                    type="submit">
                </lightning-button>
                <lightning-button 
                    class="slds-p-around_xx-small" 
                    icon-name="utility:file"
                    label="Preview Proposal PDF"
                    onclick={previewPdf}
                    type="submit">
                </lightning-button>
                <lightning-button 
                    class="slds-p-around_xx-small" 
                    icon-name="utility:email"
                    label="Send Email"
                    onclick={sendEmail}
                    type="submit">
                </lightning-button>
            </div>

            <div class="slds-grid slds-gutters" style="padding-top: 10px">
                <div class="slds-col slds-size_1-of-2" style="margin-left: 5px">
                    <lightning-input-field type="text" label="Coordinator" value={currentUser} field-name="Coordinator__c" disabled></lightning-input-field>
                    <lightning-input-field type="text" label="Proposal Title: (this title WILL NOT display in the PDF)"
                        value={title} onchange={onTitleChange} field-name="Name" re></lightning-input-field>

                        <!--
                    <lightning-textarea name="ProposalSummary" label="Proposal Summary Copy: (this WILL display in the PDF)" 
                        value={summary} onchange={onSummaryChange}></lightning-textarea>
                        -->
                    <div class="slds-grid slds-gutters">
                        <div class="slds-col slds-size_1-of-3">
                            <lightning-input-field type="text" label="Quote #:" value={quoteId} onchange={onQuoteNumberChange} field-name="Quote__c"></lightning-input-field>
                        </div>
                        <div class="slds-col slds-size_1-of-3">
                            <lightning-input-field type="text" label="Sales #:" value={salesOrderId} onchange={onSalesNumberChange} field-name="Sales_Order__c"></lightning-input-field>
                        </div>
                        <div class="slds-col slds-size_1-of-3" style="margin-top:21.5px">
                            <lightning-button label="Enter" title="Enter" onclick={preSave} class="slds-m-left_x-small" type="submit"></lightning-button>
                        </div>
                    </div>
                    <!--
                    <lightning-textarea name="ProposalNotes" label="Proposal Notes: (this WILL NOT display in the PDF)" 
                        value={notes} onchange={onNotesChange}></lightning-textarea>
                        -->
                    </div>
                <div class="slds-col slds-size_1-of-2" style="margin-left: -5px">

                    <lightning-card title="Account and Contacts">
                        <lightning-combobox
                        name="Location"
                        label="Location"
                        value={selectedLocation}
                        placeholder="Select Location"
                        options={LocationOptions}
                        onchange={changeLocation}                    
                    ></lightning-combobox>
                        <div class="slds-col slds-size_4-of-5">
                            <lightning-input-field field-name="Account__c" value={account} disabled></lightning-input-field>
                        </div>
                        <template if:true={account}>
                            <div style="margin-top: 5px">
                                <c-create-proposal-contacts account-id={account} oncontactselect={handlecontactselect}
                                  contact-list={contactList} selected-contact-list={selectedConList}
                                  filtered-contact-list={contactList} has-contact={hasContact}
                                  manager-list={managerList} selected-manager-list={selectedManList}
                                  filtered-manager-list={managerList} has-manager={hasManager}
                                  selected-additional-user-list={selectedAddConList} has-additional-user={hasAddContact}
                                  retrieved-user-id-list={selectedUserIdList} primary-contact={primaryContact}>
                                </c-create-proposal-contacts>
                            </div>
                        </template>
                        
                    </lightning-card>
                </div>
            </div>

            <div class="slds-text-align_right">
                <lightning-button class="slds-p-around_xx-small"
                onclick={openProduct} label="Add Additional Products">
                </lightning-button>
            </div>
        </lightning-record-edit-form>
        <div style="margin:10px 0px 100px 0px">
            <template if:true={hasTraining}>
                <c-create-proposal-table pli-data={pliList} ontablechange={refeshtable}></c-create-proposal-table>          
               

            </template>
        </div>
    </lightning-card>

    <template if:true={productModal}>
        <div class="productModal" style="height: 800px;">
            <section    role="dialog" 
                        tabindex="-1" 
                        aria-modal="true" 
                        aria-labelledby="modal-heading-01"
                        aria-describedby="modal-content-id-1" 
                        class="slds-modal slds-modal_large slds-fade-in-open">


            <div class="slds-modal__container" style="margin-top:-3.5rem">
                <header class="slds-modal__header">
                    <h2 id="modal-heading-02" class="slds-text-heading_medium slds-hyphenate">
                        Select Additional Products
                    </h2>
                </header>
                <div class="slds-modal__content slds-p-around_x-small">
                    Search Product
                    <lightning-input type="text" label="Search Product" value={productSearchStr} 
                      onkeyup={onProductSearch} variant="label-hidden"
                      style="margin: -20px 0px 0px 100px"></lightning-input>

                    <div class="slds-scrollable" style="height:200px">
                        <lightning-datatable
                            key-field="Id"
                            data={productlist}
                            onrowselection={getSelectedProduct}
                            columns={productColumn}>
                        </lightning-datatable> 
                    </div>
                <div class="slds-grid slds-wrap slds-align_absolute-center">
                    <lightning-button class="slds-p-around_xx-small" icon-name="utility:close"
                        label="Close" variant="destructive" onclick={closeProductModal}>
                    </lightning-button>
                    <lightning-button class="slds-p-around_xx-small" icon-name="utility:add"
                    label="Add Product" variant="constructive" onclick={addSelectedProduct}>
                    </lightning-button> 
                </div>
                </div>
            </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>


    <template if:true={resaveModal}>
        <div class="resaveModal" style="height: 800px;">
            <section    role="dialog" 
                        tabindex="-1" 
                        aria-modal="true" 
                        aria-labelledby="modal-heading-01"
                        aria-describedby="modal-content-id-1" 
                        class="slds-modal slds-modal_large slds-fade-in-open">
                <div class="slds-modal__container" style="margin-top:-3.5rem">
                    <header class="slds-modal__header">
                        <lightning-button class="slds-p-around_xx-medium" icon-name="utility:save"
                        label="Continue" variant="constructive" onclick={resaveAfterProduct} >
                        </lightning-button> 
                    </header>

            </div>
            </section>


            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>


</template>
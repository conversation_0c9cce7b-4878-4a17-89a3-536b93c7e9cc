import { LightningElement, track, api } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import createSOIEvents from "@salesforce/apex/TE_ConAllocate.createSOIEvents";

import getSalesOrderLocations from "@salesforce/apex/TE_AllocateDeallocateCtrl.getSalesOrderLocations";

export default class Te_conAllocate extends LightningElement {

    @api wrapperSOIList;
    @api selectedSOIIdList;
    @api isAllocate;

    @track locationOptn = [];
    @track selectedlocation;
    @track isShowCreateSOI = true;
    @track isLocationModalOpen = false;
    @track AllSOILocation = [];
    @track selectedProductList = [];
    @track selectedProd;
    @track allocateData;
    @track isSOIECreated = true;
    quantity;
    @track selectedProd = {
        id: "",
        productName: "",
        materialGroup: "",
        requiredCredits: "",
        location: "",
        objProd:{ Id:""},
        salesOrderId:"",
        salesOrderItemId:"",
        locationName: "",
        expirationDt: ""

    };
    isDisabled = false;

    connectedCallback(){
        this.locationOptn = [];
        
        if (this.isAllocate == true) {
            this.quantity = 1;
            
            this.openModal();
            
            
        }
    }

    openModal(){
        this.getSalesOrderLocations();
        this.isLocationModalOpen = true;
        this.quantity = 1;
        console.log('wrapperSOIList'+JSON.stringify(this.wrapperSOIList));
        
    }

    getSalesOrderLocations() {
        getSalesOrderLocations({
            selectedSOIIdList: this.selectedSOIIdList
        })
            .then((data) => {
                this.error = undefined;
                const newData = data.map((a) => Object.assign({}, a));
                this.AllSOILocation.push({
                    Id: "nolocation",
                    Name: "No Location"
                });
                this.AllSOILocation = this.AllSOILocation.concat(newData);
                console.log('this.AllSOILocation'+this.AllSOILocation);
                for (let i = 0; i < this.AllSOILocation.length; i++) {
                    if (this.AllSOILocation[i].Id != "nolocation") {
                        this.locationOptn.push({
                            label: this.AllSOILocation[i].Name,
                            value: this.AllSOILocation[i].Id
                        });
                    }
                }
                console.log('this.locationOptn'+this.locationOptn);
            })
            .catch((error) => {
                console.log(
                    "Error while fetching sales order locations : " +
                        error?.body?.message
                );
                this.error = error;
                this.record = undefined;
                this.data = undefined;
                this.showToastMessage(
                    "Error while fetching sales order locations",
                    "error"
                );
            });
    }

    closeLocModal(event){
        this.isLocationModalOpen = false;
        this.isShowCreateSOI = false;
        this.AllSOILocation = [];
        this.locationOptn = [];
    }

    findLocationDetailById(locationId) {
        return this.locationOptn.find(
            (element) => element.value === locationId
        );
    }

    defaultPreselectedLocation(selectedProductLocationId) {
        let verifiedLocation = this.findLocationDetailById(
            selectedProductLocationId
        );
       
    }

    selLocationChange(event) {
        this.selectedlocation = event.detail.value;
        for (let i = 0; i < this.locationOptn.length; i++) {
            if (this.locationOptn[i].value === this.selectedlocation) {
                this.selectedProd.locationName = this.locationOptn[i].label;
                this.selectedProd.location = this.selectedlocation;
            }
        }
    }

    qntyChange(event) {
        this.quantity = event.target.value;
    }

    insertSOIEvents(event) {
        this.isSOIECreated = false;
        this.selectedProd.requiredCredits = this.quantity;
        
        this.selectedProd.objProd.Id = this.wrapperSOIList[0].counterInfo.Product__c;
        this.selectedProd.Id = this.wrapperSOIList[0].counterInfo.Product__c;
        this.selectedProd.salesOrderId = this.wrapperSOIList[0].counterInfo.Sales_Order__c;
        this.selectedProd.salesOrderItemId = this.wrapperSOIList[0].counterInfo.Sales_Order_Item__c;
        this.selectedProd.expirationDt = this.wrapperSOIList[0].counterInfo.End_Date__c;
        this.selectedProductList.push(this.selectedProd);
        
        
        createSOIEvents({
            lstSelectedProducts: this.selectedProductList
          })
              .then((result) => {
                  this.isSOIECreated = true;
                  this.showToastMessage(
                      "SOI Events created successfully",
                      "success"
                  );
                  //fire update to parent to refresh trainingSelection.
                  //need to delay this call because the trigger is updating fields on Counter.
                  let selectedEvent = new CustomEvent("allocated");
                  this.dispatchEvent(selectedEvent, {
                      bubbles: true,
                      composed: true
                  });
                 this.closeLocModal();
              })
              .catch((error) => {
                  console.log(
                      "Error while saving sales order item events : " +
                          error?.body?.message
                  );
                  this.error = error?.body?.message;
                  this.showToastMessage(
                      "Error while saving sales order item events",
                      "error"
                  );
              });
      }

      showToastMessage(message ,variant ,) {
        const evt = new ShowToastEvent({
         message: message,
          variant: variant
         });
        this.dispatchEvent(evt);
         
      }

}
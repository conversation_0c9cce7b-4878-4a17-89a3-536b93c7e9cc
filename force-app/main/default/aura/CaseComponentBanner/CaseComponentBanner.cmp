<aura:component implements="flexipage:availableForAllPageTypes,force:hasRecordId,force:appHostable" controller="VFSL_CaseButtonLightningCtrl">
    <aura:attribute name="recordId" type="Id"/>
    <aura:attribute name="showPopup" type="boolean"/>
    <aura:attribute name="caseCancellation" type="boolean" />
    <aura:attribute name="closeCase" type="boolean" />
    <aura:attribute name="ecf" type="boolean" />
    <aura:attribute name="knowledgeArticle" type="boolean" />
    <aura:attribute name="PWO" type="boolean" />
    <aura:attribute name="teamMember" type="boolean" />
    <!--Lightning data Service -->
    <aura:attribute name="record" type="Object"/>
    <aura:attribute name="simpleRecord" type="Object"/>
    <aura:attribute name="recordError" type="String"/>
    <force:recordData aura:id="recordLoader"
                      recordId="{!v.recordId}"
                      layoutType="FULL"
                      targetRecord="{!v.record}"
                      targetFields="{!v.simpleRecord}"
                      targetError="{!v.recordError}" fields="RecordType.DeveloperName,Is_escalation_to_the_CLT_required__c,Closed_Case_Reason__c,
                                                             IsClosed,Case_ECF_Count__c,Case_ECF_Submitted__c,EtQ_Complaint__c,
                                                             Was_anyone_injured__c,Reason,Is_This_a_Complaint__c,Incident_Workflow_Location__c,Error_Code__c,ProductSystem__c"/>
    <aura:attribute name="userInfo" type="String"/>
    <aura:handler name="init" value="this" action="{!c.doInit}"/>
    <lightning:flow aura:id="flowData"/>
    
    <lightning:card title="Case Actions" iconName="standard:case">
        
        <!-- Actions available for Helpdesk record type -->
        
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Helpdesk'}">
            <aura:if isTrue="{!and(v.userInfo != 'VMS Service - Dispatch',v.userInfo != 'VMS Marketing Lite – User',v.userInfo != 'VMS Marketing - User',v.userInfo != 'VMS Marketing - User NO SSO',
                             v.userInfo != 'VMS Service - Read Only')}">
                
                <!--<lightning:button class="slds-m-around_xxx-small" label="Cancel Case" 
                                  onclick="{!c.navigateToCancelCase}"/>-->
                <aura:if isTrue="{!v.simpleRecord.ProductSystem__c != null}">
                	<lightning:button class="slds-m-around_xxx-small" label="Create ECF" title="Create Escalated Complaint Form"
                                  onclick="{!c.navigateToECF}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Create Article" title="Create Knowledge Article"
                                  onclick="{!c.navigateToCreateKA}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Create Work Order" 
                                  onclick="{!c.navigateToCWO}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Create PHI Log" 
                                  onclick="{!c.navigateToPHI}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="SmartConnect Bridge" 
                                  onclick="{!c.navigateToSCBL}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="SmartConnect Asset" 
                                  onclick="{!c.navigateToSCAL}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Close Case" 
                                  onclick="{!c.navigateToCloseCase}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Update Case Team Member" 
                                  onclick="{!c.navigateToTeamMember}"/>
                </aura:if>
                <!-- <lightning:button class="slds-m-around_xxx-small" label="Create KA" 
                                  onclick="{!c.navigateToCreateKA}"/> -->
                <!-- <lightning:button class="slds-m-around_xxx-small" label="PSE Knowledge" 
                                  onclick="{!c.navigateToPSE}"/> --> 
                <aura:if isTrue="{!v.simpleRecord.ProductSystem__c == null}">
                	<lightning:button class="slds-m-around_xxx-small" label="Update SVMXC Fields" 
                                  	onclick="{!c.runReconcileSVMXC}"/>
            	</aura:if>
                
            </aura:if>
        </aura:if>
        
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Helpdesk'}">
            <aura:if isTrue="{!v.userInfo == 'VMS Service - Dispatch'}">
                <lightning:button class="slds-m-around_xxx-small" label="Cancel Case" 
                                  onclick="{!c.navigateToCancelCase}"/>
                <aura:if isTrue="{!v.simpleRecord.ProductSystem__c != null}">
                	<lightning:button class="slds-m-around_xxx-small" label="Create Work Order" 
                                  onclick="{!c.navigateToCWO}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Create PHI Log" 
                                  onclick="{!c.navigateToPHI}"/>
                	<lightning:button class="slds-m-around_xxx-small" label="Close Case" 
                                  onclick="{!c.navigateToCloseCase}"/>
                </aura:if>

            </aura:if>
        </aura:if>
        
        <!-- Actions available for IEM case record type-->
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'IEM'}">
            <lightning:button class="slds-m-around_xxx-small" label="Installation Work Instructions" 
                              onclick="{!c.navigateToIWI}"/>
            <lightning:button class="slds-m-around_xxx-small" label="Cancel Case" 
                              onclick="{!c.navigateToCancelCase}"/>
            <lightning:button class="slds-m-around_xxx-small" label="Update Installation Case" 
                              onclick="{!c.navigateToUpdateInstallationCase}"/>
            <lightning:button class="slds-m-around_xxx-small" label="PT_Update Installation Case" 
                              onclick="{!c.navigateToPTUpdateInstallationCase}"/>
            
        </aura:if>
        
        <!-- Create PWO action available for INST case record type -->
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName ==  'Installation'}">
            <!--<lightning:button class="slds-m-around_xxx-small" label="Cancel Case" 
                              onclick="{!c.navigateToCancelCase}"/>-->
            <lightning:button class="slds-m-around_xxx-small" label="Update Installation Case" 
                              onclick="{!c.navigateToUpdateInstallationCase}"/>
            <lightning:button class="slds-m-around_xxx-small" label="Create PWO" 
                              onclick="{!c.navigateToCreatePWO}"/>
            <lightning:button class="slds-m-around_xxx-small" label="Installation Work Instructions" 
                              onclick="{!c.navigateToIWI}"/>
            <lightning:button class="slds-m-around_xxx-small" label="PT_Update Installation Case" 
                              onclick="{!c.navigateToPTUpdateInstallationCase}"/>
        </aura:if>
        
        <!-- Actions available for PM case record type -->
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Project_Management'}">
            <lightning:button class="slds-m-around_xxx-small" label="SmartConnect Asset List" 
                              onclick="{!c.navigateToSCAL}"/>
            <lightning:button class="slds-m-around_xxx-small" label="SmartConnect Bridge List" 
                              onclick="{!c.navigateToSCBL}"/>
        </aura:if>
        
        <!-- Actions available for consulting and Training case record type -->
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Consulting' || v.simpleRecord.RecordType.DeveloperName == 'Training'}">
            <lightning:button class="slds-m-around_xxx-small" label="Cancel Case" 
                              onclick="{!c.navigateToCancelCase}"/>
        </aura:if>
        <!--
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Education'}">
            <lightning:button class="slds-m-around_xxx-small" label="Close Case" 
                              onclick="{!c.navigateToCloseCase}"/>
        </aura:if>
        -->
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'Consulting'}">
            <lightning:button class="slds-m-around_xxx-small" label="Close Case" 
                              onclick="{!c.navigateToCloseCase}"/>
            <lightning:button class="slds-m-around_xxx-small" label="Create Article" title="Create Knowledge Article"
                              onclick="{!c.navigateToKA}"/>
        </aura:if>
        
        <!-- End -->
        
        <aura:if isTrue="{!v.simpleRecord.RecordType.DeveloperName == 'VIC'}">
            <lightning:button class="slds-m-around_xxx-small" label="Create Article" title="Create Knowledge Article"
                              onclick="{!c.navigateToCreateKAVIC}"/>
        </aura:if>
        
        <aura:if isTrue="{!v.caseCancellation}">
            <c:CaseCancelComponent showPopup="{!v.caseCancellation}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        <aura:if isTrue="{!v.closeCase}">
            <c:CaseCloseComponent showPopup="{!v.closeCase}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        <aura:if isTrue="{!v.ecf}">
            <c:CaseECFComponent showPopup="{!v.ecf}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        <aura:if isTrue="{!v.knowledgeArticle}">
            <c:CaseknowledgeArticleComponent showPopup="{!v.knowledgeArticle}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        <aura:if isTrue="{!v.PWO}">
            <c:CaseCreatePWOComponent showPopup="{!v.PWO}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        <aura:if isTrue="{!v.teamMember}">
            <c:CaseAddTeamMemberComponent showPopup="{!v.teamMember}" caseRecord="{!v.simpleRecord}"/>
        </aura:if>
        
        
    </lightning:card>
    
         <div class="slds-spinner_container slds-is-relative">
        	<lightning:spinner aura:id="spinner" class="slds-hide" variant="brand" size="large"/>
    	</div>
</aura:component>
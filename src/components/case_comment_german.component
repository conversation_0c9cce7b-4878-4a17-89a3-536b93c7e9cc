<!--***************************************************************************
Created By - NA
Created Date - NA
Change Log:
Date/Modified By Name/Task or Story or Inc # /Description of Change
25-Sep-2017 - Rakesh - STSK0012867 - Added case owner (Line 52).
*************************************************************************************-->
<apex:component controller="SR_ComponentClassV1" access="global">

    <apex:attribute name="German" description="Case Closure Email" type="Id" required="required" assignTo="{!controllerValue}"/>
    <style>
    .col1{
    width:20%;
    position:relative;
    }

    .col2{
    width:80%;      
    position:relative;
    }

    </style>


    <table border="0" style="background-color : #FFFFFF;width:100%;font-size:13px; font-family:arial;" cellpadding="2" cellspacing ="0">
        <tr style="text-align:center;" >
        <td colspan="2">
        <tr><td>
        <apex:image value="{!$Label.VarianCustomerSupport}" style="width:90%;" id="LogoImage"/>
        </td>
        </tr>
        </td>
        </tr>
        <!--apex:image value="{!$Label.Varian_Partner_for_life_logo}"  style="width:auto;" / background-image:url({!$Label.Varian_Partner_for_life_logo});-->
        <tr style="text-align:left;">
        <td colspan="2"> 
        <!--<b>Case Number: {!RecCase.CaseNumber}</b><br/>-->
        <b><span style="color: #0000FF;font-size:15px;"> Case Number: </span> {!RecCase.CaseNumber}</b>
        <br/><br/><br/>
        Sehr geehrte(r) Frau/Herr {!RecCase.Contact.Name},   <br/><br/>
        Sie erhalten diese E-Mail, da Ihr case von einem Varian-Mitarbeiter aktualisiert wurde.  <br/><br/>

        </td>
        </tr>
        <tr style="text-align:Left;" >
        <td colspan="2">

        <b><u><span style ="color:#0000FF;">Fall Information</span></u></b><br/>
        Aktueller Stand: {!RecCase.status}  <br/>
        Produkt: {!RecCase.Asset.name}<br/>   <!-- DE3449, Update from SVMXC__Top_Level__r to ProductSystem__r-->
        Erstellungsdatum: {!DateToDisplayNA} <br/>
        Mitarbeiter: {!RecCase.Owner.Name}  <br/><br/>

        <b><u><span style ="color:#0000FF;">Fall-Titel:</span></u></b><br/>
        {!RecCase.Local_Subject__c} <br/><br/>
        <!--<b><u>Neuer Kommentar:</u></b>-->
        <b><u><span style ="color:#0000FF;">Neuer Kommentar:</span></u></b> <br/>
        {!CaseCommentObj.CommentBody }<br/>
        <!-- <apex:outputtext value="{!RecCase.Local_Case_Activity__c}" escape="false" /><br/> -->
        <!--If you feel case  was closed in error, please <b><a href="{!$Label.portal_redirection_url}"><U>Click Here</u></a></b> to create a new case and reference the original case number.-->
        <br/>
        Falls Sie über einen MyVarian Account verfügen und Ihren Fall aktualisieren oder dessen Stand erfahren möchten, klicken Sie bitte auf "View Case". Gerne können Sie auch direkt auf diese Email antworten. 

        <br/><br/>  <!--style="text-decoration:none"-->
        <b><span style ="color:#0000FF;">Hinweis:</span></b> 
        Mit Ihrer Erlaubnis haben wir die Möglichkeit per Fernwartungssoftware "SmartConnect" auf Ihr System zuzugreifen, um Sie bei der Fehlersuche sowie der Lösung des Falles zu unterstützen. Sollte die Fernwartung außerhalb der regulären Geschäftszeiten durchgeführt werden, senden wir Ihnen selbstverständlich eine detaillierte Beschreibung der ergriffenen Maßnahmen zur Untersuchung bzw. Lösung des Falles zu.
        <div style="text-align:left;">
        </div>
        </td>
        </tr>
    </TABLE>
</apex:component>
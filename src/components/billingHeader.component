<apex:component controller="CP_MessageController">
<apex:form >
        <apex:actionFunction name="logoutmethod" action="{!logoutmethod}" />
    </apex:form>
    <div id="topToolBar">
        <div>
            <a class="logo" href="/apex/CpHomePage">&nbsp;</a>

            <ul class="links primary-links" style="float:right;left: 1000px">
                <li class="menu-66 first"><a href="http://www.varian.com">Varian.com</a>
                </li>
                <!--
                <li class="menu-4486"><a href="/apex/CpMessageCenter">Messages ({!TotalMess})
                </a>
                </li>
                <li class="menu-56"><a href="/apex/CpContactUs">Contact Us</a>
                </li>
                 <apex:outputpanel rendered="{!Ispicvis}">
                <li><img src="{!usr.SmallPhotoUrl}" style="background-position: 0px -83px;background-repeat: no-repeat;border-radius: 25%;width: 25px;height: 25px;"/>
                </li>
                <li style="margin-left: -35px">
                <a href="/apex/CpMyAccount" title="">My
                        Account</a>
                </li>
                </apex:outputpanel>
                <apex:outputpanel rendered="{!!Ispicvis}">
                <li class="menu-3074">
                <a href="/apex/CpMyAccount" title="">My
                        Account</a>
                </li> </apex:outputpanel>-->
                <li class="menu-70 last"><a href="#" onclick="logoutmethodjava(); return false;">Log
                        out</a>
                </li>
            </ul>

        </div>
    </div>
    <!-- /topToolBar -->
    <SCRIPT LANGUAGE="JavaScript">
   
    function logoutmethodjava()
    {
      logoutmethod(); 
    }
</script>

</apex:component>
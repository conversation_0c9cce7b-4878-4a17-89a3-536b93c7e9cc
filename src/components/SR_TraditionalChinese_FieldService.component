<!--***************************************************************************
Created By: <PERSON><PERSON>h
Created Date : 08/28/2017
Description : Used in the Email template 'FieldService Survey Template On Case Closure' for the language Traditional Chinese
Change Log:
Date/Modified By Name/Task or Story or Inc # /Description of Change

*************************************************************************************-->
<apex:component controller="SR_ComponentClass" access="global">

<apex:attribute name="TraditionalChinese" description="Case Closure Email" type="Id" required="required" assignTo="{!controllerValue}"/>
<style>
.col1{
    width:20%;
    position:relative;
}

.col2{
    width:80%;
    position:relative;
}

</style>


<table border="0" style="background-color : #FFFFFF;width:100%;font-size:13px; font-family:arial;" cellpadding="2" cellspacing ="0">
 
 <!--<tr style="text-align:center;" >
  <td colspan="2"><apex:image value="{!$Label.Varian_Partner_for_life_logo}" width="100%" />
 </td>
 </tr>-->

 <tr style="text-align:center;" >
      <td colspan="2">
      <tr><td>
      <apex:image value="{!$Label.VarianCustomerSupport}" style="width:90%;" id="LogoImage"/>
      </td>
      </tr>
      </td>
     </tr>
<!--apex:image value="{!$Label.Varian_Partner_for_life_logo}"  style="width:auto;" / background-image:url({!$Label.Varian_Partner_for_life_logo});-->
<tr style="text-align:left;">
  <td colspan="2"> 
  <!--<b>Case Number: {!RecCase.CaseNumber}</b><br/>-->
<b><span style="color: #0000FF;font-size:15px;"> Case Number:</span> {!RecCase.CaseNumber}</b>
 <br/><br/><br/>
Dear {!RecCase.Contact.Name},   <br/><br/>
感謝您聯繫瓦里安公司的支援部門。這封電子郵件是確認您的案例及所有關聯的工作指示皆已完結。 <br/><br/>

您的回饋對我們很重要。請点击 
    <!-- STRY0484973 Remove SVMXC Reference
      <apex:outputPanel rendered="{!not(FSL)}">
          <apex:outputPanel rendered="{!not(Service)}">
              <b> <a href="http://survey.clicktools.com/app/survey/go.jsp?iv=3l8k05rt6y5ye&q1={!RecCase.contactid}&q2={!RecCase.Contact.Email}&q3={!RecCase.accountid}&q4={!RecCase.id}&q5={!RecCase.Subject}&q6={!RecCase.ClosedDate}&q7={!RecCase.ClosedBy__r.Name}&q8={!RecCase.ClosedBy__r.Managerid}&q9={!RecCase.ProductSystem__r.SVMXC__Product__r.Name}&language={!RecCase.Contact.Language_ISOCode__c}">Here</a></b> 以進行簡短調查。
          </apex:outputPanel>
          <apex:outputPanel rendered="{!Service}">
              <b> <a href="http://survey.clicktools.com/app/survey/go.jsp?iv=3l8k05rt6y5ye&q1={!RecCase.contactid}&q2={!RecCase.accountid}&q3={!RecCase.CaseNumber}&q4={!RecCase.Id}&q5={!RecCase.ClosedBy__c}&q6={!RecCase.ProductSystem__c}&language={!RecCase.SVMXC__Site__r.ERP_Default_Preferred_Language_Code__c}">Here</a></b> 以進行簡短調查。
          </apex:outputPanel>  <br/><br/>
      </apex:outputPanel>
  -->
      <apex:outputPanel rendered="{!FSL}">
          <apex:outputPanel rendered="{!not(Service)}">
              <b> <a href="http://survey.clicktools.com/app/survey/go.jsp?iv=3l8k05rt6y5ye&q1={!RecCase.contactid}&q2={!RecCase.Contact.Email}&q3={!RecCase.accountid}&q4={!RecCase.id}&q5={!RecCase.Subject}&q6={!RecCase.ClosedDate}&q7={!RecCase.ClosedBy__r.Name}&q8={!RecCase.ClosedBy__r.Managerid}&q9={!RecCase.Asset.Product_Name__c}&language={!RecCase.Contact.Language_ISOCode__c}">Here</a></b> 以進行簡短調查。
          </apex:outputPanel>
          <apex:outputPanel rendered="{!Service}">
              <b> <a href="http://survey.clicktools.com/app/survey/go.jsp?iv=3l8k05rt6y5ye&q1={!RecCase.contactid}&q2={!RecCase.accountid}&q3={!RecCase.CaseNumber}&q4={!RecCase.Id}&q5={!RecCase.ClosedBy__c}&q6={!RecCase.AssetId}&language={!RecCase.Location__r.Preferred_Language_Code__c}">Here</a></b> 以進行簡短調查。
          </apex:outputPanel>  <br/><br/>
      </apex:outputPanel>
 </td>
</tr>

<tr style="text-align:Left;" >
  <td colspan="2">
<!--<b><u>Case Information</u></b><br/>-->



<b><u><span style ="color:#0000FF;">案例資訊：</span></u></b><br/>
Status: Case Closed <br/>
<apex:outputText rendered="{!not(FSL)}">Product: {!RecCase.ProductSystem__r.name}<br/> </apex:outputText>
<apex:outputText rendered="{!FSL}">Product: {!RecCase.Asset.Name}<br/> </apex:outputText>
Created Date: {!DateToDisplayNA} <br/>
Technician: {!RecCase.Owner.Name}<br/><br/><br/>


<!--<b><u>Subject:</u></b><br/>-->
<b><u><span style ="color:#0000FF;">案例主題：</span></u></b><br/>

{!RecCase.Subject} <br/><br/>


<!--<b><u>Summary:</u></b>-->
<!--<b><u><span style ="color:#0000FF;">Summary:</span></u></b> <br/><br/>-->
<!--<apex:outputtext value="{!RecCase.Local_Case_Activity__c}" escape="false" /><br/><br/>-->

<!-- If you feel case  was closed in error, please <b><a href="{!$Label.portal_redirection_url}"><U>Click Here</u></a></b> to create a new case and reference the original case number.-->
<br/>
如果您覺得這個案例被錯誤地完結了，請回覆此電子郵件，我們將採取適當的行動。
 
<br/><br/>


<div style="text-align:left;">


</div>
  </td>
</tr>


</TABLE>
</apex:component>
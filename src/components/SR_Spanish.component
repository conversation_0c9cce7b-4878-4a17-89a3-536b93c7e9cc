<apex:component controller="SR_ComponentClass" access="global">

<apex:attribute name="Spanish" description="Case Closure Email" type="Id" required="required" assignTo="{!controllerValue}"/>
<style>
.col1{
    width:20%;
    position:relative;
}

.col2{
    width:80%;
    position:relative;
}

</style>

<table border="0" style="background-color : #FFFFFF;width:100%;font-size:13px; font-family:arial;" cellpadding="2" cellspacing ="0">
 
 <!--<tr style="text-align:center;" >
  <td colspan="2"><apex:image value="{!$Label.Varian_Partner_for_life_logo}" width="100%" />
 </td>
 </tr>-->

 <tr style="text-align:center;" >
      <td colspan="2">
      <tr><td>
      <apex:image value="{!$Label.VarianCustomerSupport}" style="width:90%;" id="LogoImage"/>
      </td>
      </tr>
      </td>
     </tr>
<!--apex:image value="{!$Label.Varian_Partner_for_life_logo}"  style="width:auto;" / background-image:url({!$Label.Varian_Partner_for_life_logo});-->
<tr style="text-align:left;">
  <td colspan="2"> 
  <!--<b>Case Number: {!RecCase.CaseNumber}</b><br/>-->
<b><span style="color: #0000FF;font-size:15px;"> Caso nº:</span> {!RecCase.CaseNumber}</b>
 <br/><br/><br/>
Apreciado/a {!RecCase.Contact.Name},   <br/><br/>
Gracias por contactar con el soporte técnico de Varian. El objeto de este correo es 
confirmar que su caso ha sido cerrado.
 <br/><br/>Su opinión es importante para nosotros. Para contribuir a mejorar nuestro servicio por
favor pulse 
    <!-- STRY0484973 Remove SVMXC Reference
      <apex:outputPanel rendered="{!not(FSL)}">  
          <apex:outputPanel rendered="{!not(Service)}">   
              <b> <a href="http://survey.clicktools.com/go?iv=3sf81tpr32x7q&q1={!RecCase.contactid}&q2={!RecCase.Contact.Email}&q3={!RecCase.accountid}&q4={!RecCase.id}&q5={!RecCase.Subject}&q6={!RecCase.ClosedDate}&q7={!RecCase.ClosedBy__r.Name}&q8={!RecCase.ClosedBy__r.Managerid}&q9={!RecCase.ProductSystem__r.SVMXC__Product__r.Name}&language={!RecCase.Contact.Language_ISOCode__c}">aquí </a></b> para completar una pequeña encuesta.
          </apex:outputPanel>
          <apex:outputPanel rendered="{!Service}">
              <b> <a href="http://survey.clicktools.com/go?iv=c2x291q4bryw&q1={!RecCase.contactid}&q2={!RecCase.accountid}&q3={!RecCase.CaseNumber}&q4={!RecCase.Id}&q5={!RecCase.ClosedBy__c}&q6={!RecCase.ProductSystem__c}&language={!RecCase.SVMXC__Site__r.ERP_Default_Preferred_Language_Code__c}">aquí</a></b> para completar una pequeña encuesta.
          </apex:outputPanel>  <br/><br/> 
      </apex:outputPanel>
      -->
      <apex:outputPanel rendered="{!FSL}">
          <apex:outputPanel rendered="{!not(Service)}">   
              <b> <a href="http://survey.clicktools.com/go?iv=3sf81tpr32x7q&q1={!RecCase.contactid}&q2={!RecCase.Contact.Email}&q3={!RecCase.accountid}&q4={!RecCase.id}&q5={!RecCase.Subject}&q6={!RecCase.ClosedDate}&q7={!RecCase.ClosedBy__r.Name}&q8={!RecCase.ClosedBy__r.Managerid}&q9={!RecCase.Asset.Product_Name__c}&language={!RecCase.Contact.Language_ISOCode__c}">aquí </a></b> para completar una pequeña encuesta.
          </apex:outputPanel>
          <apex:outputPanel rendered="{!Service}">
              <b> <a href="http://survey.clicktools.com/go?iv=c2x291q4bryw&q1={!RecCase.contactid}&q2={!RecCase.accountid}&q3={!RecCase.CaseNumber}&q4={!RecCase.Id}&q5={!RecCase.ClosedBy__c}&q6={!RecCase.AssetId}&language={!RecCase.Location__r.Preferred_Language_Code__c}">aquí</a></b> para completar una pequeña encuesta.
          </apex:outputPanel>  <br/><br/> 
      </apex:outputPanel>
 </td>
</tr>

<tr style="text-align:Left;" >
  <td colspan="2">
<!--<b><u>Case Information</u></b><br/>-->







<b><u><span style ="color:#0000FF;">Información del Caso:</span></u></b><br/>
Estado: Caso Cerrado <br/>
<apex:outputText rendered="{!not(FSL)}">Producto: {!RecCase.ProductSystem__r.name}<br/> </apex:outputText>
<apex:outputText rendered="{!FSL}">Producto: {!RecCase.Asset.Name}<br/> </apex:outputText>  
Fecha de apertura: {!DateToDisplay} <br/>
Agente: {!RecCase.Owner.Name}<br/><br/><br/>












<!--<b><u>Subject:</u></b><br/>-->
<b><u><span style ="color:#0000FF;">Asunto:</span></u></b><br/>

{!RecCase.Subject} <br/><br/>


<!--<b><u>Summary:</u></b>-->
<b><u><span style ="color:#0000FF;">Comentarios:</span></u></b> <br/><br/>
<apex:outputtext value="{!RecCase.Local_Case_Activity__c}" escape="false" /><br/><br/>

Si usted considera que este caso no debe cerrarse  y tiene una cuenta en MyVarian, por 
favor pulse 
<b><a href="{!$Label.portal_redirection_url}"><U>aquí </u></a></b> para crear un nuevo caso en referencia al original.
<br/>
Si no tiene una cuenta en MyVarian, por favor responda directamente a este email y realizaremos el seguimiento adecuado.
 
<br/><br/>







<div style="text-align:left;"><b>Nota:</b>Previa autorización, un representante de soporte técnico de Varian podría acceder a su sistema usando la herramienta de acceso remoto SmartConnect® para ayudar en el proceso de diagnóstico y resolución. En caso de que este acceso se produjese fuera de su horario de trabajo, le enviaremos un resumen de las acciones realizadas 


</div>
  </td>
</tr>


</TABLE>
</apex:component>
<!--
/*************************************************************************\
@ Author        : <PERSON><PERSON><PERSON>
@ Date          :  20-June-2019
@ Description   : Page defined to handle logic to display workorders of given site
*   User has option to select work orders , attachment and hence email the selected one

****************************************************************************/-->

<apex:page controller="VFSL_SiteWiseWorkOrderController" >
    <apex:includeScript value="//cdnjs.cloudflare.com/ajax/libs/jquery/3.1.1/jquery.min.js" />
    <apex:stylesheet value="//cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.28.4/css/theme.blue.min.css" />
    <apex:stylesheet value="{!URLFOR($Resource.SLDS090, 'assets/styles/salesforce-lightning-design-system-vf.css')}" /> 
    <!-- STSK0012811 Start -->
    <apex:includeScript value="{!URLFOR($Resource.Datatable_scripts, '/DataTables-1.10.16/js/jquery.dataTables.min.js')}" /> 
    <apex:stylesheet value="{!URLFOR($Resource.Datatable_scripts, '/DataTables-1.10.16/css/jquery.dataTables.min.css')}" /> 
    <apex:includeScript value="{!URLFOR($Resource.Datatable_scripts, '/Select-1.2.3/js/dataTables.select.min.js')}" /> 
    <apex:stylesheet value="{!URLFOR($Resource.Datatable_scripts, '/Select-1.2.3/css/select.dataTables.min.css')}" /> 
    <apex:includeScript value="{!URLFOR($Resource.Datatable_scripts, '/Buttons-1.4.2/js/dataTables.buttons.min.js')}" /> 
    <apex:stylesheet value="{!URLFOR($Resource.Datatable_scripts, '/Buttons-1.4.2/css/buttons.dataTables.min.css')}" /> 
    <!-- STSK0012811 End -->
    
    
    <style>
        .message{
        margin: 0px;
        }
        .spinnerBg{
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: #000;
        opacity: 0.2;
        z-index: 999999;
        }
        .spinner{
        width: 100%;
        height: 100%;
        position: absolute;
        background-image: url("/img/loading32.gif");
        background-size: 16px;
        background-repeat: no-repeat;
        background-attachment: fixed;
        background-position: center;
        z-index: 9999999;
        opacity: 1;
        }  
        .instructions{
        font-style: italic;
        }
        
        
    </style>
    
    <script>
    function searchWorkOrder() {
        loadWOMethod();
        return true;
    }
    
    function showAttachment()
    {
        showAttachmentMethod();
        return true;
        
    }
    function mergeMail()
    {
        mailSetup();
        return true;
    }
    function mail()
    {
        mailAttachment();
        return true;
    }
    
    function resetInput()
    {
        document.getElementById("{!$Component.siteWiseForm.TopLevelID}").value='';
        document.getElementById("{!$Component.siteWiseForm.ComponentID}").value='';
        document.getElementById("{!$Component.siteWiseForm.CaseNoID}").value='';
        document.getElementById("{!$Component.siteWiseForm.TechID}").value='';
        document.getElementById("{!$Component.siteWiseForm.ERPID}").value='';
        document.getElementById("{!$Component.siteWiseForm.TeamID}").value='';
        document.getElementById("{!$Component.siteWiseForm.AccountID}").value='';
        document.getElementById("{!$Component.siteWiseForm.StatusID}").value='';
        return true;
        
    }
    //<!-- STSK0012811 Start --> 
    $(document).ready(function() {
        var table = $('#workorders').DataTable({"ordering":[[ 3, "desc" ]],"searching":false,"paging":false,"fixedHeader":true,"scrollY":"200px","scrollCollapse": true,
                                                dom: 'Bfrtip',
                                                buttons: [
                                                    {
                                                        text: 'Select All',
                                                        action: function ( e, dt, node, config ) {
                                                            var chkBox=$('[id*=woCheckBox]');
                                                            for(var i=0; i<chkBox.length; i++)
                                                            {
                                                                chkBox[i].checked=true;  
                                                            }
                                                        }
                                                    },
                                                    {
                                                        text: 'Unselect All',
                                                        action: function ( e, dt, node, config ) {
                                                            var chkBox=$('[id*=woCheckBox]');
                                                            for(var i=0; i<chkBox.length; i++)
                                                            {
                                                                chkBox[i].checked=false;  
                                                            }
                                                        }
                                                    }
                                                ]
                                               });
    } ); 
    $(document).ready(function() {
        $('#attachments').DataTable({"ordering":[[ 3, "desc" ]],"searching":false,"paging":false,"fixedHeader":true,"scrollY":"200px","scrollCollapse": true,
                                     dom: 'Bfrtip',
                                     buttons: [
                                         {
                                             text: 'Select All',
                                             action: function ( e, dt, node, config ) {
                                                 var chkBox=$('[id*=atchCheckBox]');
                                                 for(var i=0; i<chkBox.length; i++)
                                                 {
                                                     chkBox[i].checked=true;  
                                                 }
                                             }
                                         },
                                         {
                                             text: 'Unselect All',
                                             action: function ( e, dt, node, config ) {
                                                 var chkBox=$('[id*=atchCheckBox]');
                                                 for(var i=0; i<chkBox.length; i++)
                                                 {
                                                     chkBox[i].checked=false;  
                                                 }
                                             }
                                         }
                                     ]});
     } ); 
         $(document).ready(function() {
         $('#content').DataTable({"ordering":[[ 3, "desc" ]],"searching":false,"paging":false,"fixedHeader":true,"scrollY":"200px","scrollCollapse": true,dom: 'Bfrtip', buttons:[]});
    } ); 
    $(document).ready(function() {
        $('#contacts').DataTable({"ordering":[[ 3, "desc" ]],"searching":false,"paging":false,"fixedHeader":true,"scrollY":"200px","scrollCollapse": true,
                                  dom: 'Bfrtip',
                                  buttons: [
                                      {
                                          text: 'Select All',
                                          action: function ( e, dt, node, config ) {
                                              var chkBox=$('[id*=contactCheckBox]');
                                              for(var i=0; i<chkBox.length; i++)
                                              {
                                                  chkBox[i].checked=true;  
                                              }
                                          }
                                      },
                                      
                                      {
                                          text: 'Unselect All',
                                          action: function ( e, dt, node, config ) {
                                              var chkBox=$('[id*=contactCheckBox]');
                                              for(var i=0; i<chkBox.length; i++)
                                              {
                                                  chkBox[i].checked=false;  
                                              }
                                          }
                                          
                                      }
                                  ]});
    } ); 
    
    $(document).ready(function() {
        $('#contactRole').DataTable({"ordering":[[ 3, "desc" ]],"searching":false,"paging":false,"fixedHeader":true,"scrollY":"120px","scrollCollapse": true,
                                  dom: 'Bfrtip',
                                  buttons: [
                                      {
                                          text: 'Select All',
                                          action: function ( e, dt, node, config ) {
                                              var chkBox=$('[id*=contactRoleCheckBox]');
                                              for(var i=0; i<chkBox.length; i++)
                                              {
                                                  chkBox[i].checked=true;  
                                              }
                                          }
                                      },
                                      
                                      {
                                          text: 'Unselect All',
                                          action: function ( e, dt, node, config ) {
                                              var chkBox=$('[id*=contactRoleCheckBox]');
                                              for(var i=0; i<chkBox.length; i++)
                                              {
                                                  chkBox[i].checked=false;  
                                              }
                                          }
                                          
                                      }
                                  ]});
    } ); 
    
    // <!-- STSK0012811 End -->  
    </script>
    
    <apex:outputPanel >
        <apex:actionStatus id="spinnerStatus">
            <apex:facet name="start">
                <div class="spinnerBg" />
                <div class="spinner" />
            </apex:facet>
        </apex:actionStatus>
        <div>
            <apex:pageMessages id="pageMessages" ></apex:pageMessages>  
        </div>
        <div class="slds">
            <div class="slds-page-header">
                <div class="slds-media">
                    <div class="slds-media__figure">
                        <span class="slds-icon_container slds-icon-standard-opportunity" >
                            <c:svg styleClass="slds-icon slds-icon--large slds-icon-standard-opportunity" path="assets/icons/standard-sprite/svg/symbols.svg#account" />
                        </span>
                    </div> 
                    <div class="slds-media__body">
                        <h1 class="slds-page-header__title slds-truncate slds-align-middle"><font size="4"><strong>Attachments Search</strong></font></h1>
                        <p class="slds-text-body_small slds-line-height_reset">Search, Compile and Send Attachments</p>
                    </div>  
                </div>
            </div>
        </div>
    </apex:outputPanel>
    <apex:form id="siteWiseForm" >
        <apex:actionFunction action="{!mergeAtchmentsAndMail}" name="mailSetup"  reRender="pageMessages"  status="spinnerStatus"/>
        <apex:actionFunction action="{!back}" name="back"/>
        <apex:outputPanel >
            <div class="slds">
                <div id="searchSection">
                    <div class="slds-section slds-is-open" style="margin-top: 20px;">
                        <h3 class="slds-section__title slds-theme--shade">
                            <div style="cursor:pointer">
                                <font size="3"><strong>Search for Work Orders</strong></font>
                                <p class="instructions">Input One or More Search Criteria and Click GO.  More Search Criteria Improve Results.</p>
                            </div>
                        </h3>
                        
                        <div class="slds-section__content"  style="margin-top :10px;height: 130px; display: block;">
                            <table class="slds-table slds-table--bordered slds-table--cell-buffer">
                                <thead> 
                                    <tr class="slds-text-title--caps">
                                        <th scope="col"><strong>Location</strong></th>
                                        <th scope="col"><strong>Top Level</strong></th>
                                        <th scope="col"><strong>Component</strong></th>
                                        <th scope="col"><strong>Case</strong></th>
                                        <th scope="col"><strong>Team</strong></th>
                                        <th scope="col"><strong>Service Resource</strong></th>
                                        <th scope="col"><strong>Priority</strong></th>
                                        <th scope="col"><strong>Start Date From</strong></th>
                                        <th scope="col"><strong>Start Date To</strong></th>
                                        <th scope="col"><strong>Account</strong></th>
                                        <th scope="col"><strong>Status</strong></th>
                                    </tr>
                                </thead>
                                <tbody>                        
                                    <tr>
                                        <td scope="col">
                                            <apex:inputText id="siteNameID"  value="{!siteName}" /> 
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="TopLevelID"  value="{!searchByTopLevel}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="ComponentID"   value="{!searchByCompLevel}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="CaseNoID"  value="{!searchByCaseNo}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="TeamID"  value="{!searchByTeam}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="TechID"  value="{!searchByTech}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:selectList id="ERPID" value="{!searchByERP}" style="height:50px">
                                                <apex:selectOptions value="{!selOptERP}"></apex:selectOptions>
                                            </apex:selectList>
                                        </td>
                                        <td scope="col"> 
                                            <apex:inputField id="FromDateID" value="{!searchFromDate.StartDate}" showDatePicker="true" />
                                        </td>
                                        <td scope="col"> 
                                            <apex:inputField id="ToDateID" value="{!searchToDate.StartDate}" showDatePicker="true"/>
                                        </td>
                                        <td scope="col">
                                            <apex:inputText id="AccountID" value="{!searchByAccount}"/>
                                        </td>
                                        <td scope="col">
                                            <apex:selectList id="StatusID" value="{!searchByStatus}" style="height:50px">
                                                <apex:selectOptions value="{!selOptStatus}"></apex:selectOptions>
                                            </apex:selectList>
                                        </td>
                                    </tr>
                                    
                                    <tr>
                                        <td scope="col" colspan="8" align="center">
                                            <apex:actionFunction name="loadWOMethod" action="{!loadWorkOrdersOnSearch}" />
                                            <apex:commandButton onclick="searchWorkOrder();return false;" value="GO" style="width:300px; font-weight: bold;"/>
                                                                                    </td>
                                        <td scope="col" colspan="8" align="center">
                                            <apex:commandButton onclick="resetInput();return false;" value="Clear" style="width:300px; font-weight: bold;"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div> 
                <div class="slds-section slds-is-open" style="margin-top: 20px;">
                    <h3 class="slds-section__title slds-theme--shade">
                        <div style="cursor:pointer">
                            <font size="3"><strong>Work Order Results</strong></font>
                            <p class="instructions">Select Work Orders with Desired Attachments/Content and Click GO.</p>
                        </div>
                    </h3>
                </div>
                <div Id="ResultSection" style="margin-top: 5px; display:{!if(showWorkOrderSection,'','none')};" >
                    <div class="slds-section__content"  style=" display: block;">
                        <apex:pageBlock id="table1">
                        <table id="workorders" class="datatable slds-table slds-table--bordered slds-table--cell-buffer">
                            <thead > 
                                <tr class="slds-text-title--caps">
                                    <th scope="col">
                                        <strong>Select</strong></th>
                                    <th  scope="col">
                                        <strong>Work Order</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>File Count</strong>
                                    </th> 
                                    <th  scope="col">
                                        <strong>Account</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Top Level</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Record Type</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Status</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Scheduled Start</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Purpose Of Visit</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Machine Release</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Service Resource</strong>
                                    </th>
                                    <th scope="col">
                                        <strong>Team (Equip)</strong>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="workorderresult" class="scrollContent">
                                <apex:repeat value="{!tempListWrapper}"  var="wo">
                                    <tr>
                                        <th scope="col">
                                            <apex:inputCheckBox value="{!wo.isSelected}" id="woCheckBox"/>
                                        </th>
                                        <td data-label="Name"> 
                                            
                                            <!--<a href=" /{!wo.objWO.Id}" target="_blank" rendered="{!wo.objWO != null}">   {!wo.objWO.WorkOrderNumber}</a>
                                            <a href=" /{!wo.objWoArchieve.Id}" target="_blank" rendered="{!wo.objWoArchieve != null}"> {!wo.objWoArchieve.Name}</a> -->
                                            <apex:outputlink value="/{!wo.objWO.Id}" target="_blank">{!wo.objWO.WorkOrderNumber}</apex:outputlink>
                                            <apex:outputlink value="/{!wo.objWoArchieve.Id}" target="_blank">{!wo.objWoArchieve.Name}</apex:outputlink>
                                        </td>
                                        <td data-label="AttachmentCount">                                             
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Count_of_Attachments__c}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.Count_Attachments__c}</apex:outputpanel>
                                        </td>  
                                        <td data-label="Site"> 
                                            {!wo.accName} 
                                        </td>
                                        <td data-label="Top Level"> 
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Asset.name}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.HeaderEquipmentName__c}</apex:outputpanel>                                            
                                        </td>
                                        <td data-label="Record Type">  
                                            {!wo.objWO.recordType.name}
                                        </td>
                                        <td data-label="Status">  
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Status}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.Status__c}</apex:outputpanel>                                                                                        
                                        </td>
                                        <td data-label="ScheduleDate"> 
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.StartDate}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.Preferred_Start_Date__c}</apex:outputpanel>                                            
                                        </td>
                                        <td data-label="VisitPurpose">  
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Purpose_of_Visit__c}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.Purpose_of_Visit__c}</apex:outputpanel>                                            
                                        </td>
                                        <td data-label="MachineReleaseDate">  
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Machine_Release__c}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.Customer_Machine_Release__c}</apex:outputpanel>                                            
                                        </td>
                                        <td data-label="Technician">  
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.Service_Resource__r.name}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.TechnicianName__c}</apex:outputpanel>                                            
                                        </td>
                                        <td data-label="Team (Equip)">
                                            <apex:outputpanel rendered="{!wo.objWO != null}">{!wo.objWO.ServiceTerritory.Group_Code__c}</apex:outputpanel>
                                            <apex:outputpanel rendered="{!wo.objWoArchieve != null}">{!wo.objWoArchieve.ServiceTeamName__c}</apex:outputpanel>                                            
                                        </td>
                                    </tr>
                                </apex:repeat> 
                            </tbody>
                        </table> 
                        <div align="center">
                            <apex:commandButton value="Previous" rerender="table1" action="{!Previous}" disabled="{!prev}"/>
                    		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <apex:commandButton value="Next" action="{!Next}" disabled="{!nxt}" rerender="table1"/>
                        </div>
                        </apex:pageBlock>
                    </div>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<apex:actionFunction name="showAttachmentMethod" action="{!showAtchmntANDContactOfWorkOrder}" />
                    <apex:commandButton onclick="showAttachment();return false;" value="GO" style="width: 300px; font-weight: bold;"/>
                    
                </div>
                <h3 class="slds-section__title slds-theme--shade">
                    <div style="cursor:pointer;margin-top: 20px;">
                        <font size="3"><strong>Files of  Selected Work Orders</strong> </font>
                        <p class="instructions">Select Attachments/Content to Combine and/or Email</p>
                    </div>
                </h3>
                <div class="slds-section slds-is-open" style="margin-top: 5px; display:{!if(showAttachmentSection,'','none')};">
                    <div class="slds-section__content" style="display: block;">
                        <table id="attachments" class="display slds-table slds-table--bordered slds-table--cell-buffer" cellspacing="0" >
                            <thead>
                                <tr class="slds-text-title--caps">
                                    <th scope="col">
                                        <strong>Select</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Title/Name</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Top Level</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Work Order</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Last Modified By</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Last Modified Date</strong> 
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <apex:repeat value="{!listAttachmentWrapper}"  var="aw">
                                    <tr>
                                        <th scope="col">
                                            <apex:inputCheckBox value="{!aw.isSelected}" id="atchCheckBox"/>
                                        </th>
                                        <td scope="row" data-label="Name">
                                            <a href="/servlet/servlet.FileDownload?file={!aw.objAtch.Id}" target="_blank">{!aw.objAtch.Name}</a>
                                        </td>
                                        <td scope ="row" data-label ="Top Level">
                                         {!aw.assetName}
                                        </td>
                                        <td scope="row" data-label="Work Order">
                                            {!aw.woNo}
                                        </td>
                                        <td scope="row" data-label="Last Modified Date">
                                            {!aw.objAtch.LastModifiedDate}
                                        </td>
                                    </tr>
                                </apex:repeat>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="slds-section slds-is-open" style="margin-top: 5px; display:{!if(showContentSection,'','none')};">
                    <div class="slds-section__content" style="display: block;">
                        <table id="content" class="display slds-table slds-table--bordered slds-table--cell-buffer" cellspacing="0" >
                            <thead>
                                <tr class="slds-text-title--caps">
                                    <th scope="col">
                                        <strong>Select</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Title/Name</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Work Order</strong> 
                                    </th>
                                    <th scope="col">
                                        <strong>Last Modified</strong> 
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <apex:repeat value="{!listContentWrapper}"  var="cont">
                                    <tr>
                                        <th scope="col">
                                            <apex:inputCheckBox value="{!cont.isSelected}" id="atchCheckBox"/>
                                        </th>
                                        <td scope="row" data-label="Name">
                                            <a href="/sfc/servlet.shepherd/version/download/{!cont.contentObj.Id}" target="_blank">{!cont.contentObj.Title}</a>
                                        </td>
                                        <td scope="row" data-label="Work Order">
                                            {!cont.woNo}
                                        </td>
                                        <td data-label="Last Modified Date">  
                                            {!cont.contentObj.ContentModifiedDate}
                                        </td>
                                    </tr>
                                </apex:repeat>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div id="contactsID">
                    <div class="slds-section slds-is-open" style="margin-top: 20px; display:{!if(showContactAndMailSection,'','none')};">
                        <h3 class="slds-section__title slds-theme--shade">
                            <div style="cursor:pointer">
                                <font size="3"><strong>Contacts for Account</strong></font>
                            </div>
                        </h3>
                        <div class="slds-section__content"  style="margin-top: 10px; display: block;">
                            <table id="contacts" class="slds-table slds-table--bordered slds-table--cell-buffer">
                                <thead>
                                    <tr class="slds-text-title--caps">
                                        <th scope="col">
                                            <strong>Select</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Name</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Role</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Email</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <apex:repeat value="{!listContactWrapper}"  var="ac">
                                        <tr>
                                            <th scope="col">
                                                <apex:inputCheckBox value="{!ac.isSelected}" id="contactCheckBox"/>
                                            </th>
                                            <td scope="row" data-label="Name">
                                                <a href="/{!ac.objCnt.Id}" target="_blank">{!ac.objCnt.Name}</a>
                                            </td>
                                            <td data-label="EMail">  
                                                {!ac.objCnt.Functional_Role__c}
                                            </td>
                                            <td data-label="EMail">  
                                                {!ac.objCnt.Email}
                                            </td>
                                        </tr>
                                    </apex:repeat>
                                </tbody>
                            </table>
                        </div>
                        <div class="slds-section slds-is-open" style="margin-top: 20px; display:{!if(showContactAndMailSection,'','none')};">
                        <h3 class="slds-section__title slds-theme--shade">
                            <div style="cursor:pointer">
                                <font size="3"><strong>Contact Role Associations for Account</strong></font>
                            </div>
                        </h3>
                        <div class="slds-section__content"  style="margin-top: 10px; display: block;">
                            <table id="contactRole" class="slds-table slds-table--bordered slds-table--cell-buffer">
                                <thead>
                                    <tr class="slds-text-title--caps">
                                        <th scope="col">
                                            <strong>Select</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Name</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Role</strong>
                                        </th>
                                        <th scope="col">
                                            <strong>Email</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <apex:repeat value="{!listContactRoleWrapper}"  var="cra">
                                        <tr>
                                            <th scope="col">
                                                <apex:inputCheckBox value="{!cra.isSelected}" id="contactRoleCheckBox"/>
                                            </th>
                                            <td scope="row" data-label="Name">
                                                <a href="/{!cra.objContRole.Id}" target="_blank">{!cra.objContRole.Contact__r.Name}</a>
                                            </td>
                                            <td data-label="EMail">  
                                                {!cra.objContRole.Functional_Role__c}
                                            </td>
                                            <td data-label="EMail">  
                                                {!cra.objContRole.Contact_Email__c}
                                            </td>
                                        </tr>
                                    </apex:repeat>
                                </tbody>
                            </table>
                        </div>
                        </div>
                        <div class="slds-section slds-is-open" style="margin-top: 20px;">
                            <h3 class="slds-section__title slds-theme--shade">
                                <div style="cursor:pointer">
                                    <font size="3"><strong>Additional Mail Recipients </strong></font>
                                    <!-- STSK0012811 Start -->
                                    <p class="instructions">Enter semicolon (;) separated Email Addresses</p>
                                    <!-- STSK0012811 End -->
                                </div>
                            </h3>
                            <div class="slds-section__content" style="margin-top: 10px;display: block;">
                                <table class="slds-table slds-table--bordered slds-table--cell-buffer">
                                    <tbody>
                                        <tr>
                                            <th scope="col">
                                                <apex:inputTextarea id="mailId" style="height:30pt;width:420pt"  value="{!addMailRecepients}" required="false"/>  
                                            </th>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="slds-section slds-is-close" style="display: block;margin-top: 20px;">
                                <h3 class="slds-section__title slds-theme--shade">
                                    <div style="cursor:pointer">
                                        <font size="3"><strong>Email Subject</strong></font>
                                    </div>
                                </h3>
                                <div class="slds-section__content" style="display: block;margin-top: 10px;">
                                    <apex:inputTextArea value="{!emailSub}" id="eSubID" style="height:20pt;width:420pt"  />
                                </div>
                            </div>
                            <div class="slds-section slds-is-close" style="display: block;margin-top: 20px;">
                                <h3 class="slds-section__title slds-theme--shade">
                                    <div style="cursor:pointer">
                                        <font size="3"> <strong>Email Body</strong> - Add only your salutation to end of email body.  Your name and rest of Varian signature will default on email.</font>
                                    </div>
                                </h3>
                                <div class="slds-section__content" style="display: block;margin-top: 10px;">
                                    <!-- STSK0012811 Start -->
                                    <table style="width:100%;">
                                        <tr>
                                            <td>
                                                <apex:inputTextarea richtext="true" value="{!emailBody}" id="BodyID"  rows="8"/>
                                            </td>
                                        </tr>
                                    </table>
                                    <!-- STSK0012811 End -->
                                </div>
                            </div>
                            <div class="slds-section slds-is-close" style="display: block;margin-top: 20px;">
                                <h3 class="slds-section__title slds-theme--shade">
                                    <div style="cursor:pointer">
                                        <font size="3"><strong>Include Your Email in Carbon Copy?</strong></font>
                                    </div>
                                </h3>
                                <div class="slds-section__content" style="display: block;margin-top: 10px;">
                                    <apex:inputCheckbox selected="true" value="{!emailCC}" id="emailCC"/>
                                </div>
                            </div>
                        </div>
                    </div>  
                </div>    
                <div id="bottombtn" class="slds-col slds-no-flex slds-align-middle" style="margin-top:20px;">  
                    <!--<apex:actionFunction action="{!mergeAtchmentsAndMail}" name="mailSetup"  reRender="pageMessages"  status="spinnerStatus"/>-->
                    <!--<apex:commandButton onclick="mergeMail();return false;" value="Email With Combined Attachments" style="width: 300px; font-weight: bold;display:{!if(showContactAndMailSection,'','none')};" disabled="{!NOT(showButton)}"/> -->   
                    <apex:actionFunction action="{!attachmentsMail}" name="mailAttachment"  reRender="pageMessages"  status="spinnerStatus"/>
                    <apex:commandButton onclick="mail();return false;" value="Email with Individual Attachments" style="width: 300px; font-weight: bold;display:{!if(showContactAndMailSection,'','none')};" disabled="{!NOT(showButton)}"/>
                    
                    <!--<apex:commandlink action="{!mergeAndDownloadAttachments}" target="_blank">
                        <apex:commandButton value="Download Attachments" style="width: 300px; color: black; font-weight: bold; display:{!if(showContactAndMailSection,'','none')};"/>
                    </apex:commandLink>-->
                    
                    <apex:actionFunction action="{!back}" name="back"/> 
                    <apex:commandButton onclick="back();return false;" value="Back" style="width: 300px; font-weight: bold;"/> 
                </div>
            </div> 
        </apex:outputPanel>
    </apex:form>
</apex:page>
<apex:page action="{!trackingAndRedirect}" controller="CustomLinkTrackingController" sidebar="true" showHeader="true">
    <script type="text/javascript">
        window.onload=function()
        { 
            
            if('{!$CurrentPage.parameters.app}' == 'MHD')
            {
                if(typeof sforce != 'undefined')
                {
                    location.assign('{!MHDLightningLink}');
                }
                else
                {
                    location.assign('{!MHDLink}')
                }
            }
            if('{!$CurrentPage.parameters.app}' == 'PSE')
            {
                location.assign('{!PSELink}')
            }
        }
    </script>
</apex:page>
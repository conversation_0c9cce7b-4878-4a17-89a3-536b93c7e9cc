<apex:page showChat="false" showHeader="false" standardStylesheets="false" >
    <apex:outputPanel >
    <apex:includeLightning />
    <div id="lightning" />
    <script>       
            $Lightning.use("c:LightningOutApp", function() {
              $Lightning.createComponent("c:MvVarianUniteLoginComponent",
              { "recordId" : '',
                "accountId" : ''},
              "lightning",
              function(cmp) {
                // do some stuff
              });
            });
        
    </script>
    </apex:outputPanel>
    
</apex:page>
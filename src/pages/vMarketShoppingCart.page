<apex:page sidebar="false" showHeader="false"
    standardStylesheets="false" controller="vMarketCartItem"
    id="shoppingCartPage">

    <c:vMarketCartHeader />
    <style type="text/css">
        /* Page content styling */
        .cartDetailsWrapper .titleText {
            color: #304a61;
            font-size: 20px;
        }
        
        #cartTable .appInfoWrap {
            line-height: 1;
        }
        
        #cartTable .appInfoWrap .appLogo {
            margin-right: 15px;
        }
        
        #cartTable .appInfoWrap .appName {
            font-size: 24px;
            color: #304a61;
            margin-bottom: 5px;
            cursor: pointer;
            display: block;
            text-decoration: none;
        }
        
        #cartTable .appInfoWrap .appDevName {
            font-size: 14px;
            color: #666666;
            margin-bottom: 8px;
        }
        
        #cartTable .appInfoWrap .reviewCnt {
            font-size: 16px;
            color: #666666;
        }
        
        #cartTable .appInfoWrap .removeAppLink {
            color: #e73303;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 8px;
            display: inline-block;
        }
        
        #cartTable {
            margin-top: 20px;
            border: 1px solid #ededed;
        }
        
        #cartTable thead {
            background: #f3f3f3;
        }
        
        #cartTable thead tr th {
            border-bottom: 0px;
            color: #666666;
            font-size: 14px;
            font-weight: normal;
        }
        
        #cartTable thead tr th:first-child {
            padding-left: 15px;
        }
        
        #cartTable thead tr th:last-child {
            text-align: right;
            padding-right: 20px;
        }
        
        #cartTable tbody tr td {
            border-top: 0px;
            border-bottom: 1px solid #ededed;
            padding-top: 15px;
            padding-bottom: 15px;
        }
        
        #cartTable tbody tr td:first-child {
            padding-left: 15px;
        }
        
        #cartTable tbody tr td:last-child {
            text-align: right;
            color: #000;
            font-size: 18px;
            padding-right: 20px;
        }
        
        .cartBtnWrapper {
            text-align: right;
        }
        
        .cartBtnWrapper .continueShoppingLink {
            background-color: #f4f5f9;
            box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            -moz-box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            -webkit-box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            width: 250px;
            height: 38px;
            display: inline-block;
            color: #304a61;
            text-align: center;
            padding-top: 8px;
            cursor: pointer;
            text-decoration: none;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .cartBtnWrapper .proceedCheckoutLink {
            background-color: #178eea;
            box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            -moz-box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            -webkit-box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.2);
            width: 250px;
            height: 38px;
            display: inline-block;
            color: #fff;
            text-align: center;
            cursor: pointer;
            text-decoration: none;
            font-weight: 600;
            text-transform: uppercase;
            border: 0;
        }
        
        .cartBtnWrapper .orText {
            display: inline-block;
            margin: 0 20px;
        }
        
        .priceDetailsWrapper {
            
        }
        
        .priceDetailsWrapper .titleText {
            color: #304a61;
            font-size: 20px;
            text-align: right;
        }
        
        #priceDetailsTable {
            margin-top: 20px;
            border: 1px solid #ededed;
        }
        
        #priceDetailsTable tbody tr td {
            
        }
        
        #priceDetailsTable tbody tr td:first-child {
            text-align: left;
            color: #000;
            font-size: 16px;
        }
        
        #priceDetailsTable tbody tr td:last-child {
            text-align: right;
            color: #304a61;
            font-size: 18px;
            font-weight: 600;
        }
        .MessageModal {
                text-align: center;
            }           
            .MessageModal .modal-dialog {
                width: 400px;
            }
            
            .MessageModal .mainMessageText {
                color: #304a61;
                font-size: 18px;
                font-weight: bold;
            }
            
            .MessageModal .okBtn {
                background-color: #178eea;
                width: 150px;
                color: #fff;
                border-radius: 0;
            }
            .SubmitButton {
                    border: 0;
                    outline: none;
                    width: 117px;
                    height: 43px;
                    background-color: #178eea;
                    font-size: 16px;
                    font-weight: 600;
                    text-align: center;
                    color: #ffffff;
                }
            .MessageModalIframe {
                text-align: center;
            }           
                .MessageModalIframe .modal-dialog {
                width: 70%;
                }
            
            .MessageModalIframe .mainMessageText {
                color: #304a61;
                font-size: 18px;
                font-weight: bold;
            }
            
            .MessageModalIframe .okBtn {
                background-color: #178eea;
                width: 150px;
                color: #fff;
                border-radius: 0;
            }
             
        /* Page content styling end */
    </style>

    <apex:outputPanel id="renderCartItems" layout="block">
        <div id="content-wrapper">
            <div class="container-fluid page-top-section">
                <div class="container">
                    <h3 class="pageTitle pull-left">ORDER INFO</h3>
                   <!-- <h4 align="right">
                        <apex:outputText value="{!breadcrumb}" escape="false"> </apex:outputText>
                    </h4> -->
                    <div class="breadCrumbWrapper pull-right"> <c:vMarketBreadCrumb /></div>
                </div>
            </div>
            <apex:form rendered="{!(CartItemLineList!=null)}">
                <div class="container page-content-section">
                    <div class="row">
                        <div class="col-md-8 cartDetailsWrapper">
                            <div class="titleText">Shopping Cart ({!NumberItems} items)</div>
                            <div class="table-responsive">
                                <table class="table" id="cartTable">
                                    <thead>
                                        <tr>
                                            <th>Item Details</th>
                                            <th>Price</th>
                                        </tr>
                                    </thead>
                                    <tboby> <apex:repeat value="{!CartItemLineList}"
                                        var="app" rendered="{!(CartItemLineList!=null)}">
                                        <tr>
                                            <td>
                                                <div class="appInfoWrap">
                                                    <div class="appLogo pull-left">
                                                        <a href="/vMarketAppDetails?appId={!app.appid}"> <apex:image url="{!app.LogoUrl}" width="68px"
                                                                styleClass="cartProdImg" />
                                                        </a>
                                                    </div>
                                                    <div class="pull-left">
                                                        <a href="/vMarketAppDetails?appId={!app.appid}"
                                                            class="appName">{!app.Name}</a>
                                                        <div class="appDevName">{!app.OwnerName}</div>
                                                        <div>
                                                            <div>
                                                                <span class="stars-container stars-{!app.RatingText}"
                                                                    data-toggle="tooltip" style="font-size: 1em;"
                                                                    title="{!app.RatingObj.ScaledRating}">★★★★★</span>

                                                            </div>
                                                            <div>
                                                                 <apex:outputPanel rendered="{!AND(Authenticated, (app.EULAAttachedId!=null))}">
                                                                 This Purchase requires your agreement to the terms of the <a href="javascript:void(0)" onClick="eulaModal('{!app.EULAAttachedId}')"> Developer EULA.</a>    
                                                                 </apex:outputPanel>
                                                            </div>
                                                            <a href="javascript:void(0);" title="Remove"
                                                                class="removeAppLink removeCartLineItem"
                                                                id="{!app.AppId}">Remove</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><span class="reviewCnt">{!app.CurrencyIsoCode}&nbsp;
                                                    <apex:outputText label="NRR" value="{0,number}">
                                                        <apex:param value="{!app.Price}" />
                                                    </apex:outputText>
                                            </span></td>
                                        </tr>
                                    </apex:repeat> </tboby>
                                </table>
                            </div>
                            <apex:outputPanel styleClass="cartBtnWrapper"
                                rendered="{!Authenticated}" layout="block">
                                <a class="continueShoppingLink" href="/vMarketCatalogue">Continue
                                    Shopping</a>
                                <span class="orText">or</span>
                                <apex:commandButton onclick="return validateShippingAddress();"
                                    action="{!createOrder}" styleClass="proceedCheckoutLink"
                                    value="Proceed to Checkout"></apex:commandButton>
                            </apex:outputPanel>
                            <apex:outputPanel styleClass="cartBtnWrapper"
                                rendered="{!NOT(Authenticated)}" layout="block">
                                <a id="loginLinkOnPage" class="continueShoppingLink">Login to
                                    Checkout</a>
                            </apex:outputPanel>
                        </div>
                        <div class="col-md-4">
                            <div class="priceDetailsWrapper">
                                <div class="titleText">Price Details</div>
                                <apex:variable var="currencySymb" value="{!CartItemLineList[0].CurrencyIsoCode}" rendered="{!(CartItemLineList!=null)}"/>
                                <div class="table-responsive">
                                    <table class="table" id="priceDetailsTable">
                                        <tbody>
                                            <tr>
                                                <td>Items</td>
                                                <td>{!NumberItems}</td>
                                            </tr>
                                            <tr>
                                                <td>Sub Total</td>
                                                <td>{!currencySymb}&nbsp;<apex:outputText label="NRR" value="{0,number}">
                                                        <apex:param value="{!SubTotal}" />
                                                    </apex:outputText></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </apex:form>

                         <apex:outputpanel id="confirmBox">
                                <div class="modal fade MessageModal" tabindex="-1" role="dialog"
                                    id="confirmBoxModal" data-backdrop="static">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-body">
                                                <img src="{!$Resource.vMarketConfirmNavigationIcon}" />
                                                <div class="mainMessageText" style="padding: 15px 0;">
                                                    <span id="alertText" class="modalText"></span>
                                                </div>
                                                <button type="button" class="SubmitButton" id="cancelbutton" data-dismiss="modal" onClick="window.open ('/vMarketShoppingCart','_self')">Ok</button>                                               
                                            </div>
                                        </div>
                                        <!-- /.modal-content -->
                                    </div>
                                    <!-- /.modal-dialog -->
                                </div>
                            </apex:outputpanel> 
                            
               <div class="modal fade MessageModalIframe" tabindex="-1" role="dialog" id="AgreementBoxModal">
                    <div class="modal-dialog" role="document" style="vertical-align: top;">
                        <div class="modal-content">
                            <div class="modal-body">
                                <div class="mainMessageText modalText" style="padding: 10px 0;">
                                    <apex:iframe scrolling="true" id="EULAIframe"/>     
                                </div>
                                <div style="display:block; text-align:center;">
                                    <button type="button" class="btn okBtn" data-dismiss="modal">OK</button>
                                </div>
                            </div>
                        </div>
                        <!-- /.modal-content -->
                    </div>
                    <!-- /.modal-dialog -->
                </div>               
                                 
            <apex:outputPanel styleClass="container page-content-section"
                rendered="{!(CartItemLineList==null)}" layout="block">
                <div class="col-md-12 col-sm-12 col-xs-12 text-center" style="margin-top:50px;">
                    <apex:image id="noOrders" value="{!$Resource.vMarketNoOrdersIcon}" />
                    <p style="font-size:20px;"> {!$Label.vMarketNoAppInCart}  </p>
                    
                </div>
            </apex:outputPanel>

            <c:vMarketFooter />
        </div>
    </apex:outputPanel>

    <apex:form forceSSL="true">
        <apex:actionFunction name="sendCookieIds" action="{!setCookieIdList}"
            reRender="renderCartItems" status="cartCookieResponse"
            oncomplete="initializeBinding();">
            <apex:param name="cartCookieIds" value="" />
        </apex:actionFunction>
    </apex:form>
    <apex:actionStatus onstart="responseStatus(0);" onstop="responseStatus(1);" id="cartCookieResponse" />

    <apex:include pageName="vMarketLoginModal" />

    <script>
        $(document).ready(function() {
        
        if( '{!taxNotAvailable}' == 'true' )
        {
        $('#confirmBoxModal').modal('toggle');
                $("#alertText").text("{!$Label.vMarket_Tax_Not_Available}");               
                $("#AppActionButton").text('Cancel');
                $('#confirmBoxModal').modal({backdrop: 'static', keyboard: false})
                $("#confirmBoxModal").modal('show');
        }
            var keyIdList = getCookie(key);
            if(! {!Authenticated}) {
                sendCookieIds(keyIdList);
            }
        });
        
       function responseStatus(flag){
           if(flag)
              $('#vMarketLoader').hide(); 
           else
              $('#vMarketLoader').show();
       }
       
       function eulaModal(attachmentId) {
        //var urlStr='https://sfqa-varian.cs15.force.com/servlet/servlet.FileDownload?file='+attachmentId;
         var urlStr='{!$Label.VMarket_EULA_Frame}'+'='+attachmentId;
        console.log('===urlStr=='+urlStr);
        $('#AgreementBoxModal').modal('show');
         $('#EULAIframe').attr('src',urlStr);
    }
    </script>
</apex:page>
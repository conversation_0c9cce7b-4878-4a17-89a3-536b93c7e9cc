<!-- 
 @author: <PERSON> ka<PERSON>
 * @description: Opportunity forecast report by product category
 * @createddate: 12/07/2016
 -->
<apex:page standardStylesheets="false" showChat="false" showHeader="false" controller="FC_ProductCategoryControllerCopy">
    
        
   <html ng-app="app">
      <head>
         <script src="https://code.jquery.com/jquery-2.2.3.min.js"></script>
         <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.4.3/angular.js"></script>
         <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.4.3/angular-touch.js"></script>
         <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.4.3/angular-animate.js"></script>
         <link href="/resource/uigrid/ui-grid.min.css" rel="stylesheet" />
         <script type="text/javaScript" src="/resource/uigrid/ui-grid.min.js"></script>
         <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" rel="stylesheet" />
         <script type="text/javaScript" src="/resource/uiblock"></script>
         <link rel="stylesheet" href="/resource/multiselectchosen/docsupport/prism.css"/>
         <link rel="stylesheet" href="/resource/multiselectchosen/chosen.css"/>
         <link rel="stylesheet" href=" /resource/bootdatepick/css/bootstrap-datepicker.css"/>
         <script type="text/javaScript" src="/resource/bootdatepick/js/bootstrap-datepicker.min.js"></script>
         <script src="/resource/multiselectchosen/chosen.jquery.js" type="text/javascript"></script>
         <script src="/resource/multiselectchosen/docsupport/prism.js" type="text/javascript" charset="utf-8"></script>
         <script type="text/javaScript" src="/resource/bootdatepick/js/bootstrap-datepicker.js"></script>
         <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
         <script type="text/javaScript" src="/resource/MultiSelect/multiselect-master/dist/js/multiselect.js"></script>
         <link rel="stylesheet" href="/resource/MultiSelect/multiselect-master/css/style.css"/>
         <link rel="stylesheet" href="/resource/MultiSelect/multiselect-master/lib/google-code-prettify/prettify.css"/>
         <link rel="stylesheet" href="/resource/ToggleSwitch"/>
         <style type="text/css" media="all">
            /* fix rtl for demo */
            .chosen-rtl .chosen-drop { left: -9000px; }
            body{
                font-size:8pt;
                font-family: Tahoma;
            }
            
            .chosen-choices,.chosen-select,.chosen-drop,.chosen-single,.btn,#multiselect,#multiselect_to{
                font-family: Tahoma;
                font-size:8pt;
            }
            
            .grid {
                width: 600px;
                height: 650px;
            }
            
            .ui-grid-header-cell{
            height:auto;
            text-align: center;
            }
            /* Added by PD to wrap column headers*/
            .ui-grid-header-cell .ui-grid-cell-contents {
             height: 48px;
             white-space: normal;
             -ms-text-overflow: clip;
             -o-text-overflow: clip;
             text-overflow: clip;
             overflow: visible;
            }
            /*---*/
            .ui-grid-header-cell-label{
                white-space:normal;
            }
            
            .ui-grid-filter-container {
            padding: 4px 10px;
            position: relative;
            }    
            .ui-grid-cell-contents {
            height: auto;
            }
            .grid .ui-grid-header-cell input{
            height: 25px;
            }
            .ui-grid-filter-container .ui-grid-filter-button [class^="ui-grid-icon"] {
            position: absolute;
            top: 50%;
            line-height: 32px;
            margin-top: -10px;
            right: 10px;
            opacity: .66;}
            #subRegion_chosen{
            //margin-left: -15px !important;
            }
            #escpectedClosedDate_chosen{
            //margin-left: -15px !important; 
            }
            
            #productFamily_chosen{
            }
            
            .red { color: red;  }
            .blue { color:blue;}
            #fpopup label{
            display:none;
            } 
            /*.modal-dialog{
            max-width:300px;
            }*/
            
            #forecastViewModal .modal-dialog{
                width:80%;
            }
            
            .alert{
                    padding: 5px !important; 
            }
             
            #pqt_chosen{
                //width: 160px !important;
             }
             
             #categoryFilters{
                width: 100%;
             }
             
             #categoryFilters tr td:first-child{
                width: 20%;
             }
             
             #categoryFilters tr td:last-child{
                width: 80%;
             }
             
             #categoryFilters .chosen-container{
                width:100% !important;
             }
             
             /*#filtersView{
                width: 100%;
             }*/
             
             #filtersView .chosen-container{
                width:100% !important;
             }
             
             #categoryFiltersView{
                width:100%;
             }
             
             #categoryFiltersView tr td:first-child{
                width: 20%;
             }
             
             #categoryFiltersView tr td:last-child{
                width: 80%;
             }
             .ui-grid-viewport {
                overflow-anchor: none;
             }
         </style>
         <script type="text/javascript">
            /**
            * 
            */
            function formatNumber (num) {
                return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
            }
          
            function isNumberKey(evt){
              var charCode = (evt.which) ? evt.which : evt.keyCode;
              if (charCode != 46 && charCode > 31 
                && (charCode < 48 || charCode > 57))
                 return false;
    
              return true;
           }
            
            function getDateDifference(startDate, endDate){
                var date1 = new Date(startDate);
                var date2 = new Date(endDate);
                var timeDiff = Math.abs(date2.getTime() - date1.getTime());
                var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24)); 
                return diffDays;
            }
            
            function updateExpectedCloseDateRange(forecastView){
                //status
                $("#status" ).removeClass("alert alert-danger");
                $("#status" ).addClass("alert alert-info");
                $('#status').text('Processing.......'); 
                
                var closeDateRange;
                if(forecastView==''){
                    closeDateRange = $('#escpectedClosedDate').val()
                }else{
                    closeDateRange = $('#escpectedClosedDateView').val()
                }
                Visualforce.remoting.Manager.invokeAction(
                '{!$RemoteAction.FC_ProductCategoryControllerCopy.getExpectedDateRange}',
                closeDateRange,
                function(result, event){
 
                    if (event.status) {
                        if(result!=''){
                            console.log('-----closeDateRange'+result);
                            var dateArray = result.split('-');
                            if(forecastView!=''){
                                //$('#sdateView').val(dateArray[0]);
                                //$('#edateView').val(dateArray[1]);
                                $("#sdateView").datepicker({format: 'mm/dd/yyyy',autoclose: true}).datepicker("update", dateArray[0]); 
                                $("#edateView").datepicker({format: 'mm/dd/yyyy',autoclose: true}).datepicker("update", dateArray[1]);
                            }else{
                                //$('#sdate').val(dateArray[0]);
                                //$('#edate').val(dateArray[1]);
                                $("#sdate").datepicker({format: 'mm/dd/yyyy',autoclose: true}).datepicker("update", dateArray[0]); 
                                $("#edate").datepicker({format: 'mm/dd/yyyy',autoclose: true}).datepicker("update", dateArray[1]);
                            }
                            $("#status" ).addClass("alert alert-info");
                            $('#status').text('Done!');
                        }else{
                            $('#status').text('Error occurred while updating close date range');
                            $("#status" ).addClass("alert alert-danger");
                        }
                    } else if (event.type === 'exception') {
                            $('#status').text(event.message);
                            $("#status" ).addClass("alert alert-danger");
                    } else {}
                }, 
                {escape: true}
                );
            }
            
            function clearForecastViewModal(){
                $("#multiselect_to").css("border","");
                $("#viewName").css("border","");
                $('#sdateView').css("border","");
                $('#edateView').css("border","");
                $("#errorMessage").hide();
                
                $("#pqtView option:selected").prop("selected", false);
                $("#pqtView").trigger("chosen:updated");
                
                $("#subRegionView option:selected").prop("selected", false);
                $("#subRegionView").trigger("chosen:updated");
                
                $("#oppStageView option:selected").prop("selected", false);
                $("#oppStageView").trigger("chosen:updated");
                
                $("#familiesView option:selected").prop("selected", false);
                $("#familiesView").trigger("chosen:updated");
                
                $("#productLinesView option:selected").prop("selected", false);
                $("#productLinesView").trigger("chosen:updated");
                
                $("#productModelsView option:selected").prop("selected", false);
                $("#productModelsView").trigger("chosen:updated");
                
                $('#viewName').val("");
                $('#sdateView').val("");
                $('#edateView').val(""); 
                $('#enbaView').val("");
                $('#snbaView').val("");
                
                $("#probabilityOperatorView").val("");
                $("#probabilityOperatorView").trigger("chosen:updated");
                $("#dealProbabilityOperatorView").val("");
                $("#dealProbabilityOperatorView").trigger("chosen:updated");
                
                $("#probabilityView").val("");
                $("#probabilityView").trigger("chosen:updated");
                $("#dealProbabilityView").val("");
                $("#dealProbabilityView").trigger("chosen:updated");
                
                $("#defaultView").prop("checked",false);
                //$("#probabilityView option:selected").prop("selected", false);
                //$("#probabilityView").trigger("chosen:updated");
                //$("#probabilityOperatorView option:selected").prop("selected", false);
                //$("#probabilityOperatorView").trigger("chosen:updated");
            }
            
            function saveForecastView(){
                
                var reportURL = '/apex/FC_ForecastReportDataCopy?sparam1=' + $('#escpectedClosedDateView').val()+'&defaultView='+$("#defaultView").prop("checked");
                
                //var selectedViewJson = JSON.parse($("#userViews option:selected").val());
                var selectedViewJson = new Object();
                selectedViewJson.Default_View__c = $("#defaultView").prop("checked");
                console.log('----selectedViewJson'+selectedViewJson);
                
                var isNewView = false;
                var dataValidated = true;
                
                if($('#forecastViewId').val() === null){
                    console.log('----TEST'+isNewView);
                 }else{
                    reportURL += '&forecastViewId=' + $('#forecastViewId').val().toString();
                    selectedViewJson.Id = $('#forecastViewId').val().toString();
                 }
                
                var selectedColumnsValues = new Array();
                
                if(selectedViewJson.Id ==""){
                    isNewView = true;
                }
                console.log('----isNewView'+isNewView);
                $("#multiselect_to option").each(function(){
                     selectedColumnsValues.push($(this).val());
                });
                
                if(selectedColumnsValues == ''){
                    $('#errorMessage').show();
                    $('#errorMessage').text('Please enter all the required fields');
                    $("#multiselect_to").css("border", "2px solid red");
                    dataValidated = false;
                 }else{
                    var selectedValuesString = selectedColumnsValues.toString();
                    reportURL += '&selectedViewColumns=' + selectedValuesString;
                    selectedViewJson.Forecast_Columns__c = selectedValuesString;
                 }
                
                if($('#viewName').val() == ''){
                    $('#errorMessage').show();
                    $('#errorMessage').text('Please enter all the required fields');
                    $('#viewName').css("border", "2px solid red");
                    dataValidated = false;
                 }else{
                    var fViewName = $('#viewName').val().toString();
                    reportURL += '&viewName=' + fViewName;
                    selectedViewJson.Name = fViewName;
                 }
                 
                 if(!dataValidated){
                    return false;
                 }
                 
                 if($('#oppStageView').val() === null){
                    
                 }else{
                    var fViewStages = $('#oppStageView').val().toString();
                    reportURL += '&stageOptions=' + fViewStages;
                    selectedViewJson.Stage__c = fViewStages;
                 }
                 
                if($('#familiesView').val() === null){
                    
                 }else{
                    var fViewFamilies = $('#familiesView').val().toString();
                    reportURL += '&familyOptions=' + fViewFamilies;
                    selectedViewJson.Product_Families__c = fViewFamilies;
                 }
                 
                 if($('#productLinesView').val() === null){
                    
                 }else{
                    var fViewProductLines = $('#productLinesView').val().toString();
                    reportURL += '&lineOptions=' + fViewProductLines;
                    selectedViewJson.Product_Lines__c = fViewProductLines;
                 }
                 
                 if($('#productModelsView').val() === null){
                    
                 }else{
                    var fViewProductModels = $('#productModelsView').val().toString();
                    reportURL += '&modelOptions=' + fViewProductModels;
                    selectedViewJson.Product_Models__c = fViewProductModels;
                 }
                 
                 if($('#subRegionView').val() === null){
                    
                 }else{
                    var fViewSubRegion = $('#subRegionView').val();
                    reportURL += '&sr=' + fViewSubRegion;
                    selectedViewJson.Sub_Region__c = fViewSubRegion;
                 }
                 
                 if($('#pqtView').val() === null){
                    
                 }else{
                    var fViewspqt = $('#pqtView').val();
                    reportURL += '&pqt=' + fViewspqt;
                    selectedViewJson.Quote_Type__c = fViewspqt;
                 }
                 
                 if($('#emeaRegionsView').val() === null){
                    
                 }else{
                    var fViewEMEARegions = $('#emeaRegionsView').val();
                    reportURL += '&sr=' + fViewEMEARegions;
                    selectedViewJson.Sub_Region__c = fViewEMEARegions;
                 }
                 
                 if($('#apacRegionsView').val() === null){
                    
                 }else{
                    var fViewAPACRegions = $('#apacRegionsView').val();
                    reportURL += '&sr=' + $('#apacRegionsView').val();
                    selectedViewJson.Sub_Region__c = fViewAPACRegions;
                 }
                 
                 if($('#region').val() === null){
                    
                 }else{
                    var fViewRegion = $('#region').val();
                    reportURL += '&region=' + fViewRegion;
                    selectedViewJson.Region__c = fViewRegion;
                 }
                 
                 if($('#sdateView').val() != '' && $('#edateView').val() != ''){
                    var fViewsdate = $('#sdateView').val();
                    var fViewedate = $('#edateView').val();
                    
                    var daysDiff = getDateDifference(fViewsdate,fViewedate);
                    if(daysDiff>365){
                        $('#errorMessage').show();
                        $('#errorMessage').text('Difference between expected close date range should not exceed 1 year.');
                        $('#sdateView').css("border", "2px solid red");
                        $('#edateView').css("border", "2px solid red");
                        return false;
                    }
                    
                    reportURL += '&sdate=' + fViewsdate;
                    reportURL += '&edate=' + fViewedate;
                    selectedViewJson.Excepted_Close_Date_Start__c = fViewsdate;
                    selectedViewJson.Excepted_Close_Date_End__c = fViewedate;
                 }
                 
                 if($('#snbaView').val() != '' && $('#enbaView').val() != ''){
                    reportURL += '&snba=' + $('#snbaView').val();
                    reportURL += '&enba=' + $('#enbaView').val();
                    
                    var fViewsnba = $('#snbaView').val();
                    var fViewenba = $('#enbaView').val();
                    reportURL += '&snba=' + fViewsnba;
                    reportURL += '&enba=' + fViewenba;
                    selectedViewJson.Net_Booking_Amount_Start__c = fViewsnba;
                    selectedViewJson.Net_Booking_Amount_End__c = fViewenba;
                 }
                 
                 /*if($('#pqtView').val() != '' && $('#pqtView').val() != ''){
                    var fViewspqt = $('#pqtView').val();
                    reportURL += '&pqt=' + fViewspqt;
                    selectedViewJson.Quote_Type__c = fViewspqt;
                 }*/
                 
                 if($('#escpectedClosedDateView').val() != ''){
                    var fViewexceptedCloseDate = $('#escpectedClosedDateView').val();
                    console.log('-----fViewexceptedCloseDate'+fViewexceptedCloseDate);
                    //reportURL += '&sparam1=' + fViewexceptedCloseDate;
                    selectedViewJson.Excepted_Close_Date_Range__c = fViewexceptedCloseDate;
                 }
                 
                 if($('#probabilityOperatorView').val() != '' && $('#probabilityOperatorView').val() != '--NONE--'){
                    var fViewOperator = $('#probabilityOperatorView').val();
                    console.log('-----fViewOperator'+fViewOperator);
                    reportURL += '&probabilityOperator=' + fViewOperator;
                    selectedViewJson.Probability_Operator__c = fViewOperator;
                 }
                 
                 if($('#dealProbabilityOperatorView').val() != '' && $('#dealProbabilityOperatorView').val() != '--NONE--'){
                    var fViewDealOperator = $('#dealProbabilityOperatorView').val();
                    console.log('-----fViewDealOperator'+fViewDealOperator);
                    reportURL += '&dealProbabilityOperator=' + fViewDealOperator;
                    selectedViewJson.Deal_Probability_Operator__c = fViewDealOperator;
                 }
                 
                  if($('#probabilityView').val() != '' && $('#probabilityView').val() != '--NONE--'){
                    var fViewProbability = $('#probabilityView').val();
                    console.log('-----fViewProbability'+fViewProbability);
                    reportURL += '&probability=' + fViewProbability;
                    selectedViewJson.Probability__c = fViewProbability;
                 }
                 
                 if($('#dealProbabilityView').val() != '' && $('#dealProbabilityView').val() != '--NONE--'){
                    var fViewDealProbability = $('#dealProbabilityView').val();
                    console.log('-----fViewDealProbability'+fViewDealProbability);
                    reportURL += '&dealProbability=' + fViewDealProbability;
                    selectedViewJson.Deal_Probability__c = fViewDealProbability;
                 }
                 
                 $("#status" ).removeClass("alert alert-danger");
                     $("#status" ).addClass("alert alert-info");
                     $('#status').text('Processing.......'); 
                     /*$.blockUI({ css: { 
                        border: 'none', 
                        padding: '15px', 
                        backgroundColor: '#000', 
                        '-webkit-border-radius': '10px', 
                        '-moz-border-radius': '10px', 
                        opacity: .5, 
                        color: '#fff' 
                     } });*/
                     
                     Visualforce.remoting.Manager.invokeAction(
                     '{!$RemoteAction.FC_ProductCategoryControllerCopy.updateForecastFilters}',
                     reportURL,
                     function(result, event){
                     console.log(event);
                     console.log(result);
                     if (event.status) {
                        selectedViewJson.Id = result.toString();
                         
                        var viewJSONString = JSON.stringify(selectedViewJson);
                        console.log('-----viewJSONString'+viewJSONString);
                        if(!isNewView){
                            $("#userViews option:selected").val(viewJSONString);
                            $("#userViews option:selected").text(selectedViewJson.Name);
                        }else{
                            $("#userViews").append($('<option>', { 
                                value: viewJSONString,
                                text : selectedViewJson.Name,
                                selected : "true"
                            })
                            );
                        }
                         $("#userViews").trigger("chosen:updated");
                         updateForecastFilters();
                         $("#status" ).addClass("alert alert-info");
                         $('#status').text('Saved!');
                         $('#forecastViewModal').modal("hide");
                     } else if (event.type === 'exception') {
                        $("#status" ).addClass("alert alert-danger");
                        $('#status').text('Error!');
                        $("#errorMessage").show();
                        console.log('----message'+event.message);
                        if(event.message.includes("duplicate value found: View_Key__c")){
                             $("#errorMessage").text("View name already exist.");
                             $('#viewName').css("border", "2px solid red");
                        }else{
                            $("#errorMessage").text(event.message);
                        }
                     } else {
                     }
                     //$.unblockUI();
                     }, 
                     {escape: true}
                     );
                    //$('#forecastViewModal').modal("hide");
            }
            
            function getForecastManualAdjustments(){
                //status issTransferAmount
                $("#status" ).removeClass("alert alert-danger");
                $("#status" ).addClass("alert alert-info");
                $('#status').text('Processing.......'); 
                
                var closeDateRange = $('#escpectedClosedDate').val()
                
                Visualforce.remoting.Manager.invokeAction(
                '{!$RemoteAction.FC_ProductCategoryControllerCopy.getForecastManualAdjustments}',
                closeDateRange,
                "{!$currentpage.parameters.region}",
                function(result, event){
                    console.log('-----ManualAdjustmentStatus'+event.status);
                    if (event.status) {
                        if(result!=''){
                            console.log('-----closeDateRangeManualAdjustment'+result);
                            var manualAdjustmentObj = result.split("##");
                            $("#issTransferAmount").html(parseFloat(manualAdjustmentObj[0]).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                            $("#siteSolutionsAmount").html(parseFloat(manualAdjustmentObj[1]).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                            $("#brachyForecastAmount").html(parseFloat(manualAdjustmentObj[2]).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                            
                            $("#status" ).addClass("alert alert-info");
                            $('#status').text('Done!');
                        }else{
                            $('#status').text('Error occurred while updating manual forecast adjustment values');
                            $("#status" ).addClass("alert alert-danger");
                        }
                    } else if (event.type === 'exception') {
                            $('#status').text(event.message);
                            $("#status" ).addClass("alert alert-danger");
                    } else {}
                }, 
                {escape: true}
                );
            }
            
            function updateForecastFilters(){
                
                console.log('----selectedValue'+$("#userViews option:selected").val());
                $("#probabilityOperator").val("");
                $("#probabilityOperator").trigger("chosen:updated");
                $("#dealProbabilityOperator").val("");
                $("#dealProbabilityOperator").trigger("chosen:updated");
                $("#probability").val("");
                $("#probability").trigger("chosen:updated");
                $("#dealProbability").val("");
                $("#dealProbability").trigger("chosen:updated");
                
                if($('#userViews').val() === null){
                    $('#status').text("Please select/create a view to run forecast report");
                    $("#status" ).addClass("alert alert-danger");
                    $.unblockUI();
                    return false;
                }else{
                    var selectedView = JSON.parse($("#userViews option:selected").val());
                    console.log('----selectedView.Quote_Type__c'+selectedView.Quote_Type__c);
                    
                    //setSelectByValue('pqt',selectedView.Quote_Type__c);
                    setSelectByValue('escpectedClosedDate',selectedView.Excepted_Close_Date_Range__c);
                    
                    if(selectedView.Excepted_Close_Date_Range__c == 'custom'){
                        $("#sdate").removeAttr("disabled");
                        $("#edate").removeAttr("disabled");
                    }else{
                        $("#sdate").attr("disabled","true");
                        $("#edate").attr("disabled","true");
                    }
                    
                    $("#fcViewColumns").val(selectedView.Forecast_Columns__c);
                    //$("#pqt").trigger("chosen:updated");
                    $("#escpectedClosedDate").trigger("chosen:updated");
                    
                    $("#pqt option:selected").prop("selected", false);
                    $("#pqt").trigger("chosen:updated");
                    $("#subRegion option:selected").removeAttr("selected");
                    $("#subRegion").trigger("chosen:updated");
                    $("#emeaRegions option:selected").removeAttr("selected");
                    $("#emeaRegions").trigger("chosen:updated");
                    $("#apacRegions option:selected").removeAttr("selected");
                    $("#apacRegions").trigger("chosen:updated");
                    $("#oppStage option:selected").removeAttr("selected");
                    $("#oppStage").trigger("chosen:updated");
                    $("#families option:selected").removeAttr("selected");
                    $("#families").trigger("chosen:updated");
                    $("#productLines option:selected").removeAttr("selected");
                    $("#productLines").trigger("chosen:updated");
                    $("#productModels option:selected").removeAttr("selected");
                    $("#productModels").trigger("chosen:updated");
                    
                    var regionVal = "{!$currentpage.parameters.region}";
                    
                    if(selectedView.Sub_Region__c !== undefined){
                        var srViewArray = selectedView.Sub_Region__c.toString().split(',');
                        console.log('srViewArray123---'+srViewArray);
                        $.each( srViewArray, function( key, value ) {
                            if(value === ''){
                            }else{
                                if(regionVal == 'Americas'){                                    
                                    setSelectByValue('subRegion',value);
                                    $("#subRegion").trigger("chosen:updated");
                                }else if(regionVal == 'EMEA'){
                                    setSelectByValue('emeaRegions',value);
                                    $("#emeaRegions").trigger("chosen:updated");
                                }else if(regionVal == 'APAC'){
                                    setSelectByValue('apacRegions',value);
                                    $("#apacRegions").trigger("chosen:updated");
                                }
                            }
                        });
                    }
                    
                    if(selectedView.Probability__c !== undefined){
                        setSelectByValue('probability',selectedView.Probability__c);
                        $("#probability").trigger("chosen:updated");
                    }
                    
                    if(selectedView.Deal_Probability__c !== undefined){
                        setSelectByValue('dealProbability',selectedView.Deal_Probability__c);
                        $("#dealProbability").trigger("chosen:updated");
                    }
                    
                    if(selectedView.Probability_Operator__c !== undefined){
                        setSelectByValue('probabilityOperator',selectedView.Probability_Operator__c);
                        $("#probabilityOperator").trigger("chosen:updated");
                    }
                    
                    if(selectedView.Deal_Probability_Operator__c !== undefined){
                        setSelectByValue('dealProbabilityOperator',selectedView.Deal_Probability_Operator__c);
                        $("#dealProbabilityOperator").trigger("chosen:updated");
                    }
                    
                    if(selectedView.Quote_Type__c !== undefined){
                        console.log('selectedView.Quote_Type__c---'+selectedView.Quote_Type__c);
                        var quoteTypeViewArray = selectedView.Quote_Type__c.toString().split(',');
                        console.log('familyViewArray123---'+quoteTypeViewArray);
                        $.each( quoteTypeViewArray, function( key, value ) {    
                            if(value === ''){
                            }else{
                                setSelectByValue('pqt',value);
                                $("#pqt").trigger("chosen:updated");
                            }
                        });
                    }
                    
                    if(selectedView.Stage__c !== undefined){
                        console.log('selectedView.Stage__c---'+selectedView.Stage__c);
                        var stageViewArray = selectedView.Stage__c.split(',');
                        console.log('stageViewArray---'+stageViewArray);
                        $.each( stageViewArray, function( key, value ) {   
                            if(value === ''){
                            }else{
                                setSelectByValue('oppStage',value);
                                $("#oppStage").trigger("chosen:updated");
                            }
                        });
                    }
                    
                    if(selectedView.Product_Families__c !== undefined){
                        console.log('selectedView.Product_Families__c---'+selectedView.Product_Families__c);
                        var familyViewArray = selectedView.Product_Families__c.split(',');
                        console.log('familyViewArray123---'+familyViewArray);
                        $.each( familyViewArray, function( key, value ) {   
                            if(value === ''){
                            }else{
                                console.log('familyViewvalue---'+typeof(value));
                                setSelectByValue('families',value);
                                $("#families").trigger("chosen:updated");
                            }
                        });
                    }
                            
                    if(selectedView.Product_Lines__c !== undefined){
                        var lineViewArray = selectedView.Product_Lines__c.split(',');
                        console.log(lineViewArray);
                        $.each( lineViewArray, function( key, value ) {
                            if(value === ''){
                            }else{
                                setSelectByValue('productLines',value);
                                $("#productLines").trigger("chosen:updated");
                            }
                        });
                    }
                            
                    if(selectedView.Product_Models__c != undefined){
                        var modelViewArray = selectedView.Product_Models__c.split(',');
                        console.log(modelViewArray);
                        $.each( modelViewArray, function( key, value ) {
                            if(value === ''){
                            }else{
                                setSelectByValue('productModels',value);
                                $("#productModels").trigger("chosen:updated");
                            }
                        });
                    }
                            
                    $('#sdate').val(selectedView.Excepted_Close_Date_Start__c);
                    $('#edate').val(selectedView.Excepted_Close_Date_End__c); 
                    $('#enba').val(selectedView.Net_Booking_Amount_End__c);
                    $('#snba').val(selectedView.Net_Booking_Amount_Start__c);
                    getForecastManualAdjustments();
                    angular.element($("#grid1")).scope().gridOptions.columnDefs = [];
                    angular.element($("#grid1")).scope().gridOptions.exporterCsvFilename = getExportFileName();
                    angular.element($("#grid1")).scope().refreshData();
                }
            }
            
            function setSelectByValue(eID,val){ //Loop through sequentially//
              console.log('setValue'+eID+'-'+val);
              var ele=document.getElementById(eID);
              for(var ii=0; ii<ele.length; ii++)
                if(ele.options[ii].value==val) { //Found!
                  console.log('---setValue1'+eID+'-'+val);
                  console.log(ele.options[ii]);
                  ele.options[ii].selected=true;
                  console.log(ele.options[ii]);
                  return true;
                }
              return false;
            }
            
            function GetURLParameter(sParam){
                var sPageURL = '/apex/FC_ForecastReportDataCopy?sparam1=s';
                console.log(sPageURL);
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                     return sParameterName[1];
                    }
                }
            }
                       
            function toLocal (date) {
                var local = new Date(date);
                var offset = new Date().getTimezoneOffset();
                var minstamp = new Date().getMinutes();
                local.setMinutes(minstamp - offset );
                return local.toJSON();
            }
            
            function toJSONLocal (date) {
                var local = new Date(date);
                var offset = new Date().getTimezoneOffset();
                var minstamp = new Date().getMinutes();
                local.setMinutes(minstamp - offset );
                return local.toJSON().slice(0, 10);
            }
            
            function getExportFileName(){
                if($('#sdate').val() != '' && $('#edate').val() != ''){
                    return 'Forecast Report_'+$('#sdate').val()+' to '+$('#edate').val()+'.csv';
                }
                return 'Forecast Report.csv';
            }
            
            function dynamicSort(property) {
                var sortOrder = 1;
                if(property[0] === "-") {
                    sortOrder = -1;
                    property = property.substr(1);
                }
                return function (a,b) {
                    var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
                    return result * sortOrder;
                } 
            }
            
            function getReportURL(){
                
                 var reportURL = '/apex/FC_ForecastReportDataCopy?sparam1=' + $('#escpectedClosedDate').val();
                 
                 
                 if($('#oppStage').val() === null){
                    
                 }else{
                    reportURL += '&stageOptions=' + $('#oppStage').val().toString();
                 }
                 
                 if($('#probabilityOperator').val() === null){
                    
                 }else{
                    console.log('-----operator'+$('#probabilityOperator').val());
                    reportURL += '&probabilityOperator=' + $('#probabilityOperator').val();
                 }
                 
                 if($('#dealProbabilityOperator').val() === null){
                    
                 }else{
                    reportURL += '&dealProbabilityOperator=' + $('#dealProbabilityOperator').val();
                 }
                 
                 if($('#probability').val() === null){
                    
                 }else{
                    reportURL += '&probability=' + $('#probability').val();
                 }
                 
                 if($('#dealProbability').val() === null){
                    
                 }else{
                    reportURL += '&dealProbability=' + $('#dealProbability').val();
                 }
                 
                 if($('#families').val() === null){
                    
                 }else{
                    reportURL += '&familyOptions=' + $('#families').val().toString();
                 }
                 
                 if($('#productLines').val() === null){
                    
                 }else{
                    reportURL += '&lineOptions=' + $('#productLines').val().toString();
                 }
                 
                 if($('#productModels').val() === null){
                    
                 }else{
                    reportURL += '&modelOptions=' + $('#productModels').val().toString();
                 }
                 
                 if($('#subRegion').val() === null){
                    
                 }else{
                    reportURL += '&sr=' + escape($('#subRegion').val());
                 }
                 
                 if($('#emeaRegions').val() === null){
                    
                 }else{
                    reportURL += '&sr=' + escape($('#emeaRegions').val());
                 }
                 
                 if($('#apacRegions').val() === null){
                    
                 }else{
                    reportURL += '&sr=' + escape($('#apacRegions').val());
                 }
                 
                 if($('#region').val() === null){
                    
                 }else{
                    reportURL += '&region=' + $('#region').val();
                 }
                 
                 if($('#fcViewColumns').val() === null){
                    
                 }else{
                    reportURL += '&selectedViewColumns=' + $('#fcViewColumns').val();
                 }
                 
                 if($('#sdate').val() != '' && $('#edate').val() != ''){
                    reportURL += '&sdate=' + $('#sdate').val();
                    reportURL += '&edate=' + $('#edate').val();
                 }
                 
                 if($('#snba').val() != '' && $('#enba').val() != ''){
                    reportURL += '&snba=' + $('#snba').val();
                    reportURL += '&enba=' + $('#enba').val();
                 }
                 
                 if($('#pqt').val() === null){
                    
                 }else{
                    reportURL += '&pqt=' + $('#pqt').val();
                 }
                 
                 if("{!$currentpage.parameters.productCategories}" == "true"){
                    reportURL += '&productCategories=true';
                 }
                 console.log('----$("#includeISSTransferAmount").prop("checked")'+$("#includeISSTransferAmount").prop("checked"));
                 if($("#includeISSTransferAmount").prop("checked")){
                    reportURL += '&issTransfer='+$('#issTransferAmount').text();
                 }
                 if($("#includeSiteSolutionsAmount").prop("checked")){
                    reportURL += '&siteSolutions='+$('#siteSolutionsAmount').text();
                 }
                 if($("#includeBrachyForecastAmount").prop("checked")){
                    reportURL += '&brachyForecast='+$('#brachyForecastAmount').text();
                 }
                 if($("#includeAmendmentQuotes").prop("checked")){
                    reportURL += '&showAmendments=true';
                 }
                 console.log('---reportURL--'+reportURL);
                 return reportURL;
            
            }

            var app = angular.module('app', ['ngTouch', 'ui.grid']);
            
            var columnDefsTemp = [{name: 'subregion',displayName: 'Sub Region', width: '110',enableCellEdit: false, sort: {priority: 0,direction: 'desc'} }, 
                                  {name: 'account',enableCellEdit: false,displayName: 'Account Name',width: '150'}, 
                                  {name: 'city',enableCellEdit: false,displayName: 'City',enableFiltering: true,width: '150'},
                                  {name:'state', width:60, enableCellEdit: false , displayName: 'State'},
                                  {name: 'atype',enableCellEdit: false,displayName: 'Account Type',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'strategic_account',enableCellEdit: false,displayName: 'Strategic Account',width: '150'},
                                  {name: 'strategic_owner',width:150,displayName: 'Strategic Account Owner',enableCellEdit: false,
                                   filterHeaderTemplate: '<div class="ui-grid-filter-container" ng-repeat="colFilter in col.filters"><div id="fpopup" custom-model-sowner></div></div>',
                                  },
                                  {name: 'accountowner',width:120,displayName: 'DM',enableCellEdit: false,
                                   filterHeaderTemplate: '<div class="ui-grid-filter-container" ng-repeat="colFilter in col.filters"><div id="fpopup" my-custom-modal></div></div>',
                                  },
                                  {name: 'oppowner',width:150,displayName: 'Oppty Owner',enableCellEdit: false,
                                   filterHeaderTemplate: '<div class="ui-grid-filter-container" ng-repeat="colFilter in col.filters"><div id="fpopup" my-custom-modal-oppowner></div></div>',
                                  },
                                  {name:'pquote', width:80, enableCellEdit: false , displayName: 'Quote #', enableFiltering: true},
                                  {name: 'optyref',enableCellEdit: false,displayName: 'Oppty Ref',enableFiltering: true,enableHiding: false,width: '90'}, 
                                  {name: 'oppname',enableCellEdit: false,displayName: 'Oppty Name',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'ostage',enableCellEdit: false,displayName: 'Oppty Stage',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'na',enableCellEdit: false,displayName: 'National Affilation',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'qs',enableCellEdit: false,displayName: 'Quote Status',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'nbv',enableCellEdit: false,displayName: 'Oppty Name',enableFiltering: false,enableHiding: false,width: '150' },
                                  {name: 'iswon', enableCellEdit: false, enableFiltering: false, displayName: 'Won', type: 'boolean',cellTemplate: '<center><input type="checkbox" ng-model="row.entity.iswon" disabled></center>', width: '50'}

                                 ];
            
            
            var editedRows = [];
            
            var app = angular.module('app', ['ngAnimate', 'ngTouch', 'ui.grid',
            'ui.grid.grouping', 'ui.grid.edit', 'ui.grid.rowEdit','ui.grid.pinning',
            'ui.grid.selection', 'ui.grid.exporter', 'ui.grid.resizeColumns','ui.grid.moveColumns'
            ]);
            
            app
            .controller(
            'MainCtrl', [
             '$scope',
             '$http',
             'uiGridConstants',
             '$q',
             '$interval',
             'uiGridGroupingConstants',
             '$filter',
             'stats',
             function($scope, $http, uiGridConstants, $q, $interval,
                 uiGridGroupingConstants, $filter, stats) {
                 var setGroupValues = function(columns, rows) {
                     columns
                         .forEach(function(column) {
                             if (column.grouping &&
                                 column.grouping.groupPriority > -1) {
                                 // Put the balance next to all
                                 // group labels.
                                 column.treeAggregationFn = function(
                                     aggregation,
                                     fieldValue, numValue,
                                     row) {
                                     if (typeof(aggregation.value) === 'undefined') {
                                         aggregation.value = 0;
                                     }
                                     aggregation.value = aggregation.value +
                                         row.entity.balance;
                                 };
                                 column.customTreeAggregationFinalizerFn = function(
                                     aggregation) {
                                     if (typeof(aggregation.groupVal) !== 'undefined') {
                                         aggregation.rendered = aggregation.groupVal +
                                             ' (' +
                                             $filter(
                                                 'currency')
                                             (
                                                 aggregation.value) +
                                             ')';
                                     } else {
                                         aggregation.rendered = null;
                                     }
                                 };
                             }
                         });
                     return columns;
                 };
            
                 // Add reload funtion
            
            
                 $scope.msg = {};
            
                 $scope.saveRow = function(rowEntity) {
                     // create a fake promise - normally youd use
                     // the promise returned by $http or $resource
                     var promise = $q.defer();
                     $scope.gridApi.rowEdit.setSavePromise(
                         rowEntity, promise.promise);
                     //$promise.resolve();
                     promise.reject();
                     $scope.gridApi.selection.selectRow(rowEntity); 
                 };
                 
                 /*$scope.gridApi.selection.getSelectedRows()*/
                 $scope.saveData = function(){
                     //status
                     //alert($scope.gridApi.selection.getSelectedRows());
                     $("#status" ).removeClass("alert alert-danger");
                     $("#status" ).addClass("alert alert-info");
                     $('#status').text('Processing.......'); 
                     $.blockUI({ css: { 
                        border: 'none', 
                        padding: '15px', 
                        backgroundColor: '#000', 
                        '-webkit-border-radius': '10px', 
                        '-moz-border-radius': '10px', 
                        opacity: .5, 
                        color: '#fff' 
                     } });
                     
                     var rows = $scope.gridApi.selection.getSelectedRows();
                     for (var i = 0; i < rows.length; i++) {
                        console.log('toJSONLocal==='+toJSONLocal(rows[i].closedate));
                        console.log('local==='+toLocal(rows[i].closedate));
                        
                     }
                     console.log('SAVEDATAROWS--'+JSON.stringify(rows));
                     Visualforce.remoting.Manager.invokeAction(
                     '{!$RemoteAction.FC_ProductCategoryControllerCopy.saveData}',
                     JSON.stringify(rows),
                     function(result, event){
                     
                     console.log(event);
                     console.log(result);
                     
                     if (event.status) {
                         console.log(result);
                         $("#status" ).addClass("alert alert-info");
                         $('#status').text('Saved!');
                         //$scope.refreshData();
                     } else if (event.type === 'exception') {
                         $('#status').text(event.message);
                         $("#status" ).addClass("alert alert-danger");
                         //$.unblockUI();
                     } else {
                         //$.unblockUI();
                     }
                     $.unblockUI();
                     }, 
                     {escape: true}
                     );
                 };
                 
                 
                 $scope.callsPending = 0;
                 var i = 0;
                 $scope.refreshData = function() {
                 
                    var diffDays = getDateDifference($('#sdate').val(),$('#edate').val());
                    console.log('----diffDays'+diffDays);
                    if(diffDays>365){
                        console.log('----diffDays12'+diffDays);
                        $('#status').text('Difference between expected close date range should not exceed 1 year.');
                        $("#status" ).addClass("alert alert-danger");
                        return false;
                    }
                    
                    if($('#userViews').val() === null){
                        $('#status').text("Please select/create a view to run forecast report");
                        $("#status" ).addClass("alert alert-danger");
                        return false;
                    }                           
                    getForecastManualAdjustments();
                     editedRows = [];
                     $("#status" ).removeClass("alert alert-danger");
                     $("#status" ).addClass("alert alert-info");
                     $('#status').text('Processing.......');   
                     
                     $.blockUI({ css: { 
                        border: 'none', 
                        padding: '15px', 
                        backgroundColor: '#000', 
                        '-webkit-border-radius': '10px', 
                        '-moz-border-radius': '10px', 
                        opacity: .5, 
                        color: '#fff' 
                    } });
                     
                     $scope.gridOptions.data = [];
                     var sfdcReferenceNumber = '';
                    var totalOpps = 0;
                    var oppData = [];
                        
                        console.log('----sfdcReferenceNumberBC'+sfdcReferenceNumber);
                        $http.get(getReportURL() + '&sfdcRefNumber=start').success(
                            function(data) {
                                //console.log('-----data'+data.rdList);
                                totalOpps = data.rdList.length;
                                console.log('----length'+totalOpps);
                                if (totalOpps > 0 || "{!$currentpage.parameters.productCategories}"!="") {
                                    sfdcReferenceNumber = data.lastSFDCReferenceNumber;
                                    console.log('----sfdcReferenceNumber'+sfdcReferenceNumber);
                                    //data populate
                                    for (var i = 0; i < data.rdList.length; i++) {
                                        //data.rdList[i].closedate = new Date(data.rdList[i].closedate);
                                        //INC4687493:  "Close Date" on Forecasting Report auto-increments by 1
                                        data.rdList[i].closedate = new Date(data.rdList[i].closedate);
                                        if(data.rdList[i].closedate.getTimezoneOffset() > 0 ) {
                                            data.rdList[i].closedate.setMinutes(data.rdList[i].closedate.getMinutes() + data.rdList[i].closedate.getTimezoneOffset());
                                        }
                                          console.log('----AFTER OFFSET-----'+data.rdList[i].closedate);
                                    }
                                    
                                    var columnDefs3 = [];
                                    var columnDefs2 = [];
                                    var columnDefs1 = [];
                                    //console.log(data);
                                    //create dynamic columns
                                    var othercolumns = [];
                
                                    var colList = data.columnList.sort(dynamicSort('orderNumber'))
                                    console.log(colList);
                                    for (var i = 0; i < colList.length; i++) {
                                        var coldata = colList[i];
                                        if (coldata.visible) {
                                            if (coldata.x_type == 'Currency') {
                                                var newColumn = {
                                                    width: '110',
                                                    type: 'number',
                                                    cellFilter: 'currency',
                                                    footerCellFilter: 'currency',
                                                    enableFiltering: false,
                                                    treeAggregationType: uiGridGroupingConstants.aggregation.SUM,
                                                    suppressRemoveSort: true,
                                                    customTreeAggregationFinalizerFn: function(
                                                        aggregation) {
                                                        aggregation.rendered = aggregation.value;
                                                    },
                                                    field: coldata.name,
                                                    displayName: coldata.displayName,
                                                    enableCellEdit: false,
                                                };
                                                if (coldata.isManagerFlag) {
                                                    othercolumns.push(newColumn);
                                                } else {
                                                    othercolumns.push(newColumn);
                                                }
                
                                            } else {
                
                                                if (coldata.x_type == 'mgrp' && coldata.visible) {
                
                                                    var probabilityArray = [];
                
                                                    if ($("#region").val() == "Americas") {
                                                        probabilityArray = [{
                                                            id: '0%',
                                                            Probability: '0%   - NA'
                                                        }, {
                                                            id: '25%',
                                                            Probability: '25%  - NA'
                                                        }, {
                                                            id: '50%',
                                                            Probability: '50%  - NA'
                                                        }, {
                                                            id: '75%',
                                                            Probability: '75%  - NA'
                                                        }, {
                                                            id: '100%',
                                                            Probability: '100% - NA'
                                                        }];
                                                    } else {
                                                        probabilityArray = [{
                                                            id: '0%',
                                                            Probability: '0%   - ROW'
                                                        }, {
                                                            id: '20%',
                                                            Probability: '20%  - ROW'
                                                        }, {
                                                            id: '40%',
                                                            Probability: '40%  - ROW'
                                                        }, {
                                                            id: '60%',
                                                            Probability: '60%  - ROW'
                                                        }, {
                                                            id: '80%',
                                                            Probability: '80%  - ROW'
                                                        }, {
                                                            id: '100%',
                                                            Probability: '100% - ROW'
                                                        }];
                                                    }
                
                                                    var newColumn = {
                                                        name: coldata.name,
                                                        enableCellEdit: coldata.editable,
                                                        displayName: coldata.displayName,
                                                        enableFiltering: false,
                                                        width: '110',
                                                        editDropdownValueLabel: 'Probability',
                                                        editableCellTemplate: 'ui-grid/dropdownEditor',
                                                        headerCellClass: 'blue',
                                                        suppressRemoveSort: true,
                                                        editDropdownOptionsArray: probabilityArray
                                                    };
                                                    othercolumns.push(newColumn);
                
                                                } else {
                                                    var reportColumn = {
                                                        name: coldata.name,
                                                        enableCellEdit: coldata.editable,
                                                        displayName: coldata.displayName,
                                                        enableFiltering: coldata.enableFiltering,
                                                        width: coldata.width,
                                                        enableHiding: coldata.enableHiding,
                                                        suppressRemoveSort: true
                                                    };
                
                                                    if (coldata.cellFilter != undefined) {
                                                        reportColumn.cellFilter = coldata.cellFilter;
                                                    }
                                                    if (coldata.headerCellClass != undefined) {
                                                        reportColumn.headerCellClass = coldata.headerCellClass;
                                                    }
                                                    if (coldata.cellTemplate != undefined) {
                                                        reportColumn.cellTemplate = coldata.cellTemplate;
                                                    }
                
                                                    if (coldata.filterHeaderTemplate != undefined) {
                                                        console.log('----filterHeaderTemplate' + typeof(coldata.filterHeaderTemplate));
                                                        reportColumn.filterHeaderTemplate = coldata.filterHeaderTemplate;
                                                    }
                
                                                    if (coldata.sortAttr != undefined) {
                                                        reportColumn.sort = coldata.sortAttr;
                                                    }
                
                                                    if (coldata.editableCellTemplate != undefined) {
                                                        reportColumn.editableCellTemplate = coldata.editableCellTemplate;
                                                    }
                
                                                    othercolumns.push(reportColumn);
                                                }
                                            }
                                        }
                
                                    }
                                    //console.log(othercolumns);
                                    var columnDefsNew = columnDefs1.concat(columnDefs2);
                                    columnDefsNew = columnDefsNew.concat(othercolumns);
                                    columnDefsNew = columnDefsNew.concat(columnDefs3);
                                    console.log('---check point');
                                    console.log(columnDefsNew);
                                    $scope.gridOptions.columnDefs = columnDefsNew;
                                    
                                    //data populate
                                    for (var i = 0; i < data.rdList.length; i++) {
                                        data.rdList[i].closedate = new Date(data.rdList[i].closedate);
                                    }
                                    //oppData = oppData.concat(data.rdList);
                                    console.log('---oppDataLoop'+oppData);
                                     
                                    $scope.gridOptions.data = data.rdList;
                                    //2nd call
                                    $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                        function(data1) {
                                            console.log('----data new1'+data1.rdList);
                                            sfdcReferenceNumber = data1.lastSFDCReferenceNumber;
                                            console.log('-----data1'+sfdcReferenceNumber);
                                            for (var i = 0; i < data1.rdList.length; i++) {
                                                data1.rdList[i].closedate = new Date(data1.rdList[i].closedate);
                                            }
                                            $scope.gridOptions.data = $scope.gridOptions.data.concat(data1.rdList);
                                            
                                            //3rd call
                                            $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                function(data2) {
                                                    console.log('----data new2'+data2.rdList);
                                                    console.log('-----data2'+data2.lastSFDCReferenceNumber);
                                                    sfdcReferenceNumber = data2.lastSFDCReferenceNumber;
                                                    for (var i = 0; i < data2.rdList.length; i++) {
                                                        data2.rdList[i].closedate = new Date(data2.rdList[i].closedate);
                                                    }
                                                    $scope.gridOptions.data = $scope.gridOptions.data.concat(data2.rdList);
                                                    
                                                    //4th call
                                                    $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                        function(data3) {
                                                            console.log('-----data3'+data3.lastSFDCReferenceNumber);
                                                            sfdcReferenceNumber = data3.lastSFDCReferenceNumber;
                                                            for (var i = 0; i < data3.rdList.length; i++) {
                                                                data3.rdList[i].closedate = new Date(data3.rdList[i].closedate);
                                                            }
                                                            $scope.gridOptions.data = $scope.gridOptions.data.concat(data3.rdList);
                                                            
                                                            //5th call
                                                            $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                function(data4) {
                                                                    console.log('-----data4'+data4.lastSFDCReferenceNumber);
                                                                    sfdcReferenceNumber = data4.lastSFDCReferenceNumber;
                                                                    for (var i = 0; i < data4.rdList.length; i++) {
                                                                        data4.rdList[i].closedate = new Date(data4.rdList[i].closedate);
                                                                    }
                                                                    $scope.gridOptions.data = $scope.gridOptions.data.concat(data4.rdList);
                                                                    
                                                                    //6th call
                                                                    $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                        function(data5) {
                                                                            console.log('-----data5'+data5.lastSFDCReferenceNumber);
                                                                            sfdcReferenceNumber = data5.lastSFDCReferenceNumber;
                                                                            for (var i = 0; i < data5.rdList.length; i++) {
                                                                                data5.rdList[i].closedate = new Date(data5.rdList[i].closedate);
                                                                            }
                                                                            $scope.gridOptions.data = $scope.gridOptions.data.concat(data5.rdList);
                                                                            
                                                                            //7th call
                                                                            $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                                function(data6) {
                                                                                    console.log('-----data6'+data6.lastSFDCReferenceNumber);
                                                                                    sfdcReferenceNumber = data6.lastSFDCReferenceNumber;
                                                                                    for (var i = 0; i < data6.rdList.length; i++) {
                                                                                        data6.rdList[i].closedate = new Date(data6.rdList[i].closedate);
                                                                                    }
                                                                                    $scope.gridOptions.data = $scope.gridOptions.data.concat(data6.rdList);
                                                                                    
                                                                                    //8th call
                                                                                    $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                                        function(data7) {
                                                                                            console.log('-----data7'+data7.lastSFDCReferenceNumber);
                                                                                            sfdcReferenceNumber = data7.lastSFDCReferenceNumber;
                                                                                            for (var i = 0; i < data7.rdList.length; i++) {
                                                                                                data7.rdList[i].closedate = new Date(data7.rdList[i].closedate);
                                                                                            }
                                                                                            $scope.gridOptions.data = $scope.gridOptions.data.concat(data7.rdList);
                                                                                            
                                                                                            //9th call
                                                                                            $http.get(getReportURL() + '&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                                                function(data8) {
                                                                                                    console.log('-----data8'+data8.lastSFDCReferenceNumber);
                                                                                                    sfdcReferenceNumber = data8.lastSFDCReferenceNumber;
                                                                                                    for (var i = 0; i < data8.rdList.length; i++) {
                                                                                                        data8.rdList[i].closedate = new Date(data8.rdList[i].closedate);
                                                                                                    }
                                                                                                    $scope.gridOptions.data = $scope.gridOptions.data.concat(data8.rdList);
                                                                                                    
                                                                                                    //10th call
                                                                                                    $http.get(getReportURL() + '&finalCall=true&sfdcRefNumber='+sfdcReferenceNumber).success(
                                                                                                        function(data9) {
                                                                                                            console.log('-----data9'+data9.lastSFDCReferenceNumber);
                                                                                                            sfdcReferenceNumber = data9.lastSFDCReferenceNumber;
                                                                                                            for (var i = 0; i < data9.rdList.length; i++) {
                                                                                                                data9.rdList[i].closedate = new Date(data9.rdList[i].closedate);
                                                                                                            }
                                                                                                            $('#status').text('Done!');
                                                                                                            $.unblockUI();
                                                                                                            $( "#status" ).addClass("alert alert-info");
                                                                                                            $scope.gridOptions.data = $scope.gridOptions.data.concat(data9.rdList);
                                                                                                            $scope.gridApi.core.notifyDataChange(uiGridConstants.dataChange.ALL);
                                                                                                        }
                                                                                                    );
                                                                                                }
                                                                                            );
                                                                                        }
                                                                                    );
                                                                                }
                                                                            );
                                                                        }
                                                                    );
                                                                }
                                                            );
                                                        }
                                                    );
                                                }
                                            );
                                        }
                                    );
                                }else{
                                    $('#status').text('No Records found!');
                                    $.unblockUI();
                                    $( "#status" ).addClass("alert alert-info");
                                    $scope.gridApi.core.notifyDataChange(uiGridConstants.dataChange.ALL);
                                }
                            });
                 };
                
                 $scope.gridOptions = {
                     enableCellEditOnFocus: true,
                     enableFiltering: true,
                     enableGroupHeaderSelection: true,
                     treeRowHeaderAlwaysVisible: false,
                     showColumnFooter: true,
                     enableGridMenu: true,
                     enableSelectAll: true,
                     columnDefs: columnDefsTemp,
                     exporterCsvFilename: getExportFileName(),
                     exporterPdfDefaultStyle: {
                         fontSize: 9
                     },
                     exporterPdfTableStyle: {
                         margin: [30, 30, 30, 30]
                     },
                     exporterPdfTableHeaderStyle: {
                         fontSize: 10,
                         bold: true,
                         italics: true,
                         color: 'red'
                     },
                     exporterPdfHeader: {
                         text: "My Header",
                         style: 'headerStyle'
                     },
                     exporterPdfFooter: function(currentPage,
                         pageCount) {
                         return {
                             text: currentPage.toString() + ' of ' +
                                 pageCount.toString(),
                             style: 'footerStyle'
                         };
                     },
                     //INC4636972: Truncate time from the export on Forecast Report 
                     exporterFieldCallback: function (grid, row, col, value) {
                         if ( col.colDef.cellTemplate == 'ui-grid/date-cell' || col.colDef.type == 'date' ){
                            value = $filter('date')(value, 'yyyy-MM-dd');
                          }
                          return value;
                     },
                     exporterPdfCustomFormatter: function(
                         docDefinition) {
                         docDefinition.styles.headerStyle = {
                             fontSize: 22,
                             bold: true
                         };
                         docDefinition.styles.footerStyle = {
                             fontSize: 10,
                             bold: true
                         };
                         return docDefinition;
                     },
                     exporterPdfOrientation: 'portrait',
                     exporterPdfPageSize: 'LETTER',
                     exporterPdfMaxGridWidth: 500,
                     exporterCsvLinkElement: angular.element(document.querySelectorAll(".custom-csv-link-location")),
                     onRegisterApi: function(gridApi) {
                         $scope.gridApi = gridApi;
                     },
            
                     onRegisterApi: function(gridApi) {
                         $scope.gridApi = gridApi;
            
                         // set gridApi on scope
                         $scope.gridApi = gridApi;
                         gridApi.rowEdit.on.saveRow($scope,
                             $scope.saveRow);
            
                         // $scope.gridApi.grid.registerColumnsProcessor(setGroupValues,
                         // 410);
                         $scope.gridApi.selection.on
                             .rowSelectionChanged(
                                 $scope,
                                 function(rowChanged) {
                                     if (typeof(rowChanged.treeLevel) !== 'undefined' &&
                                         rowChanged.treeLevel > -1) {
                                         // this is a group
                                         // header
                                         children = $scope.gridApi.treeBase
                                             .getRowChildren(rowChanged);
                                         children
                                             .forEach(function(
                                                 child) {
                                                 if (rowChanged.isSelected) {
                                                     $scope.gridApi.selection
                                                         .selectRow(child.entity);
                                                 } else {
                                                     $scope.gridApi.selection
                                                         .unSelectRow(child.entity);
                                                 }
                                             });
                                     }
                                 });
            
                         gridApi.edit.on
                             .afterCellEdit(
                                 $scope,
                                 function(rowEntity, colDef,
                                     newValue, oldValue) {
                                     console.log('---colDef.name'+colDef.name+'--prob value--'+rowEntity['mprob']+'---booking value--'+rowEntity['WeightedSystem SolutionsAdvanced Treatment Delivery']);
                                     console.log('--dprob value--'+rowEntity['dprob']);
                                     console.log($scope.gridOptions.columnDefs);
                                     
                                     if(colDef.name == 'mprob' || colDef.name == 'prob' || colDef.name == 'dprob'){
                                        console.log('colDef.name'+colDef.name);
                                        $.each($scope.gridOptions.columnDefs, function( index, columnObject ) {
                                            console.log('index'+columnObject.name.indexOf("Weighted"));
                                            if(columnObject.name.indexOf("Weighted")>=0){
                                                var bookingTotal = rowEntity[columnObject.name.replace('Weighted','')];
                                                console.log('bookingTotal--'+bookingTotal);
                                                if(colDef.name == 'mprob' && rowEntity['mprob'] != ''){
                                                    var mgrPercent = parseInt(rowEntity['mprob'].replace('%',''));
                                                    rowEntity[columnObject.name] = bookingTotal*(mgrPercent/100);
                                                }else if((colDef.name == 'prob' || colDef.name == 'dprob') && rowEntity['prob'] != '' && rowEntity['dprob'] != ''){
                                                    var forecastPercentage = rowEntity['prob'].replace('%','');
                                                    var dealPercentage = rowEntity['dprob'].replace('%','')
                                                    console.log('Inline edit else'+forecastPercentage+'--'+dealPercentage);
                                                    Visualforce.remoting.Manager.invokeAction(
                                                        '{!$RemoteAction.FC_ProductCategoryControllerCopy.getEMEAProbability}',
                                                            forecastPercentage,
                                                            dealPercentage,
                                                            function(result, event){
                                                                console.log('Inline edit'+event);
                                                                console.log('Inline edit'+result);
                                                                                     
                                                                if (event.status) {
                                                                    rowEntity[columnObject.name] = bookingTotal*(parseInt(result)/100); 
                                                                    rowEntity['forecastAmount'] = rowEntity['gqa']*(parseInt(result)/100);  
                                                                } else if (event.type === 'exception') {
                                                                    $('#status').text(event.message);
                                                                    $("#status" ).addClass("alert alert-danger");
                                                                } 
                                                                                 
                                                            }, 
                                                            {escape: true}
                                                    );
                                                }
                                            }   
                                        });
                                     }else{
                                        console.log('colDef.name else'+colDef.name+'--test');
                                     }
                                     
                                     $scope.msg.lastCellEdited = 'edited row id:' +
                                         rowEntity.oppid +
                                         ' Column:' +
                                         colDef.name +
                                         ' newValue:' +
                                         newValue +
                                         ' oldValue:' +
                                         oldValue;
                                     $scope.$apply();
                                 });
            
                         $scope.gridApi.grouping.on
                             .aggregationChanged(
                                 $scope,
                                 function(col) {
                                     if (col.treeAggregation.type) {
                                         $scope.lastChange = col.displayName +
                                             ' aggregated using ' +
                                             col.treeAggregation.type;
                                     } else {
                                         $scope.lastChange = 'Aggregation removed from ' +
                                             col.displayName;
                                     }
                                 });
            
                         $scope.gridApi.grouping.on
                             .groupingChanged(
                                 $scope,
                                 function(col) {
                                     if (col.grouping.groupPriority) {
                                         $scope.lastChange = col.displayName +
                                             ' grouped with priority ' +
                                             col.grouping.groupPriority;
                                     } else {
                                         $scope.lastChange = col.displayName +
                                             ' removed from grouped columns';
                                     }
                                 });
            
                     }
                 };
                 
                $scope.exportAsCSV = function(){
                    $scope.gridOptions.exporterCsvFilename = getExportFileName();
                    $scope.gridApi.exporter.csvExport( 'all', 'visible');
                };
            
             }
            ])
            .controller('myCustomModalCtrl', function( $scope, $compile, $timeout ) {
              var $elm;
              
              $scope.showAgeModal = function() {
                $scope.listOfAOwners = [];
                
                $scope.col.grid.appScope.gridOptions.data.forEach( function ( row ) {
                  if ( $scope.listOfAOwners.indexOf( row.accountowner ) === -1 ) {
                    $scope.listOfAOwners.push( row.accountowner );
                  }
                });
                $scope.listOfAOwners.sort();
                
                $scope.gridOptions = { 
                  data: [],
                  enableColumnMenus: false,
                  onRegisterApi: function( gridApi) {
                    $scope.gridApi = gridApi;
                    
                    if ( $scope.colFilter && $scope.colFilter.listTerm ){
                      $timeout(function() {
                        $scope.colFilter.listTerm.forEach( function( accountowner ) {
                          var entities = $scope.gridOptions.data.filter( function( row ) {
                            return row.accountowner === accountowner;
                          }); 
                          
                          if( entities.length > 0 ) {
                            $scope.gridApi.selection.selectRow(entities[0]);
                          }
                        });
                      });
                    }
                  } 
                };
                
                $scope.listOfAOwners.forEach(function( accountowner ) {
                  $scope.gridOptions.data.push({accountowner: accountowner});
                });
                
                var html = '<div class="modal" ng-style="{display: \'block\'}"><div class="modal-dialog modal-sm"><div class="modal-content"><div class="modal-header">Filter Account Owners</div><div class="modal-body"><div id="grid1" ui-grid="gridOptions" ui-grid-selection class="modalGrid"></div></div><div class="modal-footer"><button id="buttonClose" class="btn btn-primary" ng-click="close()">Filter</button></div></div></div></div>';
                $elm = angular.element(html);
                angular.element(document.body).prepend($elm);
             
                $compile($elm)($scope);
                
              };
              
              $scope.close = function() {
                var accountowners = $scope.gridApi.selection.getSelectedRows();
                $scope.colFilter.listTerm = [];
                
                accountowners.forEach( function( accountowner ) {
                  $scope.colFilter.listTerm.push( accountowner.accountowner );
                });
                
                $scope.colFilter.term = $scope.colFilter.listTerm.join(', ');
                $scope.colFilter.condition = new RegExp($scope.colFilter.listTerm.join('|'));
                
                if ($elm) {
                  $elm.remove();
                }
              };
            })            
            .directive('myCustomModal', function() {
              return {
                template: '<label>{{colFilter.term}}</label><button ng-click="showAgeModal()">...</button>',
                controller: 'myCustomModalCtrl'
              };
            })
            
            
            .controller('myCustomModalOppownerCtrl', function( $scope, $compile, $timeout ) {
              var $elm;
              
              $scope.showAgeModal = function() {
                $scope.listOfOOwners = [];
                
                $scope.col.grid.appScope.gridOptions.data.forEach( function ( row ) {
                  if ( $scope.listOfOOwners.indexOf( row.oppowner ) === -1 ) {
                    $scope.listOfOOwners.push( row.oppowner );
                  }
                });
                $scope.listOfOOwners.sort();
                
                $scope.gridOptions = { 
                  data: [],
                  enableColumnMenus: false,
                  onRegisterApi: function( gridApi) {
                    $scope.gridApi = gridApi;
                    
                    if ( $scope.colFilter && $scope.colFilter.listTerm ){
                      $timeout(function() {
                        $scope.colFilter.listTerm.forEach( function( oppowner ) {
                          var entities = $scope.gridOptions.data.filter( function( row ) {
                            return row.oppowner === oppowner;
                          }); 
                          
                          if( entities.length > 0 ) {
                            $scope.gridApi.selection.selectRow(entities[0]);
                          }
                        });
                      });
                    }
                  } 
                };
                
                $scope.listOfOOwners.forEach(function( oppowner ) {
                  $scope.gridOptions.data.push({oppowner: oppowner});
                });
                
                var html = '<div class="modal" ng-style="{display: \'block\'}"><div class="modal-dialog modal-sm"><div class="modal-content"><div class="modal-header">Filter Oppty Owners</div><div class="modal-body"><div id="grid1" ui-grid="gridOptions" ui-grid-selection class="modalGrid"></div></div><div class="modal-footer"><button id="buttonClose" class="btn btn-primary" ng-click="close()">Filter</button></div></div></div></div>';
                $elm = angular.element(html);
                angular.element(document.body).prepend($elm);
             
                $compile($elm)($scope);
                
              };
              
              $scope.close = function() {
                var oppowners = $scope.gridApi.selection.getSelectedRows();
                $scope.colFilter.listTerm = [];
                
                oppowners.forEach( function( oppowner ) {
                  $scope.colFilter.listTerm.push( oppowner.oppowner );
                });
                
                $scope.colFilter.term = $scope.colFilter.listTerm.join(', ');
                $scope.colFilter.condition = new RegExp($scope.colFilter.listTerm.join('|'));
                
                if ($elm) {
                  $elm.remove();
                }
              };
            })            
            .directive('myCustomModalOppowner', function() {
              return {
                template: '<label>{{colFilter.term}}</label><button ng-click="showAgeModal()">...</button>',
                controller: 'myCustomModalOppownerCtrl'
              };
            })
            
            
            .controller('customModelSownerCtrl', function( $scope, $compile, $timeout ) {
              var $elm;
              
              $scope.showAgeModal = function() {
                $scope.listOfSOwners = [];
                
                $scope.col.grid.appScope.gridOptions.data.forEach( function ( row ) {
                  if ( $scope.listOfSOwners.indexOf( row.strategic_owner ) === -1 ) {
                    $scope.listOfSOwners.push( row.strategic_owner );
                  }
                });
                $scope.listOfSOwners.sort();
                
                $scope.gridOptions = { 
                  data: [],
                  enableColumnMenus: false,
                  onRegisterApi: function( gridApi) {
                    $scope.gridApi = gridApi;
                    
                    if ( $scope.colFilter && $scope.colFilter.listTerm ){
                      $timeout(function() {
                        $scope.colFilter.listTerm.forEach( function( strategic_owner ) {
                          var entities = $scope.gridOptions.data.filter( function( row ) {
                            return row.strategic_owner === strategic_owner;
                          }); 
                          
                          if( entities.length > 0 ) {
                            $scope.gridApi.selection.selectRow(entities[0]);
                          }
                        });
                      });
                    }
                  } 
                };
                
                $scope.listOfSOwners.forEach(function( strategic_owner ) {
                  $scope.gridOptions.data.push({strategic_owner: strategic_owner});
                });
                
                var html = '<div class="modal" ng-style="{display: \'block\'}"><div class="modal-dialog modal-sm"><div class="modal-content"><div class="modal-header">Filter Account Owners</div><div class="modal-body"><div id="grid1" ui-grid="gridOptions" ui-grid-selection class="modalGrid"></div></div><div class="modal-footer"><button id="buttonClose" class="btn btn-primary" ng-click="close()">Filter</button></div></div></div></div>';
                $elm = angular.element(html);
                angular.element(document.body).prepend($elm);
             
                $compile($elm)($scope);
                
              };
              
              $scope.close = function() {
                var strategic_owners = $scope.gridApi.selection.getSelectedRows();
                $scope.colFilter.listTerm = [];
                
                strategic_owners.forEach( function( strategic_owner ) {
                  $scope.colFilter.listTerm.push( strategic_owner.strategic_owner );
                });
                
                $scope.colFilter.term = $scope.colFilter.listTerm.join(', ');
                $scope.colFilter.condition = new RegExp($scope.colFilter.listTerm.join('|'));
                
                if ($elm) {
                  $elm.remove();
                }
              };
            })            
            .directive('customModelSowner', function() {
              return {
                template: '<label>{{colFilter.term}}</label><button ng-click="showAgeModal()">...</button>',
                controller: 'customModelSownerCtrl'
              };
            })
            
            

            .service(
            'stats',
            function() {
            
            var coreAccumulate = function(aggregation, value) {
             initAggregation(aggregation);
             if (angular.isUndefined(aggregation.stats.accumulator)) {
                 aggregation.stats.accumulator = [];
             }
             if (!isNaN(value)) {
                 aggregation.stats.accumulator.push(value);
             }
            };
            
            var initAggregation = function(aggregation) {
             /* To be used in conjunction with the cleanup finalizer */
             if (angular.isUndefined(aggregation.stats)) {
                 aggregation.stats = {
                     sum: 0
                 };
             }
            };
            
            var increment = function(obj, prop) {
             /*
              * if the property on obj is undefined, sets to 1,
              * otherwise increments by one
              */
             if (angular.isUndefined(obj[prop])) {
                 obj[prop] = 1;
             } else {
                 obj[prop]++;
             }
            };
            
            var service = {
             aggregator: {
                 accumulate: {
                     /*
                      * This is to be used with the uiGrid
                      * customTreeAggregationFn definition, to
                      * accumulate all of the data into an array for
                      * sorting or other operations by
                      * customTreeAggregationFinalizerFn In general
                      * this strategy is not the most efficient way
                      * to generate grouped statistics, but sometime
                      * is the only way.
                      */
                     numValue: function(aggregation, fieldValue,
                         numValue) {
                         return coreAccumulate(aggregation, numValue);
                     },
                     fieldValue: function(aggregation, fieldValue) {
                         return coreAccumulate(aggregation,
                             fieldValue);
                     }
                 },
                 mode: function(aggregation, fieldValue) {
                     initAggregation(aggregation);
                     var thisValue = fieldValue;
                     if (angular.isUndefined(thisValue) ||
                         thisValue === null) {
                         thisValue = aggregation.col.grid.options.groupingNullLabel;
                     }
                     increment(aggregation.stats, thisValue);
                     if (aggregation.stats[thisValue] > aggregation.maxCount ||
                         angular
                         .isUndefined(aggregation.maxCount)) {
                         aggregation.maxCount = aggregation.stats[thisValue];
                         aggregation.value = thisValue;
                     }
                 },
                 sumSquareErr: function(aggregation, fieldValue,
                     numValue) {
                     initAggregation(aggregation);
                     if (!isNaN(numValue)) {
                         increment(aggregation.stats, 'count');
                     }
                     aggregation.stats.sum += numValue || 0;
                     service.aggregator.accumulate.numValue(
                         aggregation, fieldValue, numValue);
                 }
             },
             finalizer: {
                 cleanup: function(aggregation) {
                     delete aggregation.stats;
                     if (angular.isUndefined(aggregation.rendered)) {
                         aggregation.rendered = aggregation.value;
                     }
                 },
                 median: function(aggregation) {
                     aggregation.stats.accumulator.sort();
                     var arrLength = aggregation.stats.accumulator.length;
                     aggregation.value = arrLength % 2 === 0 ? (aggregation.stats.accumulator[(arrLength / 2) - 1] + aggregation.stats.accumulator[(arrLength / 2)]) / 2 :
                         aggregation.stats.accumulator[(arrLength / 2) | 0];
                     service.finalizer.cleanup(aggregation);
                 },
                 sumSquareErr: function(aggregation) {
                     aggregation.value = 0;
                     if (aggregation.count !== 0) {
                         var mean = aggregation.stats.sum /
                             aggregation.stats.count,
                             error;
            
                         angular.forEach(
                             aggregation.stats.accumulator,
                             function(value) {
                                 error = value - mean;
                                 aggregation.value += error *
                                     error;
                             });
                     }
                 },
                 variance: function(aggregation) {
                     service.finalizer.sumSquareErr(aggregation);
                     aggregation.value = aggregation.value /
                         aggregation.stats.count;
                     service.finalizer.cleanup(aggregation);
                     aggregation.rendered = Math
                         .round(aggregation.value * 100) / 100;
                 },
                 varianceP: function(aggregation) {
                     service.finalizer.sumSquareErr(aggregation);
                     if (aggregation.count !== 0) {
                         aggregation.value = aggregation.value /
                             (aggregation.stats.count - 1);
                     }
                     service.finalizer.cleanup(aggregation);
                 },
                 stDev: function(aggregation) {
                     service.finalizer.variance(aggregation);
                     aggregation.value = Math
                         .sqrt(aggregation.value);
                     aggregation.rendered = Math
                         .round(aggregation.value * 100) / 100;
                 },
                 stDevP: function(aggregation) {
                     service.finalizer.varianceP(aggregation);
                     aggregation.value = Math
                         .sqrt(aggregation.value);
                     aggregation.rendered = Math
                         .round(aggregation.value * 100) / 100;
                 }
             },
            };
            
            return service;
            })
            
            
            
            function StringSet() {
                var setObj = {},
                val = {};
                
                this.add = function(str) {
                setObj[str] = val;
                };
                
                this.contains = function(str) {
                return setObj[str] === val;
                };
                
                this.remove = function(str) {
                delete setObj[str];
                };
                
                this.values = function() {
                var values = [];
                for (var i in setObj) {
                 if (setObj[i] === val) {
                     values.push(i);
                 }
                }
                return values;
                };
            }
         </script>
      </head>
      <body>
         <div class="blueflame">
         <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    {!if($currentpage.parameters.productCategories == "true","Opportunity Forecast Report For Quotes With Selected Products Only","Opportunity Forecast Report by Product Categories")}
                    <button type="button" class="btn btn-link btn-sm" id="showButton">Show Filters</button>
                    <button type="button" class="btn btn-link btn-sm" id="hideButton">Hide Filters</button>
                    <select class="chosen-select"  placeholder="Select Options" tabindex="-1" id="userViews" style="width:25%;">
                        <apex:repeat value="{!userViews}" var="viewOption">
                            <option value="{!viewOption.value}" >{!viewOption.label}</option>
                        </apex:repeat>
                    </select>
                    <button type="button" class="btn btn-link btn-sm" id="editView">Edit</button>|
                    <button type="button" class="btn btn-link btn-sm" id="deleteView">Delete</button>|
                    <button type="button" class="btn btn-link btn-sm" id="newView" data-toggle="modal">Create New View</button>
                </h3>
            </div>
            <div class="panel-body">
               <div ng-controller="MainCtrl" id="mainController">
                    <input id="region" type="hidden" value="{!$currentpage.parameters.region}"/>    
                    <input id="fcViewColumns" type="hidden" value=""/>   
                        <div class="row" id="filters">
                        <div class="col-md-6" id="filters1">
                        <table>
                        <tr><td style="padding:5px;"><b>Expected Close Date</b></td>
                        <td style="padding:5px;"><select  class="chosen-select" tabindex="-1" id="escpectedClosedDate" style="width:50%;">
                               <optgroup label="Fiscal Quarter">
                                  <option value="custom">Custom</option>
                                  <option value="THIS_FISCAL_QUARTER" selected="selected">Current FQ</option>
                                  <option value="LAST_FISCAL_QUARTER">Previous FQ</option>
                                  <option value="fytd">FYTD</option>
                                  <option value="LAST_FISCAL_YEAR">Last Fiscal Year</option>
                                  <option value="NEXT_FISCAL_QUARTER">Next FQ</option>
                                  <option value="curprev1">Current and Previous FQ</option>
                                  <option value="curnext1">Current and Next FQ</option>
                                  <option value="curlast3">Current and Last 3 FQ</option>
                                  <option value="curnext3">Current and Next 3 FQ</option>
                               </optgroup>
                            </select>
                            <font color="blue"><b>Note: Select "Custom" to choose from dates dropdown.</b></font>
                        </td>
                        </tr>
                        
                        <tr><td style="padding:5px;"><b>Expected Close Date Range</b></td>
                        <td style="padding:5px;"><div id="datepicker_div">
                                <div class="input-daterange input-group" id="datepicker">
                                       <input type="text" style="text-alin:left;" class="input-sm form-control" name="start" id="sdate" disabled="true"/>
                                       <span class="input-group-addon">to</span>
                                       <input type="text" style="text-alin:left;" class="input-sm form-control" name="end" id="edate" disabled="true"/>
                                 </div>
                             </div>
                        </td>
                        </tr>
                        
                        <tr><td style="padding:5px;"><b>Sub Region</b></td>
                        <td style="padding:5px;">
                            <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="subRegion">
                                <apex:repeat value="{!regions['Americas']}" var="region">
                                   <optgroup label="{!region}">
                                        <apex:repeat value="{!regions['Americas'][region]}" var="subRegion">        
                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                        </apex:repeat>
                                   </optgroup>
                                </apex:repeat>
                            </select>
                            <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="emeaRegions">
                                <apex:repeat value="{!regions['EMEA']}" var="region">
                                   <optgroup label="{!region}">
                                        <apex:repeat value="{!regions['EMEA'][region]}" var="subRegion">        
                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                        </apex:repeat>
                                   </optgroup>
                                </apex:repeat>
                            </select>
                            <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="apacRegions">
                                <apex:repeat value="{!regions['APAC']}" var="region">
                                   <optgroup label="{!region}">
                                        <apex:repeat value="{!regions['APAC'][region]}" var="subRegion">        
                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                        </apex:repeat>
                                   </optgroup>
                                </apex:repeat>
                            </select>
                        </td>
                        </tr>
                        
                        
                        <tr><td style="padding:5px;"><b>Primary Quote Types</b></td>
                        <td style="padding:5px;"><select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="pqt">
                                  <option value="Sales" selected="selected">Sales</option>
                                  <option value="Service">Service</option>
                                  <option value="Combined">Combined</option>
                                  <option value="All">All</option>
                            </select>
                        </td>
                        </tr>
                        <tr>
                            <td style="padding:5px;"><b>Net Sales Booking Amt</b></td> 
                            <td style="padding:5px;" id="integerForm"><div class="input-group" style="width:80%;">
                                <span class="input-group-addon">$</span>
                                <input id="snba" type="number" class="form-control" />
                                <span class="input-group-addon">to</span>
                                <input id="enba" type="number" class="form-control" oncomplete ="return formatNumber(this.value)"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding:5px;"><b>ISS Transfer</b></td>
                            <td style="padding:5px;">
                                <div style="float:left;margin-right:10px;">
                                    <p class="form-control-static" id="issTransferAmount"></p>
                                </div>
                                <div style="float:left;margin-right:10px;margin-top:4px;">
                                    <label class="switch">
                                      <input type="checkbox" id="includeISSTransferAmount" checked="true"/>
                                      <div class="slider round"></div>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding:5px;"><b>Site Solutions</b></td>
                            <td style="padding:5px;">
                                <div style="float:left;margin-right:10px;">
                                    <p class="form-control-static" id="siteSolutionsAmount"></p>
                                </div>
                                <div style="float:left;margin-right:10px;margin-top:4px;">
                                    <label class="switch">
                                      <input type="checkbox" id="includeSiteSolutionsAmount" checked="true"/>
                                      <div class="slider round"></div>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding:5px;"><b>Brachy Applicators</b></td>
                            <td style="padding:5px;">
                                <div style="float:left;margin-right:10px;">
                                    <p class="form-control-static" id="brachyForecastAmount"></p>
                                </div>
                                <div style="float:left;margin-right:10px;margin-top:4px;">
                                    <label class="switch">
                                      <input type="checkbox" id="includeBrachyForecastAmount" checked="true"/>
                                      <div class="slider round"></div>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        </table>
                        </div>
                        <div class="col-md-6" id="filters2">
                        <table id="categoryFilters">
                            
                            <tr><td style="padding:5px;"><b>Stage</b></td>
                                <td style="padding:5px;">
                                    <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="oppStage">
                                        <apex:repeat value="{!opportunityStageValues}" var="stageValue">
                                            <option value="{!stageValue.value}" >{!stageValue.label}</option>
                                        </apex:repeat>
                                    </select>
                                </td>
                            </tr>
                                            
                            <tr><td style="padding:5px;"><b>{!if($CurrentPage.parameters.region=='EMEA','Forecast Percentage','Probability')}</b></td>
                            <td style="padding:5px;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <select  class="chosen-select" tabindex="-1" id="probabilityOperator">
                                            <option value="">--NONE--</option>
                                            <option value="e">Equals</option>
                                            <option value="n">Not Equal to</option>
                                            <option value="l">Less than</option>
                                            <option value="g">Greater than</option>
                                            <option value="le">Less or Equal</option>
                                            <option value="ge">Greater or Equal</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">                                               
                                        <select  class="chosen-select" tabindex="-1" id="probability">
                                            <apex:repeat value="{!probabilities}" var="probability">
                                                <option value="{!probability.value}" >{!probability.label}</option>
                                            </apex:repeat>
                                        </select>
                                    </div>
                                </div>
                            </td>
                            </tr>
                            
                            <tr class="dealProbability"><td style="padding:5px;"><b>Deal Probability - ROW</b></td>
                                <td style="padding:5px;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <select  class="chosen-select" tabindex="-1" id="dealProbabilityOperator">
                                                 <option value="">--NONE--</option>
                                                 <option value="e">Equals</option>
                                                 <option value="n">Not Equal to</option>
                                                 <option value="l">Less than</option>
                                                 <option value="g">Greater than</option>
                                                 <option value="le">Less or Equal</option>
                                                 <option value="ge">Greater or Equal</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <select  class="chosen-select" tabindex="-1" id="dealProbability">
                                                 <apex:repeat value="{!dealProbabilities}" var="probability">
                                                     <option value="{!probability.value}" >{!probability.label}</option>
                                                 </apex:repeat>
                                            </select>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr><td style="padding:5px;"><b>Family</b></td>
                            <td style="padding:5px;">
                                <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="families">
                                    <apex:repeat value="{!productLineOptions}" var="familyValue">
                                        <option value="{!familyValue}" >{!familyValue}</option>
                                    </apex:repeat>
                                </select>
                            </td>
                            </tr>
                            
                            <tr><td style="padding:5px;"><b>Product Line</b></td>
                            <td style="padding:5px;">
                                  <apex:outputpanel id="lineOptionsPanel">
                                    <!--  <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productLines"  onchange="getModels('true');">-->
                                    <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productLines">
                                          <apex:repeat value="{!productLineOptions}" var="family" id="lineOptionsRepeat">
                                           <optgroup label="{!family}">
                                                <apex:repeat value="{!productLineOptions[family]}" var="line">
                                                    <option value="{!family+line}" >{!line}</option>
                                                </apex:repeat>
                                           </optgroup>
                                        </apex:repeat>
                                    </select>
                                   </apex:outputpanel> 
                                
                            </td>
                            </tr>
                            
                            <tr><td style="padding:5px;"><b>Models</b></td>
                            <td style="padding:5px;">
                                <apex:outputpanel id="modelOptionsPanel">
                                    <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productModels">
                                        <apex:repeat value="{!modelOptions}" var="line">
                                           <optgroup label="{!line}">
                                                <apex:repeat value="{!modelOptions[line]}" var="model">     
                                                    <option value="{!model}" >{!model}</option>
                                                </apex:repeat>
                                           </optgroup>
                                        </apex:repeat>
                                    </select>
                                </apex:outputpanel>
                            </td>
                            </tr>
                            <tr>
                                <td style="padding:5px;"><b>Include Amendment Quotes</b></td>
                                <td style="padding:5px;">
                                    <div style="float:left;margin-right:10px;margin-top:4px;">
                                        <label class="switch">
                                          <input type="checkbox" id="includeAmendmentQuotes"/>
                                          <div class="slider round"></div>
                                        </label>
                                    </div>
                                </td>
                            </tr>
                        </table>
                        </div>
                        </div>
                        
                        
                        
                    <script type="text/javascript">
                        
                        $(document).ready(function(){
                        
                            var regionVal = "{!$currentpage.parameters.region}";
                        
                            $('#multiselect').multiselect();
                            
                            $("#showButton").hide();
                            $("#errorMessage").hide();
                            var filtersHeight = $("#filters").height();
                            var gridHeight = $(window).height() - filtersHeight;
                            $(".grid").css("height",gridHeight+'px');
                            $("#hideButton").click(function(){
                                $("#filters").hide();
                                $("#filterButtons").hide();
                                $("#status").hide();
                                $("#hideButton").hide();
                                $("#showButton").show();
                                $(".grid").css("height",($(window).height()-100)+'px');
                                $(window).trigger('resize');
                            });
                            $("#showButton").click(function(){
                                
                                $("#filters").show();
                                $("#filterButtons").show();
                                $("#status").show();
                                $("#hideButton").show();
                                $("#showButton").hide();
                                $(".grid").css("height",gridHeight +'px');
                                $(window).trigger('resize');
                            });
                            
                            $("#userViews").change(function(){
                                updateForecastFilters();
                            });
                            
                            $("#escpectedClosedDate").change(function(){
                                if($('#escpectedClosedDate').val() == 'custom'){
                                    $("#sdate").removeAttr("disabled");
                                    $("#edate").removeAttr("disabled");
                                }else{
                                    $("#sdate").attr("disabled","true");
                                    $("#edate").attr("disabled","true");
                                    updateExpectedCloseDateRange('');
                                }
                                getForecastManualAdjustments();
                            });
                            
                            $("#escpectedClosedDateView").change(function(){
                                if($('#escpectedClosedDateView').val() == 'custom'){
                                    $("#sdateView").removeAttr("disabled");
                                    $("#edateView").removeAttr("disabled");
                                }else{
                                    $("#sdateView").attr("disabled","true");
                                    $("#edateView").attr("disabled","true");
                                    updateExpectedCloseDateRange('View');
                                }
                            });
                            
                            if(regionVal == 'EMEA'){
                                $("#includeISSTransferAmount").removeAttr("checked");
                                $("#includeSiteSolutionsAmount").removeAttr("checked");
                                $("#includeBrachyForecastAmount").removeAttr("checked");
                            }
                            
                            $("#newView").click(function(){
                                $("#forecastViewId").val("");
                                
                                clearForecastViewModal();
                                
                                for(i = $("#multiselect_to").children('option').length - 1 ; i >= 0 ; i--){
                                    $("#multiselect").append("<option value='"+$('#multiselect_to option:eq(' + i + ')').val()+"'>"+$('#multiselect_to option:eq(' + i + ')').text()+"</option>");
                                    $('#multiselect_to option:eq(' + i + ')').remove();
                                }
                                updateExpectedCloseDateRange('View');
                                $("#forecastViewModal").modal();
                            });
                            
                            
                            $("#editView").click(function(){
                                
                                clearForecastViewModal();
                                
                                if($('#userViews').val() === null){
                                    $('#status').text("Please select/create a view to edit");
                                    $("#status" ).addClass("alert alert-danger");
                                    event.preventDefault();
                                    event.stopPropagation();
                                }else{
                                    //var regionVal = "{!$currentpage.parameters.region}";
                                    var selectedView = JSON.parse($('#userViews').val());
                                    
                                    $("#forecastViewId").val(selectedView.Id);
                                    $("#viewName").val(selectedView.Name);
                                    //setSelectByValue('pqtView',selectedView.Quote_Type__c);
                                    setSelectByValue('escpectedClosedDateView',selectedView.Excepted_Close_Date_Range__c);
                                    $("#escpectedClosedDateView").trigger("chosen:updated");
                                    
                                    setSelectByValue('probabilityOperatorView',selectedView.Probability_Operator__c);
                                    $("#probabilityOperatorView").trigger("chosen:updated");
                                    setSelectByValue('probabilityView',selectedView.Probability__c);
                                    $("#probabilityView").trigger("chosen:updated");
                                    
                                    setSelectByValue('dealProbabilityOperatorView',selectedView.Deal_Probability_Operator__c);
                                    $("#dealProbabilityOperatorView").trigger("chosen:updated");
                                    setSelectByValue('dealProbabilityView',selectedView.Deal_Probability__c);
                                    $("#dealProbabilityView").trigger("chosen:updated");
                                    
                                    $("#defaultView").prop("checked",selectedView.Default_View__c);
                                    
                                    if(selectedView.Quote_Type__c !== undefined){
                                        console.log('----selectedView.Quote_Type__c'+selectedView.Quote_Type__c);
                                        var quoteTypeViewArray = selectedView.Quote_Type__c.toString().split(',');
                                        console.log('quoteTypeViewArray---'+quoteTypeViewArray);
                                        $.each( quoteTypeViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                setSelectByValue('pqtView',value);
                                                $("#pqtView").trigger("chosen:updated");
                                            }
                                        });
                                    }
                                    
                                    if(selectedView.Sub_Region__c !== undefined){
                                        console.log('----selectedView.Sub_Region__c'+selectedView.Sub_Region__c);
                                        var srViewArray = selectedView.Sub_Region__c.toString().split(',');
                                        console.log('srViewArray123---'+srViewArray);
                                        $.each( srViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                if(regionVal == 'Americas'){                                    
                                                    setSelectByValue('subRegionView',value);
                                                    $("#subRegionView").trigger("chosen:updated");
                                                }else if(regionVal == 'EMEA'){
                                                    setSelectByValue('emeaRegionsView',value);
                                                    $("#emeaRegionsView").trigger("chosen:updated");
                                                }else if(regionVal == 'APAC'){
                                                    setSelectByValue('apacRegionsView',value);
                                                    $("#apacRegionsView").trigger("chosen:updated");
                                                }
                                            }
                                        });
                                    }
                                    
                                    if(selectedView.Stage__c !== undefined){
                                        console.log('selectedView.Stage__c---'+selectedView.Stage__c);
                                        var stageViewArray = selectedView.Stage__c.split(',');
                                        console.log('stageViewArray---'+stageViewArray);
                                        $.each( stageViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                setSelectByValue('oppStageView',value);
                                                $("#oppStageView").trigger("chosen:updated");
                                            }
                                        });
                                    }
                                    
                                    if(selectedView.Product_Families__c !== undefined){
                                        console.log('selectedView.Product_Families__c---'+selectedView.Product_Families__c);
                                        var familyViewArray = selectedView.Product_Families__c.split(',');
                                        console.log('familyViewArray123---'+familyViewArray);
                                        $.each( familyViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                setSelectByValue('familiesView',value);
                                                $("#familiesView").trigger("chosen:updated");
                                            }
                                        });
                                    }
                                    
                                    if(selectedView.Product_Lines__c !== undefined){
                                        var lineViewArray = selectedView.Product_Lines__c.split(',');
                                        console.log(lineViewArray);
                                        $.each( lineViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                setSelectByValue('productLinesView',value);
                                                $("#productLinesView").trigger("chosen:updated");
                                            }
                                        });
                                    }
                                    
                                    if(selectedView.Product_Models__c != undefined){
                                        var modelViewArray = selectedView.Product_Models__c.split(',');
                                        console.log(modelViewArray);
                                        $.each( modelViewArray, function( key, value ) {
                                            if(value === ''){
                                            }else{
                                                setSelectByValue('productModelsView',value);
                                                $("#productModelsView").trigger("chosen:updated");
                                            }
                                        });
                                    }
                                    
                                    //$('#sdateView').val(selectedView.Excepted_Close_Date_Start__c);
                                    //$('#edateView').val(selectedView.Excepted_Close_Date_End__c); 
                                    $('#enbaView').val(selectedView.Net_Booking_Amount_End__c);
                                    $('#snbaView').val(selectedView.Net_Booking_Amount_Start__c);
                                    
                                    $("#sdateView").datepicker({
                                        format: 'mm/dd/yyyy',
                                        autoclose: true
                                    }).datepicker("update", selectedView.Excepted_Close_Date_Start__c); 
                                    
                                    $("#edateView").datepicker({
                                        format: 'mm/dd/yyyy',
                                        autoclose: true
                                    }).datepicker("update", selectedView.Excepted_Close_Date_End__c);
                                    
                                    console.log('---selectedView.Forecast_Columns__c'+selectedView.Forecast_Columns__c);
                                    if(selectedView.Forecast_Columns__c != undefined){
                                        var viewColumns = selectedView.Forecast_Columns__c.split(',')
                                        
                                        for(i = $("#multiselect_to").children('option').length - 1 ; i >= 0 ; i--){
                                            $("#multiselect").append("<option value='"+$('#multiselect_to option:eq(' + i + ')').val()+"'>"+$('#multiselect_to option:eq(' + i + ')').text()+"</option>");
                                            $('#multiselect_to option:eq(' + i + ')').remove();
                                        }
                                        
                                        $.each(viewColumns, function( index, value ) {
                                            var textValue = $("#multiselect option[value='"+value+"']").text();
                                            console.log('----textValue'+textValue);
                                            $("#multiselect option[value='"+value+"']").remove();
                                            $("#multiselect_to").append("<option value='"+value+"'>"+textValue+"</option>");
                                        });
                                        
                                    }
                                    
                                    $("#forecastViewModal").modal();
                                }
                                
                            });
                            
                            $("#deleteView").click(function(){
                                if($('#userViews').val() === null){
                                    $('#status').text("Please select a view to delete");
                                    $("#status" ).addClass("alert alert-danger");
                                    event.preventDefault();
                                    event.stopPropagation();
                                }else{
                                    var selectedView = JSON.parse($('#userViews').val());
                                    //status
                                    $("#status" ).removeClass("alert alert-danger");
                                    $("#status" ).addClass("alert alert-info");
                                    $('#status').text('Processing.......'); 
                                    $.blockUI({ css: { 
                                        border: 'none', 
                                        padding: '15px', 
                                        backgroundColor: '#000', 
                                        '-webkit-border-radius': '10px', 
                                        '-moz-border-radius': '10px', 
                                        opacity: .5, 
                                        color: '#fff' 
                                    } });
                     
                                    Visualforce.remoting.Manager.invokeAction(
                                    '{!$RemoteAction.FC_ProductCategoryControllerCopy.deleteForecastView}',
                                    selectedView.Id,
                                    "{!$CurrentPage.parameters.region}",
                                    function(result, event){
                     
                                        if (event.status) {
                                            if(result){
                                                console.log('deleteView'+result+'----value2'+$("#userViews option:selected").val());
                                                $('#userViews option:eq(' + $("#userViews option:selected").index() + ')').remove();
                                                $("#userViews").trigger("chosen:updated");
                                                $("#status" ).addClass("alert alert-info");
                                                $('#status').text('View deleted!');
                                                $("#userViews").trigger("change");
                                            }else{
                                                $('#status').text('View list cannot be blank');
                                                $("#status" ).addClass("alert alert-danger");
                                                $.unblockUI();
                                            }
                                        } else if (event.type === 'exception') {
                                                $('#status').text(event.message);
                                                $("#status" ).addClass("alert alert-danger");
                                                $.unblockUI();
                                        } else {}
                                    }, 
                                    {escape: true}
                                    );
                                }
                            });
                            
                            var regionValue = "{!$currentpage.parameters.region}";
                            if(regionValue == 'Americas'){
                                $("#subRegion").show();
                                $("#subRegionView").show();
                                $("#emeaRegions").removeAttr("class");
                                $("#emeaRegionsView").removeAttr("class");
                                $("#apacRegions").removeAttr("class");
                                $("#apacRegionsView").removeAttr("class");
                                $("#emeaRegions").hide();
                                $("#emeaRegionsView").hide();
                                $("#apacRegions").hide();
                                $("#apacRegionsView").hide();
                                $('.dealProbability').hide();
                            }else if(regionValue == 'EMEA'){
                                $("#emeaRegions").show();
                                $("#emeaRegionsView").show();
                                $("#subRegion").removeAttr("class");
                                $("#subRegionView").removeAttr("class");
                                $("#apacRegions").removeAttr("class");
                                $("#apacRegionsView").removeAttr("class");
                                $("#subRegion").hide();
                                $("#subRegionView").hide();
                                $("#apacRegions").hide();
                                $("#apacRegionsView").hide();
                                $('.dealProbability').show();
                            }else if(regionValue == 'APAC'){
                                $("#emeaRegions").show();
                                $("#emeaRegionsView").show();
                                $("#subRegion").removeAttr("class");
                                $("#subRegionView").removeAttr("class");
                                $("#emeaRegions").removeAttr("class");
                                $("#emeaRegionsView").removeAttr("class");
                                $("#subRegion").hide();
                                $("#subRegionView").hide();
                                $("#emeaRegions").hide();
                                $("#emeaRegionsView").hide();
                                $('.dealProbability').show();
                            }
                            updateForecastFilters();
                        });
                        
                        
                      </script>
                     <br/>
                     
                     <div id="filterButtons" style="margin-bottom:10px;">
                         <button id="refreshButton" type="button" class="btn btn-success btn-xs" ng-click="refreshData()">Run Report</button>
                         <button id="saveButton" type="button" class="btn btn-primary btn-xs" ng-click="saveData()">Save</button> 
                         <button id="exportButton" type="button" class="btn btn-success btn-xs" ng-click="exportAsCSV()">Export</button>
                         <!--  <button id="saveFilterButton" type="button" class="btn btn-info btn-xs" ng-click="saveFilter()">Save Filter</button>-->
                     </div>
                     
                     <div id="status" role="alert"></div>
                     <div id="grid1" 
                        ui-grid="gridOptions" 
                        ui-grid-pinning="gridOptions"
                        ui-grid-grouping="gridOptions" 
                        ui-grid-edit="gridOptions" 
                        ui-grid-row-edit ="gridOptions"
                        ui-grid-resize-columns = "gridOptions"
                        ui-grid-selection="gridOptions" 
                        ui-grid-exporter="gridOptions" 
                        ui-grid-move-columns="gridOptions"
                        class="grid" style="width:100%;"></div>
                  </div>
                  <br/> 
                  <br/>
                  <div>
                  </div>
               </div>
            </div>
         </div>
         
         <!-- forecast view modal -->
                        
                        <div class="modal fade" id="forecastViewModal" role="dialog">
                            <div class="modal-dialog modal-lg">
                                <!-- Modal content-->
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        <h4 class="modal-title">Forecast View Edit</h4>
                                    </div>
                                    <div class="modal-body">
                                        <input id="forecastViewId" type="hidden" value=""/>
                                        <div class="alert alert-danger" id="errorMessage">
                                            
                                        </div>
                                        <div class="row" id="filtersView">
                                        <div class="col-md-6" id="filters1View">
                                        <table>
                                        <tr>
                                            <td style="padding:5px;"><b>View Name</b></td>
                                            <td style="padding:5px;">
                                                <input type="text" class="form-control" id="viewName"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="padding:5px;"><b>Default View</b></td>
                                            <td style="padding:5px;">
                                                <label class="switch">
                                                  <input type="checkbox" id="defaultView"/>
                                                  <div class="slider round"></div>
                                                </label>
                                            </td>
                                        </tr>
                                        <tr><td style="padding:5px;"><b>Expected Close Date</b></td>
                                        <td style="padding:5px;"><select  class="chosen-select" tabindex="-1" id="escpectedClosedDateView">
                                               <optgroup label="Fiscal Quarter">
                                                  <option value="custom">Custom</option>
                                                  <option value="THIS_FISCAL_QUARTER" selected="selected">Current FQ</option>
                                                  <option value="LAST_FISCAL_QUARTER">Previous FQ</option>
                                                  <option value="fytd">FYTD</option>
                                                  <option value="LAST_FISCAL_YEAR">Last Fiscal Year</option>
                                                  <option value="NEXT_FISCAL_QUARTER">Next FQ</option>
                                                  <option value="curprev1">Current and Previous FQ</option>
                                                  <option value="curnext1">Current and Next FQ</option>
                                                  <option value="curlast3">Current and Last 3 FQ</option>
                                                  <option value="curnext3">Current and Next 3 FQ</option>
                                               </optgroup>
                                            </select>
                                        </td>
                                        </tr>
                                        
                                        <tr><td style="padding:5px;"><b>Expected Close Date Range</b></td>
                                        <td style="padding:5px;"><div id="datepicker_div">
                                                <div class="input-daterange input-group" id="datepicker">
                                                       <input type="text" style="text-alin:left;" class="input-sm form-control" name="start" id="sdateView" disabled="true"/>
                                                       <span class="input-group-addon">to</span>
                                                       <input type="text" style="text-alin:left;" class="input-sm form-control" name="end" id="edateView" disabled="true"/>
                                                 </div>
                                             </div>
                                        </td>
                                        </tr>
                                        
                                        <tr><td style="padding:5px;"><b>Sub Region</b></td>
                                        <td style="padding:5px;">
                                            <select  class="form-control chosen-select" multiple="multiple" data-placeholder="Select Some Options" tabindex="-1"  id="subRegionView">
                                                <apex:repeat value="{!regions['Americas']}" var="region">
                                                   <optgroup label="{!region}">
                                                        <apex:repeat value="{!regions['Americas'][region]}" var="subRegion">        
                                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                                        </apex:repeat>
                                                   </optgroup>
                                                </apex:repeat>
                                            </select>
                                            <select  class="form-control chosen-select" multiple="multiple" data-placeholder="Select Some Options" tabindex="-1"  id="emeaRegionsView">
                                                <apex:repeat value="{!regions['EMEA']}" var="region">
                                                   <optgroup label="{!region}">
                                                        <apex:repeat value="{!regions['EMEA'][region]}" var="subRegion">        
                                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                                        </apex:repeat>
                                                   </optgroup>
                                                </apex:repeat>
                                            </select>
                                            <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="apacRegionsView">
                                                <apex:repeat value="{!regions['APAC']}" var="region">
                                                   <optgroup label="{!region}">
                                                        <apex:repeat value="{!regions['APAC'][region]}" var="subRegion">        
                                                            <option value="{!subRegion}" >{!if(subRegion==region,subRegion+'-All',subRegion)}</option>
                                                        </apex:repeat>
                                                   </optgroup>
                                                </apex:repeat>
                                            </select>
                                        </td>
                                        </tr>
                                        
                                        
                                        <tr><td style="padding:5px;"><b>Primary Quote Types</b></td>
                                        <td style="padding:5px;"><select class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="pqtView">
                                                  <option value="Sales" selected="selected">Sales</option>
                                                  <option value="Service">Service</option>
                                                  <option value="Combined">Combined</option>
                                                  <option value="All">All</option>
                                            </select>
                                        </td>
                                        </tr>
                                        <tr>
                                            <td style="padding:5px;"><b>Net Sales Booking Amt</b></td> 
                                            <td style="padding:5px;" id="integerForm"><div class="input-group" style="width:80%;">
                                                <span class="input-group-addon">$</span>
                                                <input id="snbaView" type="number" class="form-control" />
                                                <span class="input-group-addon">to</span>
                                                <input id="enbaView" type="number" class="form-control" oncomplete ="return formatNumber(this.value)"/>
                                                </div>
                                            </td>
                                        </tr>
                                         <tr>
                                            <td style="padding:5px;"></td>
                                            <td style="padding:5px;">
                                            </td>
                                        </tr>
                                        </table>
                                        </div>
                                        <div class="col-md-6" id="filters2View">
                                        <table id="categoryFiltersView">
                                            
                                            <tr><td style="padding:5px;"><b>Stage</b></td>
                                            <td style="padding:5px;">
                                                <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="oppStageView">
                                                    <apex:repeat value="{!OpportunityStageValues}" var="stageValue">
                                                        <option value="{!stageValue.value}" >{!stageValue.label}</option>
                                                    </apex:repeat>
                                                </select>
                                            </td>
                                            </tr>
                                            
                                            <tr><td style="padding:5px;"><b>{!if($CurrentPage.parameters.region == 'EMEA','Forecast Percentage','Probability')}</b></td>
                                            <td style="padding:5px;">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <select  class="chosen-select" tabindex="-1" id="probabilityOperatorView">
                                                            <option value="">--NONE--</option>
                                                            <option value="e">Equals</option>
                                                            <option value="n">Not Equal to</option>
                                                            <option value="l">Less than</option>
                                                            <option value="g">Greater than</option>
                                                            <option value="le">Less or Equal</option>
                                                            <option value="ge">Greater or Equal</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <select  class="chosen-select" tabindex="-1" id="probabilityView">
                                                            <apex:repeat value="{!probabilities}" var="probability">
                                                                <option value="{!probability.value}" >{!probability.label}</option>
                                                            </apex:repeat>
                                                        </select>
                                                    </div>
                                                </div>
                                            </td>
                                            </tr>
                                            
                                            <tr class="dealProbability"><td style="padding:5px;"><b>Deal Probability - ROW</b></td>
                                            <td style="padding:5px;">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <select  class="chosen-select" tabindex="-1" id="dealProbabilityOperatorView">
                                                            <option value="">--NONE--</option>
                                                            <option value="e">Equals</option>
                                                            <option value="n">Not Equal to</option>
                                                            <option value="l">Less than</option>
                                                            <option value="g">Greater than</option>
                                                            <option value="le">Less or Equal</option>
                                                            <option value="ge">Greater or Equal</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <select  class="chosen-select" tabindex="-1" id="dealProbabilityView">
                                                            <apex:repeat value="{!dealProbabilities}" var="probability">
                                                                <option value="{!probability.value}" >{!probability.label}</option>
                                                            </apex:repeat>
                                                        </select>
                                                    </div>
                                                </div>
                                            </td>
                                            </tr>
                                            
                                            <tr><td style="padding:5px;"><b>Family</b></td>
                                            <td style="padding:5px;">
                                                <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1" id="familiesView">
                                                    <apex:repeat value="{!productLineOptions}" var="familyValue">
                                                        <option value="{!familyValue}" >{!familyValue}</option>
                                                    </apex:repeat>
                                                </select>
                                            </td>
                                            </tr>
                                            
                                            <tr><td style="padding:5px;"><b>Product Line</b></td>
                                            <td style="padding:5px;">
                                                  <apex:outputpanel id="lineOptionsPanelView">
                                                    <!--  <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productLines"  onchange="getModels('true');">-->
                                                    <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productLinesView">
                                                          <apex:repeat value="{!productLineOptions}" var="family">
                                                           <optgroup label="{!family}">
                                                                <apex:repeat value="{!productLineOptions[family]}" var="line">
                                                                    <option value="{!family+line}" >{!line}</option>
                                                                </apex:repeat>
                                                           </optgroup>
                                                        </apex:repeat>
                                                    </select>
                                                   </apex:outputpanel> 
                                                
                                            </td>
                                            </tr>
                                            
                                            <tr><td style="padding:5px;"><b>Models</b></td>
                                            <td style="padding:5px;">
                                                <apex:outputpanel id="modelOptionsPanelView">
                                                    <select  class="form-control chosen-select" multiple="multiple" placeholder="Select Options" tabindex="-1"  id="productModelsView">
                                                        <apex:repeat value="{!modelOptions}" var="line">
                                                           <optgroup label="{!line}">
                                                                <apex:repeat value="{!modelOptions[line]}" var="model">     
                                                                    <option value="{!model}" >{!model}</option>
                                                                </apex:repeat>
                                                           </optgroup>
                                                        </apex:repeat>
                                                    </select>
                                                </apex:outputpanel>
                                            </td>
                                            </tr>
                                            
                                            <tr>
                                                <td style="padding:5px;" valign="top"><b>Report Columns</b></td>
                                                <td style="padding:5px;">
                                                    <div class="row">
                                                        <div class="col-sm-5">
                                                            <select name="from[]" id="multiselect" class="form-control" size="8" multiple="multiple">
                                                                <apex:repeat value="{!forecastColumns}" var="column">
                                                                    <option value="{!column.value}">{!column.label}</option>
                                                                </apex:repeat>
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="col-sm-2">
                                                            <button type="button" id="multiselect_rightAll" class="btn btn-block"><i class="glyphicon glyphicon-forward"></i></button>
                                                            <button type="button" id="multiselect_rightSelected" class="btn btn-block"><i class="glyphicon glyphicon-chevron-right"></i></button>
                                                            <button type="button" id="multiselect_leftSelected" class="btn btn-block"><i class="glyphicon glyphicon-chevron-left"></i></button>
                                                            <button type="button" id="multiselect_leftAll" class="btn btn-block"><i class="glyphicon glyphicon-backward"></i></button>
                                                        </div>
                                                        
                                                        <div class="col-sm-5">
                                                            <select name="to[]" id="multiselect_to" class="form-control" size="8" multiple="multiple"></select>
                                                     
                                                            <div class="row">
                                                                <div class="col-sm-6">
                                                                    <button type="button" id="multiselect_move_up" class="btn btn-block"><i class="glyphicon glyphicon-arrow-up"></i></button>
                                                                </div>
                                                                <div class="col-sm-6">
                                                                    <button type="button" id="multiselect_move_down" class="btn btn-block col-sm-6"><i class="glyphicon glyphicon-arrow-down"></i></button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                        </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-primary" onclick="saveForecastView();">Save</button>
                                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
         
         
      </body>
      <script type="text/javascript">
        $(function(){
      
    
             var config = {
               '.chosen-select'           : {},
               '.chosen-select-deselect'  : {allow_single_deselect:true},
               '.chosen-select-no-single' : {disable_search_threshold:10},
               '.chosen-select-no-results': {no_results_text:'Oops, nothing found!'},
               '.chosen-select-width'     : {width:"95%"}
             }
             for (var selector in config) {
               $(selector).chosen(config[selector]);
             }
             
             $('#datepicker_div .input-daterange').datepicker({
                 todayBtn: true,
                 autoclose:true
             });
                
        });
      </script>
   </html>
</apex:page>
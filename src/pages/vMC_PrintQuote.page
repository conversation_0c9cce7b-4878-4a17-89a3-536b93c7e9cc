<apex:page label="Quote" title="Quote" controller="vMC_PrintQuoteController_New" showHeader="false" sidebar="false" standardStylesheets="false" applyBodyTag="false" applyHtmlTag="false" renderAs="pdf">
	<head>
        <style>
       		#orderDetail {
  				font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
  				border-collapse: collapse;
  				width: 100%;
			}

			#orderDetail td, #orderDetail th {
  				border: 1px solid #ddd;
  				padding: 5px;
               	max-width: 100px;
				word-wrap: break-word;
			}

			#orderDetail tr:nth-child(even){background-color: #f2f2f2;}

			#orderDetail tr:hover {background-color: #ddd;}

            #orderDetail th {
              padding-top: 12px;
              padding-bottom: 12px;
              text-align: left;
              background-color: #4CAF50;
              color: white;
            }

            .headerItem {
                font-weight : bold;
            }

            .currencyPrice {
                float: right;
            }
               
            .headerBox {
                border: 1px solid black;
            }

            .headerContent {
                padding: 8px;
                font-size: 18px;
                font-weight: 700;
                border-bottom: 1px solid black;
                background-color: #d8d7d7;
            }

            .body {
                padding: 10px;
                font-size: 13px;
            }

            .title {
                    font-weight: 600;
                    color: #696868;
            }
               
			table{table-layout: fixed;}    
        </style>
    </head>
    <body>
    	<div><apex:image url="{!$Resource.VarianLogo}" width="250" height="90"/></div>
        <div class="body">
        	<!--START : Customer Info-->        
        	<div class="headerBox">
                <table id="orderDetail">
                <tr>
                    <td class="headerItem" width="45%">Customer</td>
                    <td width="55%">{!orderDetailObj.parentOrder.vMC_Account__r.Name}</td>
                </tr>
                <tr>
                    <td rowspan="5">
                        {!orderDetailObj.parentOrder.vMC_Account__r.SFDC_Account_Ref_Number__c} <br /><br />
                        <span class="title">Billing Address:</span><br />
                        {!billingAddress_One} <br />
                        {!billingAddress_Two} <br />
                        {!billingAddress_Three}
                    </td>
                </tr>
                <tr>
                    <td>3290 Northside Parkway<br /> Atlanta,GA <br />30327 US</td>
                </tr>
                <tr>
                    <td><span class="title">Varian Sales Customer Support:</span> 1-888-666-7847 Ext. 2</td>
                </tr>
                <tr>
                    <td><span class="title">Fax: </span> 1-(434) 951-8655</td>
                </tr>
                <tr>
                    <td><span class="title">Email:</span> {!orderDetailObj.parentOrder.PO_Submitted_Notification__c}</td>
                </tr>
            </table>
            </div>
        	<!--END : Customer Info-->            
            <br /><br />
            
            <!--START : Shipping Info-->
             <div class="headerBox">
                 <table id="orderDetail">
                    <tr>
                        <td class="title" rowspan="2">Incoterms</td>
                        <td rowspan="2">{!orderDetailObj.parentOrder.vMC_FOB__c}</td>
                        <td><span class="title">Shipping Address :</span></td>
                    </tr>
                    <tr>                        
                        <td width="55%" rowspan="4">
                            <b>{!orderDetailObj.parentOrder.Attn__c}</b><br />
                            {!shippingAddress_One} <br />
                        	{!shippingAddress_Two} <br />
                        	{!shippingAddress_Three} <br /><br />
                            <span class="title">Shipping Phone :</span> {!orderDetailObj.parentOrder.Shipping_Contact_Phone__c}<br /><br />
                            <span class="title">Shipping Email :</span> <apex:outputText value="{!orderDetailObj.parentOrder.Shipping_Contact_Email__c}"></apex:outputText>
                        </td>
                    </tr>
                    <tr>
                        <td class="title">Note</td>
                        <td>{!orderDetailObj.parentOrder.Shipping_Note__c}</td>
                    </tr>
                    <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Shipping_Method__c=='Customer Carrier'}">
                     <tr>
                         <td class="title">Customer Carrier # </td>
                         <td>{!orderDetailObj.parentOrder.vMC_Customer_Carrier__c}-{!orderDetailObj.parentOrder.Customer_Carrier_Number__c}</td>
                     </tr>
                    </apex:outputPanel>              
                </table>
            </div>
        	<!--END : Shipping Info-->
            <br /><br />
            
            <!--START : Order Summary-->
            <div class="headerBox"><div class="headerContent">Order Number: {!orderDetailObj.parentOrder.Name}</div>
                <table id="orderDetail">
                    <tr>
                        <td class="title" width="20%">Order Date</td>
                        <td width="25%">                            
                            <apex:outputText value="{0,date,MM/dd/yy}"> 
                                <apex:param value="{!orderDetailObj.parentOrder.OrderPlacedDate__c}" /> 
                            </apex:outputText>							
                        </td>
                        <td class="title" width="20%">Order Status</td>
                        <td>{!orderDetailObj.parentOrder.Status__c}</td>
                    </tr>                    
                    <tr>
                        <td class="title">Payment Terms</td>
                        <td>                            
                            <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c=='PO'}">{!orderDetailObj.parentOrder.Net_Term__c}</apex:outputPanel>
                            <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c!='PO'}">Due Immediately</apex:outputPanel>							
                        </td>
                        <td class="title">Payment Method</td>
                        <td>{!orderDetailObj.parentOrder.vMC_Payment_Method__c}</td>    
                    </tr>
                    <tr>
                        <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c=='PO' || orderDetailObj.parentOrder.vMC_Payment_Method__c=='Credit Card'}" layout="none"> <!-- Tulasi STRY0209732 Added Credit Card -->
                            <td class="title" width="20%">PO #</td>
                            <td width="25%">                            
                                <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c!='PO'}">{!orderDetailObj.parentOrder.PO_Number__c}</apex:outputPanel> <!-- Tulasi STRY0209732 Updated PO Number -->
                                <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c=='PO'}">{!orderDetailObj.parentOrder.PO_Number__c}</apex:outputPanel>							
                            </td>
                            <td class="title">Shipment 100.00% </td>
                            <td></td>
                        </apex:outputPanel>
                        <!-- 
                        <apex:outputPanel rendered="{!orderDetailObj.parentOrder.vMC_Payment_Method__c!='PO'}" layout="none">
                            <td class="title">Shipment 100.00% </td>
                            <td colspan="3"></td>    
                        </apex:outputPanel> -->
                    </tr>
                </table>
            </div>
            <!--END : Order Summary-->
            <br /><br />
            
            <!--START: Order Details-->            
			<apex:variable value="{!0}" var="count"/>            
            <apex:repeat value="{!orderDetailObj.childOrders}" var="orderObj" > 
                <apex:variable value="{!count+1}" var="count"/>                
                <!--<div style="{!IF(orderDetailObj.childOrders.size == count, '','page-break-after:always;')}">-->                
                <apex:outputpanel rendered="{!orderObj.order.RecordType.Name == 'Varian Product Order'}">
                    
                    <div class="headerBox"><div class="headerContent">{!orderObj.order.Product_Model__c} Order Details</div>
                    <br />
                    <table>
                        <tr>
                            <td>
                            	<div><span class="title">Sales Order Number</span></div>
                    			<div>{!orderObj.order.Order_Number__c}</div>
                            </td>
                            <td>
                            	<div><span class="title">Shipping Method</span></div>
                    			<div>
                                    <apex:outputText rendered="{!orderObj.order.Customer_Carrier_Number__c == ''}" value="{!orderObj.order.vMC_Shipping_Method__c}"/>
                                    <apex:outputText rendered="{!orderObj.order.Customer_Carrier_Number__c != ''}" value="{!orderObj.order.Customer_Carrier_Shipping_Method__c}"/>
                                </div>
                            </td>
                            <td>
                            	<div><span class="title">Expected Delivery Date</span></div>
                    			<div>
                                	<apex:outputText value="{0,date,MM/dd/yy}"> 
                                		<apex:param value="{!orderObj.order.Estimated_Delivery_Date__c}" /> 
                            		</apex:outputText>
                                </div>
                            </td>
                        </tr>    
                    </table>
                    <br />    
                   	<table id="orderDetail">
                        <tr>
                            <td class="title" width="30%">Item</td>
                            <td class="title" width="20%">Part Number</td>
                            <td class="title" width="10%">Quantity</td>
                            <td class="title" width="20%">Offer Unit Price</td>
                            <td class="title" width="20%">Line Total</td>
                        </tr>
                        
                        <apex:repeat value="{!orderObj.orderLineItems}" var="lineItm" >
                            <tr>
                               <td>{!lineItm.Product__r.Name}</td> 
                                <td>{!lineItm.Product__r.ProductCode}</td>
                                <td>{!lineItm.vMC_Quantity__c}</td>
                                <td>
                                    <span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                    <apex:outputText value="{0, number ,###,###.00}" rendered="{!lineItm.vMC_Discounted_Price__c > 0 }">
                                        <apex:param value="{!lineItm.vMC_Discounted_Price__c}"></apex:param>
                                    </apex:outputText>
                                    <apex:outputText value="{!lineItm.vMC_Discounted_Price__c}" rendered="{!lineItm.vMC_Discounted_Price__c == 0 }"></apex:outputText>
                                    </span>
                                </td>
                                <td>
                                    <span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                        <apex:outputText value="{0, number ,###,###.00}" rendered="{!lineItm.vMC_Total_Price__c > 0 }">
                                            <apex:param value="{!lineItm.vMC_Total_Price__c}"></apex:param>
                                        </apex:outputText>
                                        <apex:outputText value="{!lineItm.vMC_Total_Price__c}" rendered="{!lineItm.vMC_Total_Price__c == 0 }"></apex:outputText>
                                    </span>
                                </td>
                            </tr>
                        </apex:repeat>
                        
                        <tr><td></td><td></td><td></td><td class=""><span class="currencyPrice">Sub-Total</span></td>
                        <td><span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                            <apex:outputText value="{0, number ,###,###.00}" rendered="{!orderObj.order.Price__c > 0 }">
                                <apex:param value="{!orderObj.order.Price__c}"></apex:param>
                            </apex:outputText>
                            <apex:outputText value="{!orderObj.order.Price__c}" rendered="{!orderObj.order.Price__c == 0 }"></apex:outputText>
                            </span>
                        </td>
                        </tr>
                        <tr><td></td><td></td><td></td><td><span class="currencyPrice">Tax</span></td>
                        <td>
                            <span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp; 
                                <apex:outputText value="{0, number ,###,###.00}" rendered="{!orderObj.order.Tax__c > 0 }">
                                    <apex:param value="{!orderObj.order.Tax__c}"></apex:param>
                                </apex:outputText>
                                <apex:outputText value="{!orderObj.order.Tax__c}" rendered="{!orderObj.order.Tax__c == 0 }"></apex:outputText>
                            </span>
                            </td>
                        </tr>
                        
                        <tr><td></td><td></td><td></td><td><span class="currencyPrice">Shipping Cost</span></td>
                        <td><span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />
                            <apex:outputText value="{0, number, 0.00}">
                                <apex:param value="{!orderObj.order.Shipping_Cost__c}"></apex:param>
                            </apex:outputText>
                            <!--<apex:outputText value="{!orderObj.order.Shipping_Cost__c}" rendered="{!orderObj.order.Shipping_Cost__c == 0 }"></apex:outputText>-->
                            </span></td></tr>
                        
                        <tr><td></td><td></td><td></td><td class="title"><span class="currencyPrice">Sales Total</span></td>
                        <td><span class="currencyPrice"><apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                            <apex:outputText value="{0, number ,###,###.00}" rendered="{!orderObj.order.Total_Price__c > 0 }">
                                <apex:param value="{!orderObj.order.Total_Price__c}"></apex:param>
                            </apex:outputText>
                            <apex:outputText value="{!orderObj.order.Total_Price__c}" rendered="{!orderObj.order.Total_Price__c == 0 }"></apex:outputText>
                            </span></td></tr>
                    </table>
                    </div>
                    
                </apex:outputpanel>
                
                
                <apex:outputpanel rendered="{!orderObj.order.RecordType.Name == 'Third Party Apps Order'}">
                	<div class="headerBox">
                        <div class="headerContent">Third Party Apps Order Details</div>                        	
                        	<table id="orderDetail">
                                <tr>
                                    <td class="title">Item</td>
                                    <td class="title">Qty</td>
                                    <td class="title" style="text-align: right">Unit Price</td>
                                    <apex:outputPanel rendered="{!orderObj.order.IsSubscribed__c}" layout="none">
                                    <td class="title" >Billing Frequency</td>
                                    </apex:outputPanel>
                                    <apex:outputPanel rendered="{!!orderObj.order.IsSubscribed__c}" layout="none">
                                        <td class="title" colspan="2" style="text-align: right">Line Total</td>
                                    </apex:outputPanel>
                                    <apex:outputPanel rendered="{!orderObj.order.IsSubscribed__c}" layout="none">
                                    <td class="title" style="text-align: right">Line Total</td>
                                    </apex:outputPanel>
                                </tr>
                                <apex:repeat value="{!orderObj.orderLineItems}" var="lineItm" >
                                    <tr>
                                        <td>{!lineItm.vMTP_App__r.Name}</td>
                                        <td>{!lineItm.vMC_Quantity__c}</td>
                                        
                                        <td>
                                            <span class="currencyPrice">
                                                <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                                <apex:outputText value="{0, number ,###,##0.00}">
                                                    <apex:param value="{!lineItm.Price__c}"></apex:param>
                                                </apex:outputText>
                                            </span>
                                        </td>
                                        <apex:outputPanel rendered="{!orderObj.order.IsSubscribed__c}" layout="none">
                                        <td>{!lineItm.vMC_Duration_Type__c}</td>
                                        </apex:outputPanel>
                                        <apex:outputPanel rendered="{!!orderObj.order.IsSubscribed__c}" layout="none">
                                            <td colspan="2">
                                                <span class="currencyPrice">
                                                    <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                                    <apex:outputText value="{0, number ,###,##0.00}">
                                                        <apex:param value="{!lineItm.vMC_Total_Price__c}"></apex:param>
                                                    </apex:outputText>
                                                </span>
                                        	</td>
                                        </apex:outputPanel> 
                                        <apex:outputPanel rendered="{!orderObj.order.IsSubscribed__c}" layout="none">
                                            <td>
                                                <span class="currencyPrice">
                                                    <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                                    <apex:outputText value="{0, number ,###,##0.00}">
                                                        <apex:param value="{!lineItm.vMC_Total_Price__c}"></apex:param>
                                                    </apex:outputText>
                                                </span>
                                        	</td>
                                        </apex:outputPanel>
                                    </tr>
                                </apex:repeat>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td class=""><span class="currencyPrice">Sub-Total</span></td>
                                    <td>
                                        <span class="currencyPrice">
                                            <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp; 
                                            <apex:outputText value="{0, number ,###,##0.00}">
                                                <apex:param value="{!orderObj.order.Price__c}"></apex:param>
                                            </apex:outputText>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td><span class="currencyPrice">Tax</span></td>
                                    <td>
                                        <span class="currencyPrice">
                                            <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                            <apex:outputText value="{0, number ,###,##0.00}">
                                                <apex:param value="{!orderObj.order.Tax__c}"></apex:param>
                                            </apex:outputText>
                                        </span>
                                    </td>
                                </tr>
                               <apex:outputPanel rendered="{!!orderObj.order.IsSubscribed__c}" layout="none"> 
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td><span class="currencyPrice">Shipping Cost</span></td>
                                    <td>
                                        <span class="currencyPrice">
                                            <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                            <apex:outputText value="{0, number ,###,##0.00}">
                                                <apex:param value="{!orderObj.order.Shipping_Cost__c}"></apex:param>
                                            </apex:outputText>
                                        </span>
                                    </td>
                                </tr>
                                </apex:outputPanel>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td class="title"><span class="currencyPrice">Sales Total</span></td>
                                    <td>
                                        <span class="currencyPrice">
                                            <apex:outputText value="{!orderObj.order.CurrencyIsoCode}" />&nbsp;
                                            <apex:outputText value="{0, number ,###,##0.00}">
                                                <apex:param value="{!orderObj.order.Total_Price__c}"></apex:param>
                                            </apex:outputText>
                                        </span>
                                    </td>
                                </tr>
                        </table>
                    </div>
                </apex:outputpanel>         
                <br /><br />
                <!--</div>-->    
            </apex:repeat>
            

            <!--END: Order Details-->
            
            <br /><br />
    		<!--START: Terms and Conditions-->
    		<div class="headerBox">
                <div class="headerContent">Terms and Conditions</div>
                <div style="padding: 8px">
                   <apex:outputText value="{!vMCTermsConds}" escape="false" />
                </div>
    		</div>
    		<!--END: Terms and Conditions-->
            
        </div>        
    </body>    
</apex:page>
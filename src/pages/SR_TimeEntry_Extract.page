<apex:page id="pg" controller="SR_TimeEntry_Extract_Controller" docType="html-5.0" title="Time Entry Extract" >
  <apex:form >
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
    <link rel="stylesheet" href="/resources/demos/style.css"/>
    <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script>
        
        jQuery(document).ready(function($) {
            $("#datepicker, #datepicker2").each(function() {
                $(this).datepicker({
                    autoclose: true
                });
            });
        }); 
    </script>
    <script type="text/javascript">
        function updateTags(textbox){
                    if((textbox.oldvalue !='' || textbox.oldvalue != null) 
                        && (textbox.value =='' || textbox.value == null)){
                            updateTagAction(textbox.oldvalue);
                    } else{
                    updateTagsAction(textbox.value, textbox.oldvalue);
                }
                } 
    </script>
    <Apex:PageBlock Title="TIME ENTRY EXTRACT" id="criteria">
    <apex:pageMessages id="errormsgs"/>
    <!--    <Apex:OutputText><b> TIME ENTRY EXTRACT </b> </Apex:OutputText> <br/><br/><br/> -->
    <apex:pageblockSection title="Time Entry Criteria" columns="2">
    <apex:pageBlockSectionItem >
        <apex:outputLabel value="Country" />
        <apex:selectList size="5"  label="Country" value="{!strCountry}" multiselect="true" onchange="updateTags(this);this.oldvalue = this.value;" style="width:150px;"  > 
            <apex:selectOption itemLabel="Canada" itemvalue="Canada"/>
            <apex:selectOption itemLabel="China" itemvalue="China"/>
            <apex:selectOption itemLabel="Germany" itemvalue="Germany"/>
            <apex:selectOption itemLabel="United Kingdom" itemvalue="United Kingdom"/>
            <apex:selectOption itemLabel="United States" itemvalue="United States"/>
        </apex:selectList>
    </apex:pageBlockSectionItem>
    <apex:pageBlockSectionItem >
        <apex:outputLabel value="Status" />
        <apex:selectList size="3"  label="Status" value="{!strStatus}" multiselect="true" onchange="updateTags(this);this.oldvalue = this.value;" style="width:150px;"> 
            <apex:selectOption itemLabel="--- Any ---" itemvalue="any"/>
            <apex:selectOption itemLabel="Approved" itemvalue="APPROVED"/>
            <apex:selectOption itemLabel="Submitted" itemvalue="SUBMITTED"/>
        </apex:selectList>
    </apex:pageBlockSectionItem>
    <apex:pageBlockSectionItem >
        <apex:outputLabel value="Start Date" />
        <apex:inputField value="{!startdatefrmte.TEExtractDt__c}" onchange="updateTags(this);this.oldvalue = this.value;"  required="true" id="stdatefld"/>
    </apex:pageBlockSectionItem>
    <apex:pageBlockSectionItem >
        <apex:outputLabel value="End Date" />
        <apex:inputField value="{!enddatefromte.TEExtractDt__c}" onchange="updateTags(this);this.oldvalue = this.value;"  required="true" id="eddatefld"/>
    </apex:pageBlockSectionItem>
    </apex:pageblockSection>
    <apex:pageBlockButtons >
        <apex:commandButton value="Search" action="{!searchTE}" status="createEvent1" reRender="criteria,errormsgs,pb1,pb"></apex:commandButton>
        <apex:commandButton action="{!cancel}" value="Cancel"/> 
        <apex:actionStatus id="createEvent1" style="align:center;" >
            <apex:facet name="start" >
                <img src="/img/loading.gif"  height="20" width="20" />                       
            </apex:facet>
        </apex:actionStatus>&nbsp;
    </apex:pageBlockButtons>
    </Apex:PageBlock>
    <!-- <br></br><br></br>
    <label for="stdatefld">START DATE: </label>&nbsp;&nbsp;
    <apex:inputField value="{!startdatefrmte.TEExtractDt__c}" onchange="updateTags(this);this.oldvalue = this.value;"  required="false" id="stdatefld"/>
    </tr>
    <tr>
    <br></br><br></br>
    <label for="eddatefld">END DATE: </label>&nbsp;&nbsp;&nbsp;&nbsp;
    
    </tr>
    <br></br><br></br>
    <tr>
    COUNTRY : &nbsp;
    
    <br></br><br></br>
    </tr>
    
    </table>
    <br></br>
        
    </apex:pageBlock> -->
    
    <apex:outputPanel id="pb1">   
    <apex:pageBlock id="pb" rendered="{!toggleResult}">
        <apex:pageBlockSection columns="8" >
            <apex:pageBlockTable value="{!timeentries}" var="te1">
                <apex:column value="{!te1.te.WCExtract__c}" headerValue="Work Center"/>
                <apex:column value="{!te1.te.Payroll_Start_Date__c}" headerValue="Payroll Start Date"/>
                <apex:column value="{!te1.te.StTimeExtract__c}" headerValue="Start Time"/>
                <apex:column value="{!te1.te.EdTimeExtract__c}" headerValue="End Time"/>
                <apex:column value="{!te1.te.LTExtract__c}" headerValue="Labor Time"/>
                <apex:column value="{!te1.te.WOExtract__c}" headerValue="Work Order #"/>
                <apex:column value="{!te1.te.Type__c}" headerValue="Type"/>
                <apex:column value="{!te1.te.Activity_Description__c}" headerValue="Activity Description"/>
            </apex:pageBlockTable>
        </apex:pageBlockSection>
        <apex:panelGrid columns="4">
            <apex:commandLink action="{!first}" status="fetchStatus" reRender="pb">First</apex:commandlink>
            <apex:commandLink action="{!previous}" status="fetchStatus" rendered="{!hasPrevious}" reRender="pb">Previous</apex:commandlink> 
            <apex:commandLink action="{!next}"  status="fetchStatus" rendered="{!hasNext}" reRender="pb">Next</apex:commandlink>
            <apex:commandLink action="{!last}"  status="fetchStatus" reRender="pb">Last</apex:commandlink>
            <apex:actionStatus id="fetchStatus" style="align:center;" >
            <apex:facet name="start" >
                <img src="/img/loading.gif"  height="20" width="20" />                       
            </apex:facet>
            </apex:actionStatus>&nbsp; 
        </apex:panelGrid>
        <br></br>
        <apex:outputLabel value="Include Time Entry Name">
        <apex:inputCheckbox value="{!incldtename}" />
        </apex:outputLabel>
        <apex:commandButton action="{!extractpg}" value="Download"/>  
    </apex:pageBlock>
    <apex:pageBlock id="pb2" rendered="{!toggleResultGE}">
        <apex:pageBlockSection >
            <apex:pageBlockTable value="{!timeentries}" var="te1">
                <apex:column value="{!te1.te.Timesheet__r.Organization__r.Name}" headerValue="Organization"/>
                <apex:column value="{!te1.te.Timesheet__r.Status__c}" headerValue="Status"/>
                <apex:column value="{!te1.te.Timesheet__r.District__r.Name}" headerValue="District"/>
                <apex:column value="{!te1.te.Work_Center__c}" headerValue="Badge Number"/>
                <apex:column value="{!te1.te.Activity_Description__c}" headerValue="Activity Description"/>
                <apex:column value="{!te1.te.Record_Type_Name__c}" headerValue="Record Type"/>
                <apex:column value="{!te1.te.Payroll_Start_Date__c}" headerValue="Payroll Start Date"/>
                <apex:column value="{!te1.te.Start_Time__c}" headerValue="Start Time"/>
                <apex:column value="{!te1.te.Payroll_End_Date__c}" headerValue="Payroll End Date"/>
                <apex:column value="{!te1.te.End_Time__c}" headerValue="End Time"/>
                <apex:column value="{!te1.te.TotalTime__c}" headerValue="Labor Time"/>
                <apex:column value="{!te1.te.Equipment__c}" headerValue="Equipment"/>
                <apex:column value="{!te1.te.City__c}" headerValue="City"/>
                <apex:column value="{!te1.te.Work_Order_Number__c}" headerValue="Work Order Number"/>
                <apex:column value="{!te1.te.timesheet__r.Dosimeter_Reading__c}" headerValue="Domestic Reading"/>
                <apex:column value="{!te1.te.timesheet__r.Name}" headerValue="Timesheet Name"/>
                <apex:column value="{!te1.te.Name}" headerValue="Timeentry Name"/>
            </apex:pageBlockTable>
        </apex:pageBlockSection>
        <apex:panelGrid columns="4">
            <apex:commandLink action="{!first}" status="fetchStatus" reRender="pb2">First</apex:commandlink>
            <apex:commandLink action="{!previous}" status="fetchStatus" rendered="{!hasPrevious}" reRender="pb2">Previous</apex:commandlink> 
            <apex:commandLink action="{!next}"  status="fetchStatus" rendered="{!hasNext}" reRender="pb2">Next</apex:commandlink>
            <apex:commandLink action="{!last}"  status="fetchStatus" reRender="pb2">Last</apex:commandlink>
            <apex:actionStatus id="fetchStatus" style="align:center;" >
            <apex:facet name="start" >
                <img src="/img/loading.gif"  height="20" width="20" />                       
            </apex:facet>
            </apex:actionStatus>&nbsp; 
        </apex:panelGrid>
        <br></br>
        <apex:commandButton action="{!extractpg1}" value="Download"/>  
    </apex:pageBlock>
    </apex:outputPanel>
    <!-- /apex:outputPanel -->
    </apex:form>
</apex:page>
<!-- 
/*************************************************************************\
    @ Author        : <PERSON><PERSON><PERSON>
    @ Date      : 10-May-2013
    @ Description   : This page is used to display Summary of selected TrueBeam.
    @ Last Modified By  :   <PERSON><PERSON><PERSON>
    @ Last Modified On  :   27-May-2013
    @ Last Modified Reason  :   Updated Header Comments
****************************************************************************/

-->
<apex:page showHeader="false" standardStylesheets="{!if(shows == 'none',false,true)}" sidebar="false" controller="VR_CP_TBSummary" id="mypage">
 
  <head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="CACHE-CONTROL" content="NO-CACHE" />
    <title>TrueBeam Developer Mode | MyVarian</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<!-- <link rel="shortcut icon" href="/system/files/myvarian4_favicon.ico" type="image/x-icon" /> -->
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/aggregator.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/node.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/poll.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/defaults.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/system.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/system-menus.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/user.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/calendar_multiday.css')}" rel="stylesheet" type="text/css"/>

<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/content-module.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/ckeditor.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/date.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/datepicker.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/jquery.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/filefield.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/thickbox.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/thickbox_ie.css')}" rel="stylesheet" type="text/css"/>

<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/dcpartner.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/marketingkit.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/views_slideshow.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/views_slideshow_002.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/fieldgroup.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/views.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/style.css')}" rel="stylesheet" type="text/css"/>
<link href="{!URLFOR($Resource.VarianCss, '/VarianCss/MyVarian_files/banners.css')}" rel="stylesheet" type="text/css"/>
  <!-- for community -->
    <script src="/jslibrary/1381427012000/sfdc/JiffyStubs.js">
        </script>
        <script src="/jslibrary/1381427012000/sfdc/main.js"></script><script src="/jslibrary/jslabels/1382057526000/en_US.js">
        </script>
        <script src="/jslibrary/1381427012000/sfdc/AccessibleDialog.js"></script>
        <script src="/jslibrary/1381427012000/sfdc/DevContextMenu.js"></script>
        <script type="text/javascript">
          

    SetupTreeNode.prototype.initializeSetup = function() {
      var openNodes = '';
      if (openNodes != null) {
        SetupTreeNode.prototype.openListSetup = openNodes.split(':');
      }
      SetupTreeNode.prototype.personalSetupV2 = false;
    }

        </script><script>
          try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1381884298000/Theme3/default/base/zen-componentsCompatible.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1381884298000/Theme3/default/base/elements.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1381884298000/Theme3/default/base/common.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1381884298000/Theme3/default/base/setup.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1382134726000/Theme3/base/dStandard.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1382476000000/Theme3/00DM00000013QDl/005E00000033Wxn/base/dCustom0.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1382476000000/Theme3/00DM00000013QDl/005E00000033Wxn/base/dCustom1.css")}catch(e){}
    try{DynamicCss.addCssUrl("/sCSS/29.0/sprites/1381884298000/Theme3/default/base/extended.css")}catch(e){}
    try{DynamicCss.doneLoading()}catch(e){}
        </script><link href="https://varian--SFDM.cs7.my.salesforce.com/favicon.ico" rel="shortcut icon"></link><script src="/jslibrary/1381427012000/sfdc/Security.js"></script>
        <script src="/jslibrary/1381427012000/sfdc/Setup.js" defer="true"></script><script src="/jslibrary/1381427012000/sfdc/Zen.js" defer="true"></script>

    <!-- end here -->
    
  </head>
  
  
  <body> 
 <!-- Community header -->
    <apex:outputpanel style="display:{!shows}"> 
    <div id="globalHeaderBar" class="globalHeaderBar"><div class="globalHeaderCommunityMenuContainer"><div class="globalHeaderVerticalLineInner"><div class="globalHeaderVerticalLineOuter"><div class="zen-select" role="application" data-sfdc-widget="SfdcCmp.Ui.Dropdown" data-isselect="false" data-uidsfdc="6"><a id="globalHeaderCommunitySwitcher" class="zen-trigger" title="Press space bar or Enter key to open" aria-haspopup="true" href="javascript:void(0)" data-uidsfdc="4"><b class="zen-selectArrow"></b>
         MyVarian
        </a><ul class="zen-options" role="menu" aria-labelledby="globalHeaderCommunitySwitcher" data-uidsfdc="5"><li class="zen-firstItem" role="presentation"></li><li class="" role="presentation"><a title="Varian Medical Systems" role="menuitem" href="{!$label.Org_Domain_URL}">
          Varian Medical Systems Internal
        </a></li><li class="zen-lastItem" role="presentation"></li></ul></div></div></div></div><div class="globalHeaderNameMenuContainer"><div class="globalHeaderVerticalLineInner"><div class="globalHeaderVerticalLineOuter"><div class="zen-select" role="application" data-sfdc-widget="SfdcCmp.Ui.Dropdown" data-isselect="false" data-uidsfdc="9"><a id="globalHeaderNameMink" class="zen-trigger" title="Press space bar or Enter key to open" aria-haspopup="true" href="javascript:void(0)" data-uidsfdc="7"><b class="zen-selectArrow"></b><span><span class="chatter-avatarSmall chatter-avatar globalHeaderProfilePhoto"><img class="chatter-photo" width="20" height="20" src="{!$Label.CPProfilePhotoURL}"></img><img class="chatter-avatarStyle" title="" alt="" src="/s.gif"></img></span>
          {!Usrname}
        </span></a><ul class="zen-options" role="menu" aria-labelledby="globalHeaderNameMink" data-uidsfdc="8"><li class="zen-firstItem" role="presentation"></li><li role="presentation"></li><li role="presentation"></li><li role="presentation"></li><li role="presentation"></li><li class="zen-lastItem" role="presentation"><a title="Logout" role="menuitem" href="/secur/logout.jsp">
          Logout
        </a></li></ul></div></div></div></div><div class="nhTestHelperCurrentContext" style="display: none">
          00DM00000013QDl
        </div></div></apex:outputpanel>
    <!-- end here -->
  
<div id="wrapper">
    <c:CpPortalHeader />
   <!-- /topToolBar -->
      
    <div id="container">
        <c:CpPortalSideMenu /> 
    <div id="center" class="CenterNoRightSide tbdev_pg">
        <h1 class="TrueBeamDeveloperMode">{!lDevMode[0].Title__c}</h1>          
            <div class="centerOneCol">  
                 <apex:OutputText value="{0,date,MMM' 'dd', 'yyyy}"><apex:Param value="{!lDevMode[0].LastModifiedDATE}" /></apex:OutputText> 
            </div>
            <br/>
            <div class="centerOneCol">  
                <strong> Type: </strong><br/>
                 {!TBtype}
                 <br/><br/>
                 <apex:OutputText value="{!lDevMode[0].description__c}"/>
            </div>
            <br/>
    <apex:pageblock >        
        <apex:pageblocktable value="{!attachment}" var="attach">
        <apex:column headerValue="Name">                
                <a href="/sfc/servlet.shepherd/version/download/{!attach.Id}" target="_blank">{!attach.Title}</a> 
            </apex:column>
           <!-- <apex:column headerValue="Updated Date">   
                <apex:OutputText value="{0,date,MMM' 'dd', 'yyyy}"><apex:Param value="{!attach.LastModifiedDate}" /></apex:OutputText>          
                
            </apex:column>
            -->
            
            <apex:column headerValue="Size">   
                <apex:outputText value="{!ROUND(attach.ContentSize/(1024*1024),4)} MB"/>        
            </apex:column>
        </apex:pageblocktable>  
    </apex:pageblock>   
    <p><a href="/apex/cpTBRecPage?TBtype={!TBtype}" class="button">go back</a></p>              
    
    
    <span class="clear"></span>
    </div>
    </div> <!-- /container -->
  
  <!-- footer -->
        <c:CpFooter />
  <!-- /footer -->
  </div>
<!-- /layout -->

  

  </body>
 
 
 
 
</apex:page>
trigger BigMachinesQuoteProductTrigger on BigMachines__Quote_Product__c (after insert, after update, after delete) {
    system.debug('BookingValueCalculationHandler.isExecuted = '+BookingValueCalculationHandler.isExecuted);
    system.debug('BigMachinesQuoteProductTrigger  Inside');
    
    set<Id> quoteIds =  new set<Id>();
    //Set<id> triggerIds = Trigger.newMap.keyset();
    //Calculated booking value
    if(Trigger.isafter && (Trigger.isupdate || Trigger.isInsert || Trigger.isdelete)){
        if(!BookingValueCalculationHandler.isExecuted) {
            system.debug('booking value set method invoke');
            
            list<BigMachines__Quote_Product__c> newList = Trigger.new;
            if(Trigger.isdelete){
                newList = Trigger.old;
            }
            for(BigMachines__Quote_Product__c  bqp : newList ) {
                quoteIds.add(bqp.BigMachines__Quote__c);
            }
            if(!quoteIds.isEmpty()){
                BookingValueCalculationHandler.calculateBookingValue(quoteIds);
            }   
            
        }
    }
    if(Trigger.isinsert || Trigger.isupdate){
        UpdateLibraOpportunityProduct.updateLibraOpportunityProd(trigger.new);
    }
    if(Trigger.isupdate){
        QuoteProductPricing.UpdateSubscription(trigger.new);
    }
}
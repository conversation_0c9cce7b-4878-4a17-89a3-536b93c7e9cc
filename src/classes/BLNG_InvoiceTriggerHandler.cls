public class BLNG_InvoiceTriggerHandler {
    @testVisible private static Map<String, Integer> loopCountMap = new Map<String, Integer>();
    public static Boolean loopCount = true;
    private boolean m_isExecuting = false;
    private integer BatchSize = 0;
    
    //BLNG_InvoiceTriggerHelper helper = new BLNG_InvoiceTriggerHelper();
    
    // Constructor
    public BLNG_InvoiceTriggerHandler(boolean isExecuting, integer size){
        m_isExecuting = isExecuting;
        BatchSize = size;
    }
    
    //set default loop count for before and after update 
    public void setLoopCount(Integer beforeUpdate, Integer afterUpdate){
        loopCount = false; 
        loopCountMap.put('beforeUpdate',beforeUpdate);
        loopCountMap.put('afterUpdate',afterUpdate); 
    }
    
    // On Before Insert 
    public void OnBeforeInsert(list<blng__Invoice__c> newBlng){
        updateBilling(newBlng, null);
       /* Set<Id> invoiceIds = new Set<Id>();
        for(blng__Invoice__c inv : newBlng) {
            if(inv.blng__InvoiceRunCreatedBy__c != null) {
                invoiceIds.add(inv.Id);
            }
        }
        helper.updateRelatedOrders(invoiceIds);*/
    } 
    
    // On After Insert  
    public void OnAfterInsert(list<blng__Invoice__c> newBlng){
    }
    
    // On Before Update     
    public void OnBeforeUpdate(List<blng__Invoice__c> newBlng, List<blng__Invoice__c> oldBlng, Map<Id,blng__Invoice__c> newBlngMap, Map<Id, blng__Invoice__c> oldBlngMap){
        Integer loopCount = loopCountMap.get('beforeUpdate');
        
        if(loopCount > 0){
            loopCountMap.put('beforeUpdate',loopCount-1);
            List<blng__Invoice__c> postedInvoices = new List<blng__Invoice__c>();
            //do your logic here
            updateBilling(newBlng,oldBlngMap); //General Update logic for all lines.
            Set<Id> deleteAttachmentInvoiceIds = new Set<Id>();
            for(blng__Invoice__c invoice : newBlng){
                System.debug('old billing invoice status:  '+oldBlngMap.get(invoice.Id).blng__InvoiceStatus__c);
                System.debug('new billing status:  '+invoice.blng__InvoiceStatus__c);                
                if(invoice.blng__InvoiceStatus__c == 'Posted' && invoice.blng__InvoiceStatus__c != oldBlngMap.get(invoice.Id).blng__InvoiceStatus__c && String.isBlank(invoice.SAPSalesOrderContract__c) && !invoice.EDIInvoice__c){ // STRY0565622 : Added on May 2025
                    postedInvoices.add(invoice);
                }
            }
            System.debug('---postedInvoices--'+postedInvoices);
            if(!postedInvoices.isEmpty()) updateVarianInvoiceNumber(postedInvoices, false);
        }
    }
    
    // On After Update
    public void OnAfterUpdate(List<blng__Invoice__c> newBlng, List<blng__Invoice__c> oldBlng, Map<Id, blng__Invoice__c> newBlngMap, Map<Id,blng__Invoice__c> oldBlngMap){
        Integer loopCount = loopCountMap.get('afterUpdate');
        
        if(loopCount > 0){
            loopCountMap.put('beforeUpdate',loopCount-1);
            List<UpdateRecord__e> listUr = new List<UpdateRecord__e>();
            Set<Id> masterQuoteIds = new Set<Id>();
            //do your logic here  
            CPQ_CongaTriggerJob.publishCongaTriggerJobFinishedEvent(newBlng[0], oldBlng[0], 'STANDALONE', newBlng[0].Name); 
            
            for(blng__Invoice__c inv : newBlng){
                //consider moving to generic approach.
                if(inv.SAP_Tax_Interface_Status__c == 'Processed' && oldBlngMap.get(inv.Id).SAP_Tax_Interface_Status__c != 'Processed'){
                    UpdateRecord__e ur = new UpdateRecord__e();
                    ur.Type__c = 'tax';
                    ur.Record_Id__c = inv.Id;
                    listUr.add(ur);
                }
                
                if(inv.blng__InvoiceStatus__c == 'Posted' && inv.blng__InvoiceStatus__c != oldBlngMap.get(inv.Id).blng__InvoiceStatus__c){
                    masterQuoteIds.add(inv.Master_Quote__c);
                }
            }
            if(listUr.size()>0) EventBus.publish(listUr);
            
            //STRY0545711
            if(!masterQuoteIds.isEmpty()){
                rollupBilledUnbilledTotalsOnMasterQuote(masterQuoteIds);
            }
        }
    }
    
    /* On Before Delete 
    public void OnBeforeDelete(Map<Id, blng__Invoice__c> oldBlngMap){
        
    }
    
    // On After Delete 
    public void OnAfterDelete(Map<Id, blng__Invoice__c> oldBlngMap){
        
    }
    
    // On After Undelete
    public void OnAfterUndelete(Map<Id, blng__Invoice__c> oldBlngMap){
        
    }*/
    
    //Update Functions
    private void updateBilling(List<blng__Invoice__c> newBlng, Map<Id,blng__Invoice__c> oldBlngMap){
        
        Set<String> deliverToCountries = new Set<String>();
        Set<Id> erpPartnerAssociations = new Set<Id>();
        Set<Id> legalEntities = new Set<Id>();
        Map<Id, blng__Invoice__c> invoicesWithLanguageChange = new Map<Id, blng__Invoice__c>();
        Set<Id> accountIds = new Set<Id>();
        for(blng__Invoice__c inv : newBlng){
            accountIds.add(inv.blng__Account__c);
		}
         
        for(blng__Invoice__c inv : newBlng){
            
            if(oldBlngMap !=null && inv.InvoicePosted__c && (
                (inv.SAP_Tax_Interface_Status__c == 'Processed' && oldBlngMap.get(inv.Id).SAP_Tax_Interface_Status__c != 'Processed') 
                || (inv.BillingDate__c == inv.blng__InvoiceDate__c && !oldBlngMap.get(inv.Id).InvoicePosted__c)
            )
              ){
                  inv.blng__InvoiceStatus__c = 'Posted';
                  // STRY0565622 : Added on May 2025
                  if (!String.isBlank(inv.SAPSalesOrderContract__c) || inv.EDIInvoice__C) {
                      inv.DMRInterfaceStatus__c = 'Process';
                  }else{
                      inv.Interface_Status__c = 'Process';
                  }
                              
                inv.Conga_Trigger_Generate_Document__c = true;
                if(inv.Delivery_Status__c != 'Do Not Send'){
                    inv.Delivery_Status__c = 'Ready To Send';
                }
            }
            
            System.debug('--inv.blng__InvoiceDate__c--'+inv.blng__InvoiceDate__c);
            System.debug('--inv.PaymentTermDays__c--'+inv.PaymentTermDays__c);
            System.debug('--inv.blng__DueDate__c--'+inv.blng__DueDate__c);
            System.debug('--CHANGE CHECK--'+(oldBlngMap==null || (inv.blng__InvoiceDate__c != oldBlngMap.get(inv.Id).blng__InvoiceDate__c) || inv.blng__DueDate__c == null));
            
            if(inv.blng__InvoiceDate__c !=null && inv.PaymentTermDays__c != null && (oldBlngMap==null || (inv.blng__InvoiceDate__c != oldBlngMap.get(inv.Id).blng__InvoiceDate__c) || inv.blng__DueDate__c == null)){
                inv.blng__DueDate__c = inv.blng__InvoiceDate__c.addDays(Integer.valueOf(inv.PaymentTermDays__c));
            } 
            
            if(!String.isBlank(inv.Deliver_To_Country__c) && (oldBlngMap == null || oldBlngMap.get(inv.Id).Deliver_To_Country__c != inv.Deliver_To_Country__c)){
                deliverToCountries.add(inv.Deliver_To_Country__c);
                erpPartnerAssociations.add(inv.Bill_To__c);
                erpPartnerAssociations.add(inv.Ship_To__c);
                erpPartnerAssociations.add(inv.Payer__c);
                erpPartnerAssociations.add(inv.Sold_To__c);
                legalEntities.add(inv.LegalEntity__c);
            }
            System.debug('---inv.Invoice_Language__c--'+inv.Invoice_Language__c);
            System.debug('---inv.Deliver_To_Country__c--'+inv.Deliver_To_Country__c);
            if(!String.isBlank(inv.Invoice_Language__c) && (oldBlngMap != null && oldBlngMap.get(inv.Id).Invoice_Language__c!= inv.Invoice_Language__c)){
                invoicesWithLanguageChange.put(inv.Id, inv);
            }
        } 
        
        if(!deliverToCountries.isEmpty()) formatInvoiceAddress(newBlng, deliverToCountries, erpPartnerAssociations, legalEntities);
        if(!invoicesWithLanguageChange.isEmpty()) handleInvoiceLanguageChange(invoicesWithLanguageChange);
    }
    
    // Updates varian invoice number field for new invoices and Cancellation invoice number field for rebiiled invoices
    //This method is getting called from credit note trigger handler
    public static void updateVarianInvoiceNumber(List<blng__Invoice__c> newBlng, Boolean canceledInvoices){
        System.debug('---updateVarianInvoiceNumber--');
        Set<Id> legalEntityIds = new Set<Id>();
        Set<String> documentCodes = new Set<String>();
        Map<String, vblng_DocumentSequence__c>  documentSequences = new Map<String, vblng_DocumentSequence__c>();
        
        for(blng__Invoice__c inv : newBlng){
            if(inv.LegalEntity__c != null){
                legalEntityIds.add(inv.LegalEntity__c);
            }
        }
        
        Map<Id, blng__LegalEntity__c> legalEntities = new Map<Id, blng__LegalEntity__c>([
            SELECT Id, Document_Code__c FROM blng__LegalEntity__c WHERE Id IN:legalEntityIds
        ]);
        for(blng__LegalEntity__c legalEntity:legalEntities.values()){
            documentCodes.add(legalEntity.Document_Code__c);
        }
        for(vblng_DocumentSequence__c documentSequence : [
            SELECT Id, Next_Value__c, Document_Code__c 
            FROM vblng_DocumentSequence__c 
            WHERE Document_Category__c includes ('Invoice') AND Document_Code__c IN:documentCodes
        ]){
            documentSequences.put(documentSequence.Document_Code__c, documentSequence);
        }
        System.debug('---documentSequences--'+documentSequences);
        System.debug('---legalEntities--'+legalEntities);
        for(blng__Invoice__c inv : newBlng){
            if(legalEntities.containsKey(inv.LegalEntity__c)){
            
                vblng_DocumentSequence__c documentSequence;
                if(documentSequences.containsKey(legalEntities.get(inv.LegalEntity__c).Document_Code__c)){
                    documentSequence = documentSequences.get(legalEntities.get(inv.LegalEntity__c).Document_Code__c);
                }
                System.debug('---documentSequence--'+documentSequence);
                if(documentSequence != null){
                    if(!canceledInvoices){
                        inv.Varian_Invoice_Number__c = String.valueOf(documentSequence.Next_Value__c);
                    }else{
                        inv.Cancellation_Invoice_Number__c = String.valueOf(documentSequence.Next_Value__c);
                        inv.Interface_Status__c = 'Process';
                        //STRY0178450: Set Billing Date to today's date for Invoice Cancellation to avoid SAP Accounting Document Interface Status Error 
                        inv.BillingDate__c = Date.today();
                    }
                    documentSequence.Next_Value__c = documentSequence.Next_Value__c + 1;
                }
                System.debug('---inv.Varian_Invoice_Number__c--'+inv.Varian_Invoice_Number__c);
            }
        }
        update documentSequences.values();
    }
    
    //Update address fields using format from country record
    private static void formatInvoiceAddress(List<blng__Invoice__c> newInvoices, Set<String> deliverToCountries, Set<Id> erpAssociations, Set<Id> legalEntityIds){
        Map<String, String> countryAddressFormats = new Map<String, String>();
        
        Map<Id, ERP_Partner_Association__c> partnerAssociations = new Map<Id, ERP_Partner_Association__c>([
            SELECT Id, Partner_Street__c, Partner_Street_line_2__c, ERP_Partner__r.Region_Desc__c, Partner_Zipcode_postal_code__c, Partner_City__c, ERP_Partner__r.Country__c,
            ERP_Partner__r.House_No_1__c, ERP_Partner__r.House_No_2__c, ERP_Partner__r.Partner_Name_Line_2__c, ERP_Partner__r.Partner_Name_Line_3__c
            FROM ERP_Partner_Association__c
            WHERE Id IN: erpAssociations
        ]);
        
        Map<Id, blng__LegalEntity__c> legalEntities = new Map<Id, blng__LegalEntity__c>([
            SELECT Id, blng__Street1__c, blng__Street2__c, blng__State__c, blng__ZipPostalCode__c, blng__City__c, blng__Country__c
            FROM blng__LegalEntity__c
            WHERE Id IN: legalEntityIds
        ]);
        
        for(Country__c country : [SELECT Id, Name, Invoice_Output_Address_Format__c FROM Country__c WHERE Name IN:deliverToCountries]){
            countryAddressFormats.put(country.Name, country.Invoice_Output_Address_Format__c);
        }
        for(blng__Invoice__c invoice : newInvoices){
            String addressFormat = countryAddressFormats.get(invoice.Deliver_To_Country__c);
            if(String.isBlank(addressFormat)){
                addressFormat = System.Label.blng_InvoiceAddressDefaultFormat;
            }
            
            if(!String.isBlank(addressFormat)){
                if(partnerAssociations.containsKey(invoice.Bill_To__c)){
                    ERP_Partner_Association__c ep = partnerAssociations.get(invoice.Bill_To__c);
                    invoice.Bill_To_Address__c = addressFormat.replace('<<PARTNERNAME2>>',ep.ERP_Partner__r.Partner_Name_Line_2__c!=null?ep.ERP_Partner__r.Partner_Name_Line_2__c:'').replace('<<PARTNERNAME3>>',ep.ERP_Partner__r.Partner_Name_Line_3__c!=null?ep.ERP_Partner__r.Partner_Name_Line_3__c:'');
                    invoice.Bill_To_Address__c = invoice.Bill_To_Address__c.replace('<<HOUSENO1>>',ep.ERP_Partner__r.House_No_1__c!=null?ep.ERP_Partner__r.House_No_1__c:'').replace('<<HOUSENO2>>',ep.ERP_Partner__r.House_No_2__c!=null?ep.ERP_Partner__r.House_No_2__c:'').replace('<<STREET1>>',ep.Partner_Street__c!=null?ep.Partner_Street__c:'').replace('<<STREET2>>',ep.Partner_Street_line_2__c!=null?ep.Partner_Street_line_2__c:'').replace('<<CITY>>',ep.Partner_City__c!=null?ep.Partner_City__c:'').replace('<<STATE>>',ep.ERP_Partner__r.Region_Desc__c!=null?ep.ERP_Partner__r.Region_Desc__c:'').replace('<<COUNTRY>>',ep.ERP_Partner__r.Country__c!=null?ep.ERP_Partner__r.Country__c:'').replace('<<POSTALCODE>>',ep.Partner_Zipcode_postal_code__c!=null?ep.Partner_Zipcode_postal_code__c:'');
                    invoice.Bill_To_Address__c = invoice.Bill_To_Address__c.replaceAll('\r\n\r\n','\r\n').replaceAll('\n\n','\n');
                    invoice.Bill_To_Address__c = invoice.Bill_To_Address__c.replaceAll(', ,',',').replaceAll(',,',',');
                    invoice.Bill_To_Address__c = invoice.Bill_To_Address__c.replaceAll(', \r\n','\r\n').replaceAll(',\r\n','\r\n');
                    invoice.Bill_To_Address__c = invoice.Bill_To_Address__c.replaceAll(', \n','\n').replaceAll(',\n','\n');
                }
                if(partnerAssociations.containsKey(invoice.Ship_To__c)){
                    ERP_Partner_Association__c ep = partnerAssociations.get(invoice.Ship_To__c);
                    invoice.Ship_To_Address__c = addressFormat.replace('<<PARTNERNAME2>>',ep.ERP_Partner__r.Partner_Name_Line_2__c!=null?ep.ERP_Partner__r.Partner_Name_Line_2__c:'').replace('<<PARTNERNAME3>>',ep.ERP_Partner__r.Partner_Name_Line_3__c!=null?ep.ERP_Partner__r.Partner_Name_Line_3__c:'');
                    invoice.Ship_To_Address__c = invoice.Ship_To_Address__c.replace('<<HOUSENO1>>',ep.ERP_Partner__r.House_No_1__c!=null?ep.ERP_Partner__r.House_No_1__c:'').replace('<<HOUSENO2>>',ep.ERP_Partner__r.House_No_2__c!=null?ep.ERP_Partner__r.House_No_2__c:'').replace('<<STREET1>>',ep.Partner_Street__c!=null?ep.Partner_Street__c:'').replace('<<STREET2>>',ep.Partner_Street_line_2__c!=null?ep.Partner_Street_line_2__c:'').replace('<<CITY>>',ep.Partner_City__c!=null?ep.Partner_City__c:'').replace('<<STATE>>',ep.ERP_Partner__r.Region_Desc__c!=null?ep.ERP_Partner__r.Region_Desc__c:'').replace('<<COUNTRY>>',ep.ERP_Partner__r.Country__c!=null?ep.ERP_Partner__r.Country__c:'').replace('<<POSTALCODE>>',ep.Partner_Zipcode_postal_code__c!=null?ep.Partner_Zipcode_postal_code__c:'');
                    invoice.Ship_To_Address__c = invoice.Ship_To_Address__c.replaceAll('\r\n\r\n','\r\n').replaceAll('\n\n','\n');
                    invoice.Ship_To_Address__c = invoice.Ship_To_Address__c.replaceAll(', ,',',').replaceAll(',,',',');
                    invoice.Ship_To_Address__c = invoice.Ship_To_Address__c.replaceAll(', \r\n','\r\n').replaceAll(',\r\n','\r\n');
                    invoice.Ship_To_Address__c = invoice.Ship_To_Address__c.replaceAll(', \n','\n').replaceAll(',\n','\n');
                }
                if(partnerAssociations.containsKey(invoice.Sold_To__c)){
                    ERP_Partner_Association__c ep = partnerAssociations.get(invoice.Sold_To__c);
                    invoice.Sold_To_Address__c = addressFormat.replace('<<PARTNERNAME2>>',ep.ERP_Partner__r.Partner_Name_Line_2__c!=null?ep.ERP_Partner__r.Partner_Name_Line_2__c:'').replace('<<PARTNERNAME3>>',ep.ERP_Partner__r.Partner_Name_Line_3__c!=null?ep.ERP_Partner__r.Partner_Name_Line_3__c:'');
                    invoice.Sold_To_Address__c = invoice.Sold_To_Address__c.replace('<<HOUSENO1>>',ep.ERP_Partner__r.House_No_1__c!=null?ep.ERP_Partner__r.House_No_1__c:'').replace('<<HOUSENO2>>',ep.ERP_Partner__r.House_No_2__c!=null?ep.ERP_Partner__r.House_No_2__c:'').replace('<<STREET1>>',ep.Partner_Street__c!=null?ep.Partner_Street__c:'').replace('<<STREET2>>',ep.Partner_Street_line_2__c!=null?ep.Partner_Street_line_2__c:'').replace('<<CITY>>',ep.Partner_City__c!=null?ep.Partner_City__c:'').replace('<<STATE>>',ep.ERP_Partner__r.Region_Desc__c!=null?ep.ERP_Partner__r.Region_Desc__c:'').replace('<<COUNTRY>>',ep.ERP_Partner__r.Country__c!=null?ep.ERP_Partner__r.Country__c:'').replace('<<POSTALCODE>>',ep.Partner_Zipcode_postal_code__c!=null?ep.Partner_Zipcode_postal_code__c:'');
                    invoice.Sold_To_Address__c = invoice.Sold_To_Address__c.replaceAll('\r\n\r\n','\r\n').replaceAll('\n\n','\n');
                    invoice.Sold_To_Address__c = invoice.Sold_To_Address__c.replaceAll(', ,',',').replaceAll(',,',',');
                    invoice.Sold_To_Address__c = invoice.Sold_To_Address__c.replaceAll(', \r\n','\r\n').replaceAll(',\r\n','\r\n');
                    invoice.Sold_To_Address__c = invoice.Sold_To_Address__c.replaceAll(', \n','\n').replaceAll(',\n','\n');
                }
                if(partnerAssociations.containsKey(invoice.Payer__c)){
                    ERP_Partner_Association__c ep = partnerAssociations.get(invoice.Payer__c);
                    invoice.Payer_Address__c = addressFormat.replace('<<PARTNERNAME2>>',ep.ERP_Partner__r.Partner_Name_Line_2__c!=null?ep.ERP_Partner__r.Partner_Name_Line_2__c:'').replace('<<PARTNERNAME3>>',ep.ERP_Partner__r.Partner_Name_Line_3__c!=null?ep.ERP_Partner__r.Partner_Name_Line_3__c:'');
                    invoice.Payer_Address__c = invoice.Payer_Address__c.replace('<<HOUSENO1>>',ep.ERP_Partner__r.House_No_1__c!=null?ep.ERP_Partner__r.House_No_1__c:'').replace('<<HOUSENO2>>',ep.ERP_Partner__r.House_No_2__c!=null?ep.ERP_Partner__r.House_No_2__c:'').replace('<<STREET1>>',ep.Partner_Street__c!=null?ep.Partner_Street__c:'').replace('<<STREET2>>',ep.Partner_Street_line_2__c!=null?ep.Partner_Street_line_2__c:'').replace('<<CITY>>',ep.Partner_City__c!=null?ep.Partner_City__c:'').replace('<<STATE>>',ep.ERP_Partner__r.Region_Desc__c!=null?ep.ERP_Partner__r.Region_Desc__c:'').replace('<<COUNTRY>>',ep.ERP_Partner__r.Country__c!=null?ep.ERP_Partner__r.Country__c:'').replace('<<POSTALCODE>>',ep.Partner_Zipcode_postal_code__c!=null?ep.Partner_Zipcode_postal_code__c:'');
                    invoice.Payer_Address__c = invoice.Payer_Address__c.replaceAll('\r\n\r\n','\r\n').replaceAll('\n\n','\n');
                    invoice.Payer_Address__c = invoice.Payer_Address__c.replaceAll(', ,',',').replaceAll(',,',',');
                    invoice.Payer_Address__c = invoice.Payer_Address__c.replaceAll(', \r\n','\r\n').replaceAll(',\r\n','\r\n');
                    invoice.Payer_Address__c = invoice.Payer_Address__c.replaceAll(', \n','\n').replaceAll(',\n','\n');
                }
                if(legalEntities.containsKey(invoice.LegalEntity__c)){
                    blng__LegalEntity__c le = legalEntities.get(invoice.LegalEntity__c);
                    invoice.Legal_Entity_Address__c = addressFormat.replace('<<PARTNERNAME2>>','').replace('<<PARTNERNAME3>>','');
                    invoice.Legal_Entity_Address__c = invoice.Legal_Entity_Address__c.replace('<<HOUSENO1>>','').replace('<<HOUSENO2>>','').replace('<<STREET1>>',le.blng__Street1__c!=null?le.blng__Street1__c:'').replace('<<STREET2>>',le.blng__Street2__c!=null?le.blng__Street2__c:'').replace('<<CITY>>',le.blng__City__c!=null?le.blng__City__c:'').replace('<<STATE>>',le.blng__State__c!=null?le.blng__State__c:'').replace('<<COUNTRY>>',le.blng__Country__c!=null?le.blng__Country__c:'').replace('<<POSTALCODE>>',le.blng__ZipPostalCode__c!=null?le.blng__ZipPostalCode__c:'');
                    invoice.Legal_Entity_Address__c = invoice.Legal_Entity_Address__c.replaceAll('\r\n\r\n','\r\n').replaceAll('\n\n','\n');
                    invoice.Legal_Entity_Address__c = invoice.Legal_Entity_Address__c.replaceAll(', ,',',').replaceAll(',,',',');
                    invoice.Legal_Entity_Address__c = invoice.Legal_Entity_Address__c.replaceAll(', \r\n','\r\n').replaceAll(',\r\n','\r\n');
                    invoice.Legal_Entity_Address__c = invoice.Legal_Entity_Address__c.replaceAll(', \n','\n').replaceAll(',\n','\n');
                }
            }
        }
    }
    
    //Update Product description translations and culture on invoice lines and invoice respectively. 
    private void handleInvoiceLanguageChange(Map<Id,blng__Invoice__c> updatedInvoices){
        Set<String> countries = new Set<String>();
        Map<Id, String> invoiceLanguages = new Map<Id, String>();
        Set<String> productCodes = new Set<String>();
        Map<String, blng__Invoice__c> countryLanguageInvoiceMap = new Map<String, blng__Invoice__c>();
        Map<String, Product_Description_Language__c> productTranslations = new Map<String, Product_Description_Language__c>();
        Map<String, String> cultureCodes = new Map<String, String>();
        for(blng__Invoice__c inv:updatedInvoices.values()){
            countries.add(inv.Deliver_To_Country__c);
            invoiceLanguages.put(inv.Id, inv.Invoice_language__c);
            
        }
        for(CPQ_Culture_Code__c culture : [
            SELECT Id, Culture_Code__c, Country__r.Name, Language__c FROM CPQ_Culture_Code__c WHERE Country__r.Name IN:countries AND Language__c IN:invoiceLanguages.values()
        ]){
            cultureCodes.put(culture.Country__r.Name+culture.Language__c, culture.Culture_Code__c);
        }
        
        List<blng__InvoiceLine__c> invoiceLines = [
            SELECT Id, blng__Product__r.ProductCode, blng__Invoice__c FROM blng__InvoiceLine__c WHERE blng__Invoice__c IN:updatedInvoices.keySet()
        ];
        for(blng__InvoiceLine__c invoiceLine : invoiceLines){
            productCodes.add(invoiceLine.blng__Product__r.ProductCode);
        }
        for(Product_Description_Language__c pdl : [
            SELECT Id, Product_Code__c, Language_Name__c FROM Product_Description_Language__c WHERE Product_Code__c IN:productCodes AND Language_Name__c IN:invoiceLanguages.values()
        ]){
            productTranslations.put(pdl.Product_Code__c+pdl.Language_Name__c, pdl);
        }
        //update invoice culture code
        for(blng__Invoice__c inv:updatedInvoices.values()){
            if(cultureCodes.containsKey(inv.Deliver_To_Country__c+inv.Invoice_language__c)){
                inv.Master_Quote_Culture_Code__c = cultureCodes.get(inv.Deliver_To_Country__c+inv.Invoice_language__c);
            }
        }
        
        //update invoice lines
        for(blng__InvoiceLine__c invLine : invoiceLines){
            String key = invLine.blng__Product__r.ProductCode+invoiceLanguages.get(invLine.blng__Invoice__c);
            if(productTranslations.containsKey(key)){
                invLine.Product_Description_Translation__c = productTranslations.get(key).Id;
            }
        }
        
        if(Test.isRunningTest() == false) {
            update invoiceLines;
        }
    }
    
    //Rollup pending billing and billed totals from order to master quote
    //Not used aggregate queries as it converts currency field aggregate values to org currency
    //STRY0545711
    private static void rollupBilledUnbilledTotalsOnMasterQuote(Set<Id> masterQuoteIds){
        List<OrderItem> orderItems = [
            SELECT Id, blng__BilledAmountwithouttax__c, TotalPrice, Order.MasterQuote__c 
            FROM OrderItem 
            WHERE Order.MasterQuote__c IN:masterQuoteIds
        ];
        
        Map<Id, SBQQ__Quote__c> quoteTotals = new Map<Id, SBQQ__Quote__c>();
        Map<Id, Decimal> billedTotals = new Map<Id, Decimal>();
        
        for(OrderItem oi : orderItems){
            if(!quoteTotals.containsKey(oi.Order.MasterQuote__c)){
                quoteTotals.put(
                    oi.Order.MasterQuote__c, 
                    new SBQQ__Quote__c(
                        Id = oi.Order.MasterQuote__c, PendingBillings__c = (oi.TotalPrice - oi.blng__BilledAmountwithouttax__c), 
                        BilledAmount__c = oi.blng__BilledAmountwithouttax__c
                    )
                );
                continue;
            }
            SBQQ__Quote__c masterQuote = quoteTotals.get(oi.Order.MasterQuote__c);
            masterQuote.PendingBillings__c = masterQuote.PendingBillings__c + (oi.TotalPrice - oi.blng__BilledAmountwithouttax__c);
            masterQuote.BilledAmount__c = masterQuote.BilledAmount__c + oi.blng__BilledAmountwithouttax__c;
            quoteTotals.put(oi.Order.MasterQuote__c, masterQuote);
        }
        
        if(!quoteTotals.isEmpty()) update quoteTotals.values();
        
    }
}
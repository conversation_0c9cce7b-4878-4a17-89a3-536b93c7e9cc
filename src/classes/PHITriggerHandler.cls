public with sharing class PH<PERSON>riggerHandler extends TriggerHandler{
    private Map<Id, PHI_Log__c> newPHIMap;
    private Map<Id, PHI_Log__c> oldPHIMap;
    private List<PHI_Log__c> newPHIList;

    public PHITriggerHandler() {
        this.newPHIMap = (Map<Id, PHI_Log__c>) Trigger.newMap;
        this.oldPHIMap = (Map<Id, PHI_Log__c>) Trigger.oldMap;
        this.newPHIList = (List<PHI_Log__c>) Trigger.new;
        //If trigger is turned off at cmdt level, then we bypass entirely.
        //STRY0462726 - Moved trigger logic check to TriggerFlag__mdt
        //if(TriggerSettings__mdt.getInstance('Single_Row_for_Trigger_Value').PHITriggerDisabled__c){
        
        //STRY0462726
        Map<String, TriggerFlag__mdt> mcs = TriggerFlag__mdt.getAll();
        if(mcs.get('PHI') != null && mcs.get('PHI').Disabled__c) {
            TriggerHandler.bypass('PHITriggerHandler');
            System.debug('handler TTOTriggerHandler bypassed: '+TriggerHandler.isBypassed('TTOTriggerHandler'));
        }
    }
    
     public override void beforeInsert(){
          System.debug('in before insert');
          PHITriggerServices.populateBeforeField(newPHIList,null);
          /* **** As per Vickie and Arslan, we don't need sharepoint related updates with new PHI process**** */
          //PHITriggerServices.updateSPO(newPhi,null);
          
     }

     public override void afterInsert(){
          System.debug('in after insert');
          SObjectHistoryProcessor.trackHistory('PHI_Log__c');
     }

     public override void beforeUpdate() {
        System.debug('in before update');
        deactivateRecursionControl('PHITriggerHandler');
        PHITriggerServices.populateBeforeField(newPHIList,oldPHIMap);
        /* **** As per Vickie and Arslan, we don't need sharepoint related updates with new PHI process**** */
        //PHITriggerServices.updateSPO(newPhiMap.values(),oldPhiMap);
        //PHITriggerServices.renameFolderOnUpdateRecord(newPhiMap.values(),oldPhiMap);
    }

     public override void afterUpdate() {
        System.debug('in after update');
        SObjectHistoryProcessor.trackHistory('PHI_Log__c');
    }
   

}
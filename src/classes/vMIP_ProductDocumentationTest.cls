@isTest
public class vMIP_ProductDocumentationTest {
    static vMTP_App__c  app;  
    static Account account;
    static Contact Con; 
    static Profile  Prof, portal;  
    static User Usr, customer1;  static ContentDocumentLink contentlink ;   
    static {
        
        portal = vMCDataUtilityTest.getPortalProfile();
        //Usr  = vMCDataUtilityTest.createStandardUser('Customer',prof.Id,'Test1',true);
        Usr  = vMCDataUtilityTest.getSystemAdmin();
        account = vMCDataUtilityTest.createAccount('Mayo',true);       	
        Con = vMCDataUtilityTest.contact_data(account,true); 
        Con.oktaid__c ='abhishekkolipe67936';
        update Con;
        customer1 = vMCDataUtilityTest.createPortalUser(true, 'Customer', false, portal.Id, Con.Id, 'Customer1');
        app   = vMCDataUtilityTest.createApp(true);     
        app = vMCDataUtilityTest.createApp(true);     
        contentlink =  vMCDataUtilityTest.createContentDocument(app, true);
    }
    
    private static testMethod void TestMethod1(){  
        Test.startTest();
        System.runAs(customer1) {
            vMIP_ProductDocumentation.getlContentVersions();
            vMIP_ProductDocumentation.getTerritory();
             
        }
 Test.stopTest();
    }
}
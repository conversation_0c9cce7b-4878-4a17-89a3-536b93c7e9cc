global class wowrapper implements comparable{
        
        @Auraenabled public DateTime CreatedDate;
        @Auraenabled public Date manstartdate;
        @Auraenabled public String subject;
        @Auraenabled public String casenumber;
        @Auraenabled public String productgroup;
        @Auraenabled public String status;
        @Auraenabled public String priority;
        @Auraenabled public String assetname;
        @Auraenabled public String accountname;
    	@Auraenabled public String accountid;
        @Auraenabled public String contactname;
     	@Auraenabled public String wonumber;
   	    @Auraenabled public String fsrreport;
   	    @Auraenabled public String fsdocid;
        
        public wowrapper(Datetime cd,String sub,String casenum,String prodgroup,String sts,String pri,String assetname,
                         String actname,String contname,String actid,string wonum,String reportid,Date manstartdate,String fsdocid)
        {
            this.CreatedDate = cd;
            this.subject = sub;
            this.casenumber = casenum;
            this.productgroup = prodgroup;
            this.status = sts;
            this.priority = pri;
            this.assetname = assetname;
            this.accountname = actname;
            this.contactname = contname;
            this.accountid = actid;
            this.wonumber = wonum;
            this.fsrreport = reportid;
            this.manstartdate = manstartdate;
            this.fsdocid = fsdocid;
        }
 		global Integer compareTo(Object objToCompare) 
    	{
        	wowrapper that = (wowrapper)objToCompare;
        	if (this.CreatedDate > that.CreatedDate) return 1;
        	if (this.CreatedDate < that.CreatedDate) return -1;
        	return 0;
    	}
    }
@isTest
public class ContentDocument_Test {

    @TestSetup
    static void setupData(){

        //One of tests is on Work Order so we need to set up pre-requesites
        Account acct = VFSL_TestDataUtility.createAccount('Test Account');
        insert acct;

        //Test charket from AutoDeliverContentTriggerTest
        /*Charket__WeChatAccount__c weChatAcct = new Charket__WeChatAccount__c();
        weChatAcct.Name = 'Test Account';
        weChatAcct.Charket__WeChatOriginId__c = '';
        weChatAcct.Charket__AppId__c = '';
        insert weChatAcct;*/

        Contact cont = VFSL_TestDataUtility.createContact(acct.Id);
        insert cont;

        ServiceTerritory st1 = VFSL_TestDataUtility.createServiceTerritory('Test Territory 3', 'TES3','Resource_Territory');
        insert st1;

        ServiceResource sr1 = VFSL_TestDataUtility.createServiceResource('TestResource3',st1,'Technician');
        insert sr1;

        Schema.Location loc = VFSL_TestDataUtility.createLocationWithArguments(acct,'Test Location');
        loc.LocationType = 'Customer';
        loc.Active__c = true;
        loc.Service_Territory_Code__c = 'TEST';
        insert loc;

        ERP_PCode__c erpPcode = VFSL_TestDataUtility.getERPPCode();
        insert erpPcode; 

        Product2 prod = VFSL_TestDataUtility.getProduct(erpPcode.Id);
        prod.Product_Type__c = 'Model';
        insert prod;

        Asset a1 = VFSL_TestDataUtility.createAssetWithArguments(acct,cont,loc,sr1,st1,'Test Asset');
        a1.Product2Id = prod.Id;
        insert a1;

        Test.startTest();
        Case ca = VFSL_TestDataUtility.createCaseWithArguments(acct, cont, loc, a1, sr1, st1);
        ca.OwnerId = UserInfo.getUserId();   
        insert ca;

        WorkOrder wo1 = VFSL_TestDataUtility.createWorkOrderWithArguments(acct,a1,loc,ca,sr1,st1,'Field_Service');
        WorkOrder wo2 = VFSL_TestDataUtility.createWorkOrderWithArguments(acct,a1,loc,ca,sr1,st1,'Helpdesk');
        database.insert(new List<WorkOrder>{wo1,wo2},false);

        //Test charket from AutoDeliverContentTriggerTest
        /*
        Id charketId;
        Id acuityId;
        List<ContentWorkspace> listCW = [SELECT Id,Name FROM ContentWorkspace 
                                         WHERE Name = 'Charket – documentation' OR Name = 'Acuity'];
        for(ContentWorkspace cw : listCW){
            if(cw.Name == 'Charket – documentation'){
                charketId = cw.Id;
            }else{
                acuityId = cw.Id;
            }
        }
        System.runAs(new User(Id = UserInfo.getUserId())){
            insertPerms(charketId,acuityId); 
        }*/

        //ContentDocument is created by system when ContVersion inserted.
        ContentVersion cv1 = new ContentVersion();
        cv1.Title = 'Test Parent';
        cv1.PathOnClient = 'TestParent.jpg';
        cv1.VersionData = Blob.valueOf('Test Content');
        cv1.Document_Number__c = 'Test1';
        ContentVersion cv2 = new ContentVersion();
        cv2.Title = 'Test Child';
        cv2.PathOnClient = 'TestChild.jpg';
        cv2.VersionData = Blob.valueOf('Test Content');
        cv2.Parent_Documentation__c = 'Test1';
        ContentVersion cv3 = new ContentVersion();
        cv3.Title = 'APTS - ACTIVATED.';
        cv3.PathOnClient = 'APTS.jpg';
        cv3.VersionData = Blob.valueOf('Test Content');
        ContentVersion cv4 = new ContentVersion();
        cv4.Title = 'Test New';
        cv4.PathOnClient = 'TestNew.jpg';
        cv4.VersionData = Blob.valueOf('Test New');
        //Test charket from AutoDeliverContentTriggerTest
        /*ContentVersion cv5 = new ContentVersion();
        cv5.Title = 'Test WeChat';
        cv5.PathOnClient = 'TestWeChat.jpg';
        cv5.VersionData = Blob.valueOf('Test WeChat');
        cv5.FirstPublishLocationId = charketId;
        ContentVersion cv6 = new ContentVersion();
        cv6.Title = 'Test WeChat Old';
        cv6.PathOnClient = 'TestWeChatOld.jpg';
        cv6.VersionData = Blob.valueOf('Test WeChat Old');
        cv6.RecordTypeId = pdRecId;*/
        database.insert(new List<ContentVersion>{cv1,cv2,cv3,cv4},false);

        List<ContentVersion> listCV = [SELECT Id,ContentDocumentId 
                                       FROM ContentVersion WHERE Id = :cv4.Id OR Id = :cv2.Id];

        ContentDocumentLink cdl1 = new ContentDocumentLink();
        cdl1.LinkedEntityId = wo2.id;
        cdl1.ContentDocumentId = listCV[0].ContentDocumentId;
        cdl1.ShareType = 'V';
        ContentDocumentLink cdl2 = new ContentDocumentLink();
        cdl2.LinkedEntityId = wo2.id;
        cdl2.ContentDocumentId = listCV[1].ContentDocumentId;
        cdl2.ShareType = 'V';
        database.insert(new List<ContentDocumentLink>{cdl1,cdl2},false);

        Test.stopTest();
    }

    //Test charket from AutoDeliverContentTriggerTest
    /*private static void insertPerms(Id charketId, Id acuityId){
        ContentWorkspacePermission cwp = new ContentWorkspacePermission();
        cwp.Name = 'Charket – documentation';
        cwp.PermissionsManageWorkspace = true;
        cwp.PermissionsAddContent = true;
        cwp.PermissionsDeleteContent = true;
        cwp.PermissionsAddContent = true;
        cwp.PermissionsDeliverContent = true;
        cwp.PermissionsOrganizeFileAndFolder = true;
        insert cwp;

        Id userId = UserInfo.getUserId();

        List<ContentWorkspaceMember> listCWMUpsert = new List<ContentWorkspaceMember>();

        List<ContentWorkspaceMember> cwmCharket = [SELECT Id FROM ContentWorkspaceMember
                                                    WHERE MemberId = :userId
                                                    AND ContentWorkspaceId = :charketId LIMIT 1];
        if(cwmCharket.size() > 0){
            cwmCharket[0].ContentWorkspacePermissionId = cwp.Id;
            listCWMUpsert.add(cwmCharket[0]);
        }else{
            ContentWorkspaceMember newCWM = new ContentWorkspaceMember();
            newCWM.ContentWorkspaceId = charketId;
            newCWM.ContentWorkspacePermissionId = cwp.Id;
            newCWM.MemberId = userId;
            listCWMUpsert.add(newCWM);
        }

        List<ContentWorkspaceMember> cwmAcuity = [SELECT Id FROM ContentWorkspaceMember
                                                  WHERE MemberId = :userId
                                                    AND ContentWorkspaceId = :acuityId LIMIT 1];
        if(cwmAcuity.size() > 0){
            cwmAcuity[0].ContentWorkspacePermissionId = cwp.Id;
            listCWMUpsert.add(cwmAcuity[0]);
        }else{
            ContentWorkspaceMember newCWMAcuity = new ContentWorkspaceMember();
            newCWMAcuity.ContentWorkspaceId = acuityId;
            newCWMAcuity.ContentWorkspacePermissionId = cwp.Id;
            newCWMAcuity.MemberId = userId;
            listCWMUpsert.add(newCWMAcuity);
        }

        upsert listCWMUpsert;
    }*/

    @isTest
    static void updateParentContentToArchivedUpdatesChildContent(){
        ContentVersion cvParent = [SELECT Id,ContentDocumentId 
                                   FROM ContentVersion WHERE Title = 'Test Parent' LIMIT 1];
        ContentDocument cdParent = [SELECT Id,IsArchived 
                                    FROM ContentDocument WHERE Id = :cvParent.ContentDocumentId LIMIT 1];
        
        Test.startTest();
        cdParent.IsArchived = true;
        update cdParent;
        Test.stopTest();
        ContentVersion cvChild = [SELECT Id,ContentDocumentId 
                                  FROM ContentVersion WHERE Title = 'Test Child' LIMIT 1 ALL ROWS];
        ContentDocument cdChild = [SELECT Id,IsArchived 
                                   FROM ContentDocument WHERE Id = :cvChild.ContentDocumentId LIMIT 1 ALL ROWS];
        System.assert(cdChild.IsArchived, 'Child should be archived');
    }

    @isTest
    static void updateParentContentToNotArchivedUpdatesChildContent(){
        ContentVersion cvParent = [SELECT Id,ContentDocumentId 
                                   FROM ContentVersion WHERE Title = 'Test Parent' LIMIT 1];
        ContentDocument cdParent = [SELECT Id,IsArchived 
                                    FROM ContentDocument WHERE Id = :cvParent.ContentDocumentId LIMIT 1];
        
        Test.startTest();
        cdParent.IsArchived = false;
        update cdParent;
        Test.stopTest();
        ContentVersion cvChild = [SELECT Id,ContentDocumentId 
                                  FROM ContentVersion WHERE Title = 'Test Child' LIMIT 1 ALL ROWS];
        ContentDocument cdChild = [SELECT Id,IsArchived 
                                   FROM ContentDocument WHERE Id = :cvChild.ContentDocumentId LIMIT 1 ALL ROWS];
        System.assert(!cdChild.IsArchived, 'Child should no longer be archived');
    }

    @isTest
    static void deleteParentContentDeletesChildContent(){
        ContentVersion cvParent = [SELECT Id,ContentDocumentId 
                                   FROM ContentVersion WHERE Title = 'Test Parent' LIMIT 1];
        ContentDocument cdParent = [SELECT Id,IsArchived 
                                    FROM ContentDocument WHERE Id = :cvParent.ContentDocumentId LIMIT 1];
        
        Test.startTest();
        delete cdParent;
        Test.stopTest();
        List<ContentVersion> listChildCV = [SELECT Id,ContentDocumentId 
                                            FROM ContentVersion WHERE Title = 'Test Child'];
        System.assert(listChildCV.size() == 0, 'Child should be deleted');
    }

    @isTest
    static void deleteAPTSFileNameShouldBeBlocked(){
        ContentVersion cvApts = [SELECT Id,ContentDocumentId 
                                 FROM ContentVersion WHERE Title = 'APTS - ACTIVATED.' LIMIT 1];
        
        ContentDocument cd = [SELECT Id FROM ContentDocument WHERE Id = :cvApts.ContentDocumentId LIMIT 1];
        
        Test.startTest();
        try{
            delete cd;
        }catch(DmlException e){
            System.assert(e.getMessage().length()>0, 'Message blocking deletion returned');
        }
        Test.stopTest();
    }

    @isTest
    static void addFileToWorkOrderShouldIncrementCount(){
        WorkOrder woBefore = [SELECT Id,Count_of_Attachments__c FROM WorkOrder LIMIT 1];

        Test.startTest();
        ContentVersion cvNew = new ContentVersion();
        cvNew.Title = 'Test WO Attach';
        cvNew.PathOnClient = 'TestWOAttach.pdf';
        cvNew.VersionData = Blob.valueOf('Test Content');
        ContentVersion cvNew2 = new ContentVersion();
        cvNew2.Title = 'Test WO Attach 2';
        cvNew2.PathOnClient = 'TestWOAttach2.pdf';
        cvNew2.VersionData = Blob.valueOf('Test Content');
        database.insert(new List<ContentVersion>{cvNew,cvNew2},false);

        List<ContentVersion> listCV = [SELECT Id,ContentDocumentId 
                                       FROM ContentVersion WHERE Id = :cvNew.Id OR Id = :cvNew2.Id];

        ContentDocumentLink cdl1 = new ContentDocumentLink();
        cdl1.LinkedEntityId = woBefore.Id;
        cdl1.ContentDocumentId = listCV[0].ContentDocumentId;
        cdl1.ShareType = 'V';
        ContentDocumentLink cdl2 = new ContentDocumentLink();
        cdl2.LinkedEntityId = woBefore.Id;
        cdl2.ContentDocumentId = listCV[1].ContentDocumentId;
        cdl2.ShareType = 'V';
        database.insert(new List<ContentDocumentLink>{cdl1,cdl2},false);
        Test.stopTest();

        WorkOrder woAfter = [SELECT Id,Count_of_Attachments__c FROM WorkOrder LIMIT 1];

        System.assert(woAfter.Count_of_Attachments__c == 2, 'Count should be incremented');
    }

    @isTest
    static void deleteFileToWorkOrderShouldDecrementCount(){
        WorkOrder woBefore = [SELECT Id,Count_of_Attachments__c 
                              FROM WorkOrder WHERE Count_of_Attachments__c = 2 LIMIT 1];

        Set<Id> setCDId = new Set<Id>();                      
        List<ContentDocumentLink> listCDL = [SELECT Id,ContentDocumentId 
                                             FROM ContentDocumentLink 
                                             WHERE LinkedEntityId = :woBefore.Id];
        for(ContentDocumentLink cdl : listCDL){
            setCDId.add(cdl.ContentDocumentId);
        }

        List<ContentDocument> listCD = [SELECT Id 
                                        FROM ContentDocument WHERE Id IN :setCDId];

        Test.startTest();
        delete listCD;
        Test.stopTest();

        WorkOrder woAfter = [SELECT Id,Count_of_Attachments__c 
                             FROM WorkOrder WHERE Id = :woBefore.Id LIMIT 1];

        //Class is not bulkified properly, so this will only ever decrement by 1 at a time.
        System.assert(woAfter.Count_of_Attachments__c == 1, 'Count should be decremented');
    }

    //Test charket from AutoDeliverContentTriggerTest
    /*
    @isTest 
    static void addToCharketLibraryShouldPublishRelease(){
        Id notWeChatId = [SELECT Id FROM ContentWorkspace WHERE Name = 'Acuity' LIMIT 1]?.Id;
        Id libraryId = [SELECT Id FROM ContentWorkspace WHERE Name = 'Charket – documentation' LIMIT 1]?.Id;
        ContentVersion cv = [SELECT Id,ContentDocumentId 
                             FROM ContentVersion WHERE Title = 'Test WeChat Old' LIMIT 1];
        ContentWorkspaceDoc cwd1 = new ContentWorkspaceDoc();
        cwd1.ContentDocumentId = cv.ContentDocumentId;
        cwd1.ContentWorkspaceId = notWeChatId;   
        insert cwd1;

        ContentDocument cd = [SELECT Id,ParentId FROM ContentDocument 
                              WHERE Id = :cv.ContentDocumentId LIMIT 1];
        Test.startTest();
        cd.ParentId = libraryId;
        update cd;
        Test.stopTest();
    }*/

    @isTest
    static void undeleteCoverageNoCurrentLogic(){
        ContentDocument cd = [SELECT Id FROM ContentDocument LIMIT 1];

        Test.startTest();
        delete cd;
        undelete cd;
        Test.stopTest();
        //No action taking place on undelete.
    }

}
/**
  * <AUTHOR>
  * @description Extension controller for Visualforce Page - 'Add CLM Licenses'
  *
**/

public with sharing class APTS_CLM_ViewAllLicensesController {

    public APTS_CLM_ViewAllLicensesController(ApexPages.StandardController controller) {}
    public APTS_CLM_ViewAllLicensesController(ApexPages.StandardSetController controller) {}
    List<LicenseRecord> licenses {get;set;}
    public List<CLM_License_Financial__c> selectedLicenses;
    
    // instantiate the StandardSetController from a query locator
    public ApexPages.StandardSetController con {
        get {
            if(con == null) {
                String financialId = ApexPages.currentPage().getParameters().get('id');
                CLM_Financial__c financial= [SELECT Agreement__c FROM CLM_Financial__c 
                                             WHERE Id = :financialId];
                
                List<CLM_License__c> licensesOfParentAgreement = [SELECT Id,Name,Product__c,License_Matrix__c,Net_License_Fee__c,Quantity__c FROM CLM_License__c
                                                                  WHERE Agreement__c =: financial.Agreement__c 
                                                                  ORDER BY Name];
                Set<Id> licenseIdsOfAgreement = new Set<Id>();
                for(CLM_License__c lic:licensesOfParentAgreement){
                   licenseIdsOfAgreement.add(lic.Id);
                }
                //System.debug('licenseIds '+licenseIds);
        
                List<CLM_License_Financial__c> listOfExistingRel = [SELECT CLM_Financial__c,CLM_License__c FROM CLM_License_Financial__c 
                                                                    WHERE CLM_Financial__c =:financialId 
                                                                    AND CLM_License__c IN: licenseIdsOfAgreement];
                //System.debug('listOfFinIds '+listOfFinIds);
                Set<Id> licensesIds = new Set<Id>();
                for(CLM_License_Financial__c finLic:listOfExistingRel){
                   licensesIds.add(finLic.CLM_License__c);   
                }
                List<CLM_License__c> newLicenses = new List<CLM_License__c>();
                for(CLM_License__c lic : licensesOfParentAgreement){
                    if(!licensesIds.contains(lic.id)){
                        newLicenses.add(lic);
                    }
                }
                System.debug('newLicenses '+newLicenses.size());
                    
                con = new ApexPages.StandardSetController(newLicenses);
                // sets the number of records in each page set
                con.setPageSize(10);
            }
            return con;
        }
        set;
    }
    
    // returns a list of wrapper objects for the sObjects in the current page set
    public List<LicenseRecord> getLicenses() {
        licenses = new List<LicenseRecord>();
        for (CLM_License__c license : (List<CLM_License__c>)con.getRecords())
            licenses.add(new LicenseRecord(license));
        return licenses;
    }

    // adds the selected items to the Related list
    public void process() {
        selectedLicenses = new List<CLM_License_Financial__c>();
        String FinancialId = ApexPages.currentPage().getParameters().get('id');
        CLM_Financial__c financial = [Select Id From CLM_Financial__c Where Id = :FinancialId];
        
        for(LicenseRecord ar : licenses)
        {
            if(ar.selected)
            {
                CLM_License_Financial__c license = new CLM_License_Financial__c();
                license.CLM_License__c = ar.license.id;
                license.CLM_Financial__c = financial.id;
                selectedLicenses.add(license);
            }
        }
        insert selectedLicenses;
        //PageReference pr = new ApexPages.StandardController(financial).view();
        //pr.setRedirect(true);
        //return new PageReference('/'+FinancialId);
        //return pr;
    }

    // indicates whether there are more records after the current page set.
    public Boolean hasNext {
        get {
            return con.getHasNext();
        }
        set;
    }

    // indicates whether there are more records before the current page set.
    public Boolean hasPrevious {
        get {
            return con.getHasPrevious();
        }
        set;
    }

    // returns the page number of the current page set
    public Integer pageNumber {
        get {
            return con.getPageNumber();
        }
        set;
    }

    // returns the first page of records
    public void first() {
        con.first();
    }

    // returns the last page of records
    public void last() {
        con.last();
    }

    // returns the previous page of records
    public void previous() {
        con.previous();
    }

    // returns the next page of records
    public void next() {
        con.next();
    }

    // returns the PageReference of the original page, if known, or the home page.
    public void cancel() {
        con.cancel();
    }
    
    // wrapper class
    public class LicenseRecord {
        public Boolean selected {get;set;}
        public CLM_License__c license{get;set;}
        public LicenseRecord (CLM_License__c license){
            selected = false;
            this.license = license;
        } 
    }
    
}
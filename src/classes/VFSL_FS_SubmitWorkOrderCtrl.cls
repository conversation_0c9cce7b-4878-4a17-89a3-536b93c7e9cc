/**
 * <AUTHOR>
 * @created      2019-06-03
 *
 * -------------------------------------------------------------------
 * Controller for FS Work Order Submit Button
 * -------------------------------------------------------------------
 */
public class VFSL_FS_SubmitWorkOrderCtrl {

    private static final String ECF_IS_REQUIRED = 'Yes from Work Order';  
    private static final String SP_EMERGENCY = '1 - Emergency';
    private static final String AT_EMERGENCY = 'Emergency Service';
    private static final String STATUS_DNP = 'Do Not Process';
    private static final String STATUS_NA = 'Not Applicable';
    private static final List<String> woStatusList = new List<String>{'Closed', 'Approved', 'Submitted', 'Error'};
    public static Set<WorkOrderLineItem> woliRecordsSet = new Set<WorkOrderLineItem>();                                                     

    @AuraEnabled
    public static String getCurrentTimezoneKey() {
        String timeZoneKey = [Select Id, TimeZoneSidKey from User where Id=:userInfo.getUserId()].TimeZoneSidKey;
        if(timeZoneKey == null || timeZoneKey == '')
            timeZoneKey = 'America/Los_Angeles';

        return timeZoneKey;
    }

    public class WorkOrderResult {
        @AuraEnabled public String message;
        @AuraEnabled public Boolean isMessage;
        @AuraEnabled public WorkOrder workOrder;
        @AuraEnabled public List<WorkOrderLineItem> activityLineItems;
        @AuraEnabled public List<PartsInstalled> partsInstalledLineItems;
        @AuraEnabled public List<RemainingParts> remainingParts;
        @AuraEnabled public List<RemainingPartsForRemoval> remainingPartsRemoval;
        @AuraEnabled public List<Tools> toolsLineItems;
        @AuraEnabled public List<Ecf> ecfRecord;
        @AuraEnabled public Datetime latestWoliEndDate;
        @AuraEnabled public List<RemovedParts> removedPartsLineItems;
        @AuraEnabled public List<WorkOrderLineItem> activeLineItems;
        @AuraEnabled public List<RemainingPartsForRemoval> partsReqLineItems;

        public WorkOrderResult(String msg, Boolean isMsg, WorkOrder woObj, List<WorkOrderLineItem> actLineItems,
                                 List<PartsInstalled> partsInstalledWrap, List<RemainingParts> partswrap,
                                 List<RemainingPartsForRemoval> partsForRemovalWrap, List<Tools> toolsWrap,
                                 List<Ecf> ecfWrap, DateTime latestEndDate, List<RemovedParts> removedPartsWrap,
                                 List<RemainingPartsForRemoval> partsForRemovalReq) {
            message = msg;
            isMessage = isMsg;
            workOrder = woObj;
            activityLineItems = actLineItems;
            partsInstalledLineItems = partsInstalledWrap;
            remainingParts = partswrap;
            remainingPartsRemoval = partsForRemovalWrap;
            toolsLineItems = toolsWrap;
            ecfRecord = ecfWrap;
            latestWoliEndDate = latestEndDate;
            removedPartsLineItems = removedPartsWrap;
            partsReqLineItems = partsForRemovalReq;
            if(woliRecordsSet.size() > 0)
                activeLineItems = new List<WorkOrderLineItem>(woliRecordsSet);
        }
    }

    public class PartsInstalled {
        @AuraEnabled public String lineNumber;
        @AuraEnabled public String lineType;
        @AuraEnabled public String installedPart;
        @AuraEnabled public String asset;
        @AuraEnabled public String installedQty;
        @AuraEnabled public String lineStatus;
    }

    public class RemainingParts {
        @AuraEnabled public String partName;
        @AuraEnabled public String partNumber;
        @AuraEnabled public String installedQty;
        @AuraEnabled public String remainingQty;
        @AuraEnabled public String lineStatus;
        @AuraEnabled public String shipmentType;
    }

    public class RemainingPartsForRemoval {
        @AuraEnabled public Id lineId;
        @AuraEnabled public String lineNumber;
        @AuraEnabled public String productName;
        @AuraEnabled public String productCode;
        @AuraEnabled public String lineQuantity;
        @AuraEnabled public String status;
        @AuraEnabled public String interfaceStatus;

    }

    public class Tools {
        @AuraEnabled public Id lineId;
        @AuraEnabled public String lineNumber;
        @AuraEnabled public String toolName;
        @AuraEnabled public String toolType;
        @AuraEnabled public String serialNumber;
        @AuraEnabled public String outOfCalibration;
        @AuraEnabled public String interfaceStatus;
    }

    public class RemovedParts {
        @AuraEnabled public Id Id;
        @AuraEnabled public String lineItem;
        @AuraEnabled public String lineProdName;
        @AuraEnabled public String lineProdCode;
        @AuraEnabled public String lineQty;
        @AuraEnabled public String lineSER;
        @AuraEnabled public String lineSN;
        @AuraEnabled public String interfaceStatus;
    }

    public class Ecf {
        @AuraEnabled public String ecfNumber;
        @AuraEnabled public String ecfStatus;
        @AuraEnabled public String isSubmitted;
        @AuraEnabled public String nameOfContact;
        @AuraEnabled public Date dateOfEvent;
        @AuraEnabled public Date dateReported;
        @AuraEnabled public String injured;
    }

    private static List<RemainingParts> createPartsWapper(List<ProductRequestLineItem> prliItems) {
        List<RemainingParts> parts = new List<RemainingParts>();

        for(ProductRequestLineItem prli : prliItems) {
            RemainingParts wrap = new RemainingParts();
            wrap.partName = prli.Product2.Name;
            wrap.partNumber = prli.Part_Number__c;
            wrap.installedQty = String.valueOf(prli.Applied_Qty__c);
            wrap.remainingQty = String.valueOf(prli.Remaining_Qty__c);
            wrap.lineStatus = prli.Status;
            wrap.shipmentType = prli.ShipmentType;
            parts.add(wrap);
        }

        return parts;
    }

    private static List<PartsInstalled> createPartInstalledsWapper(List<WorkOrderLineItem> woliItems) {
        List<PartsInstalled> parts = new List<PartsInstalled>();

        for(WorkOrderLineItem woli : woliItems) {
            PartsInstalled wrap = new PartsInstalled();
            wrap.lineNumber = woli.LineItemNumber;
            wrap.lineType = woli.Type__c;
            wrap.installedPart = woli.Part__r.Name;
            wrap.asset = woli.Asset_Name__c;
            wrap.installedQty = String.valueOf(woli.Part_Qty__c);
            wrap.lineStatus = woli.Status;
            parts.add(wrap);
        }

        return parts;
    }

    private static List<RemainingPartsForRemoval> createPartsRemovalWrapper(List<WorkOrderLineItem> woliItems) {
        List<RemainingPartsForRemoval> parts = new List<RemainingPartsForRemoval>();

        for(WorkOrderLineItem woli : woliItems) {
            RemainingPartsForRemoval wrap = new RemainingPartsForRemoval();
            wrap.lineId = woli.Id;
            wrap.lineNumber = woli.LineItemNumber;
            wrap.productName = woli.Part__r.Name;
            wrap.productCode = woli.Part__r.ProductCode;
            wrap.lineQuantity = String.valueOf(woli.Part_Qty__c);
            wrap.status = woli.Status;
            wrap.interfaceStatus = woli.Interface_Status__c;
            parts.add(wrap);
        }

        return parts;
    }

     private static List<RemovedParts> createRemovedPartsWrapper(List<WorkOrderLineItem> woliItems) {
        List<RemovedParts> parts = new List<RemovedParts>();

        for(WorkOrderLineItem woli : woliItems) {
            RemovedParts wrap = new RemovedParts();
            wrap.Id = woli.Id;
            wrap.lineItem = woli.LineItemNumber;
            wrap.lineProdName = woli.Part__r.Name;
            wrap.lineProdCode = woli.Part__r.ProductCode;
            wrap.lineQty = String.valueOf(woli.Removed_Qty__c);
            wrap.lineSER = woli.Symptom_Error_Code__c;
            wrap.lineSN = woli.Serial_Number__c;
            wrap.interfaceStatus = woli.Interface_Status__c;
            parts.add(wrap);
        }

        return parts;
    }

    private static List<Tools> createToolsWrapper(List<WorkOrderLineItem> woliItems) {
        List<Tools> tools = new List<Tools>();

        for(WorkOrderLineItem woli : woliItems) {
            Tools wrap = new Tools();
            wrap.lineId = woli.Id;
            wrap.lineNumber = woli.LineItemNumber;
            wrap.toolName = woli.Service_Tool__r.Name;
            wrap.toolType = woli.Service_Tool__r.Tool_Type__c;
            wrap.serialNumber = woli.Service_Tool__r.Serial_Number__c;
            wrap.outOfCalibration = woli.Service_Tool__r.Calibration_Status__c;
            wrap.interfaceStatus = woli.Interface_Status__c;
            tools.add(wrap);
        }

        return tools;
    }

    private static List<Ecf> createEcfWrapper(List<L2848__c> ecfItems) {
        List<Ecf> ecf = new List<Ecf>();

        for(L2848__c ecfWrap : ecfItems) {
            Ecf wrap = new Ecf();
            wrap.ecfNumber = ecfWrap.Name;
            wrap.ecfStatus = ecfWrap.Is_Submitted__c;
            wrap.isSubmitted = String.valueOf(ecfWrap.Is_Submit__c);
            wrap.nameOfContact = ecfWrap.Name_of_Customer_Contact__c;
            wrap.dateOfEvent = ecfWrap.Date_of_Event__c;
            wrap.dateReported = ecfWrap.Date_only_Reported_to_Varian__c;
            wrap.injured = ecfWrap.Was_anyone_injured__c;
            ecf.add(wrap);
        }

        return ecf;
    }

    @AuraEnabled
    public static WorkOrderResult getWORecord(Id recordId) {
        WorkOrder wo = getWorkOrderById(recordId);

        WorkOrderResult woResult;

        List<Ecf> ecf = new List<Ecf>();
        Map<String, List<WorkOrderLineItem>> woliItems = getAllWorkOrderLineItem(recordId);
        List<PartsInstalled> partsInstalled = createPartInstalledsWapper(woliItems.get('Parts'));
        List<ProductRequestLineItem> prliItems = getAllProductRequestLineItem(recordId);
        List<RemainingParts> parts = createPartsWapper(prliItems);
        List<RemainingPartsForRemoval> partsForRemoval = createPartsRemovalWrapper(woliItems.get('RemovedPartsToBeReturn'));
        List<RemainingPartsForRemoval> partsForRemovalReq = createPartsRemovalWrapper(woliItems.get('RemovedPartsReq'));
        List<Tools> tools = createToolsWrapper(woliItems.get('Tools'));
        List<RemovedParts> removedParts = createRemovedPartsWrapper(woliItems.get('RemovedParts'));
        Datetime latestEndDate = getLatestEndDate(wo);

        if(wo != null && wo.ECF_Required__c != null) {
            if(wo.ECF_Required__c.equals(ECF_IS_REQUIRED)) 
                ecf = createEcfWrapper(getEcfByWorkOrderId(recordId));
        }

        if(recordId == null)
            woResult = new WorkOrderResult('Record not found', true, null, null, null, null, null, null, null, null, null, null);
        else if(woStatusList.contains(wo.Status))
            woResult = new WorkOrderResult('Work Order is already Approved, Closed, Submitted, or has an Error', true, wo,
                                            woliItems.get('Activity'), partsInstalled, parts, partsForRemoval, 
                                            tools, ecf, latestEndDate, removedParts, partsForRemovalReq);
        else
            woResult = new WorkOrderResult(null, false, wo, woliItems.get('Activity'), partsInstalled,
                                           parts, partsForRemoval, tools, ecf, latestEndDate, removedParts, partsForRemovalReq); // No Error

        return woResult;
    }

    private static WorkOrder getWorkOrderById(String recordId) {
        List<WorkOrder> wo = [Select Id, Status, WorkOrderNumber, Malfunction_Start__c, Line_Count__c, Machine_Release__c, Account.ERP_Timezone__c, 
                                Priority, Remaining_Parts_Count__c, Close__c, Closure_Summary__c, Service_Resource__c, Customer_Malfunction_End__c, 
                                Service_Resource__r.Work_Center__c, Interface_Status__c, Accepted_Time__c, ECF_Required__c,  
                                ECF_Form_Count__c, Count_of_FSR__c, Injured__c, Complaint__c, Test_Required__c, Closure_Resolution__c,Case_Type__c, //STSK0024819
                                Test_Specifications_Met__c, Total_Work_Hours__c, SAP_Priority__c, Open_Line_Count__c
                             From WorkOrder 
                             Where Id=: recordId];
        
        if(wo.size() > 0) {
            return wo.get(0);
        } else {
            return null;
        }
    }
    
    public static Map<String, List<WorkOrderLineItem>> getAllWorkOrderLineItem(Id recordId) {
        List<WorkOrderLineItem> woliList = [Select Id, LineItemNumber, StartDate, EndDate, Asset_Name__c, Activity_Type__c, Type__c, Status, On_Job_Training__c, 
                                            Part__c, Part__r.Name, Part_Qty__c, RecordTypeId, Product_Item__c, Removed_Qty__c, Installed_Removed_WOLI__c,
                                            Removed_Part_Returnable__c, Billing_Type__c, Return_All__c, RecordType.Name, Source_of_Parts__c, 
                                            Installed_Part_Traceable__c, Part__r.ProductCode, Service_Tool__r.Tool_Type__c, Service_Tool__r.Calibration_Status__c,
                                            Service_Tool__r.Name, Service_Tool__r.Serial_Number__c, Interface_Status__c, Serial_Number__c, Symptom_Error_Code__c,
                                            Activity_Group__c, Removed_WOLI_Returnable__c, ReturnOrderLineItemId, SAP_Billable__c 
                                            From WorkOrderLineItem Where WorkOrderId =: recordId And RecordType.Name != 'Header' 
                                            ORDER BY StartDate ASC];
        
        List<WorkOrderLineItem> activityWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> partsWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> remainingWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> toolsWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> removedPartsWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> serviceWorkOrderLineItems = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem> removedPartsReqWorkOrderLineItems = new List<WorkOrderLineItem>();

        for(WorkOrderLineItem woli: woliList) {
            if((woli.Type__c == 'Travel' || woli.Type__c == 'Labor') && (woli.Status == 'New' || woli.Status == 'Open')) {
                activityWorkOrderLineItems.add(woli);
                woliRecordsSet.add(woli);
            }
                

            if(woli.Type__c == 'Parts Install' && (woli.Status == 'New' || woli.Status == 'Open')) {
                partsWorkOrderLineItems.add(woli);
                woliRecordsSet.add(woli);
            }

            if(woli.Type__c == 'Parts Removal' && (woli.Status == 'New' || woli.Status == 'Open') && 
                woli.Removed_WOLI_Returnable__c == true && woli.ReturnOrderLineItemId == null)
                removedPartsReqWorkOrderLineItems.add(woli);                

            if(woli.Installed_Part_Traceable__c == true && woli.Installed_Removed_WOLI__c == null && (woli.Status == 'New' || woli.Status == 'Open') &&
                woli.Source_of_Parts__c != 'L - Locally Purchased' && woli.Type__c == 'Parts Install') 
                remainingWorkOrderLineItems.add(woli);

            if(woli.Type__c == 'Tools' && (woli.Status == 'New' || woli.Status == 'Open')) {
                toolsWorkOrderLineItems.add(woli);
                woliRecordsSet.add(woli);
            }
            
            if(woli.Type__c == 'Parts Removal' && (woli.Status == 'New' || woli.Status == 'Open')) {
                removedPartsWorkOrderLineItems.add(woli);
                woliRecordsSet.add(woli);
            }
                
        }   
        Map<String, List<WorkOrderLineItem>> workLineItemMap = new Map<String, List<WorkOrderLineItem>>{'Activity' => activityWorkOrderLineItems,
                                                                                                        'Parts' => partsWorkOrderLineItems,
                                                                                                        'RemovedPartsToBeReturn' => remainingWorkOrderLineItems,
                                                                                                        'Tools' => toolsWorkOrderLineItems,
                                                                                                        'RemovedParts' => removedPartsWorkOrderLineItems,
                                                                                                        'RemovedPartsReq' => removedPartsReqWorkOrderLineItems};
        return workLineItemMap;
    }
    
    public static List<ProductRequestLineItem> getAllProductRequestLineItem(Id recordId) {
        List<ProductRequestLineItem> prliList = [Select Id, WorkOrderLineItemId, Part_Number__c, WorkOrderLineItem.LineItemNumber, WorkOrderLineItem.Part_Qty__c, Product2.Name,
                                                 WorkOrderLineItem.Removed_Qty__c, WorkOrderLineItem.Product_Item__c, Remaining_Qty__c, Status, ShipmentType, Applied_Qty__c  
                                                 From ProductRequestLineItem Where WorkOrderId =: recordId];

        List<ProductRequestLineItem> remainingShipmentItmes = new List<ProductRequestLineItem>();
        for(ProductRequestLineItem prli: prliList) {
            if(prli.Remaining_Qty__c > 0) 
                remainingShipmentItmes.add(prli);
        }
        return remainingShipmentItmes;
    }

    private static List<L2848__c> getEcfByWorkOrderId(String recordId) {
        List<L2848__c> ecfList = new List<L2848__c>();

        ecfList = [SELECT Id, Name, Is_Submitted__c, Is_Submit__c, Name_of_Customer_Contact__c, Date_of_Event__c,
                          Date_only_Reported_to_Varian__c, Was_anyone_injured__c
                    FROM L2848__c 
                    WHERE WorkOrder__c = :recordId];
        return ecfList;
    }

    private static Datetime getLatestEndDate(WorkOrder wo) {
        Datetime dt = System.now();
        List<WorkOrderLineItem> woli = new List<WorkOrderLineItem>();

        if(wo == null) 
            return dt;
    
        woli = [SELECT EndDate, Activity_Type__c
                FROM WorkOrderLineItem
                WHERE WorkOrderId = :wo.Id
                  AND EndDate != null
                  AND (Activity_Group__c = 'Service' OR Activity_Group__c = 'Maintenance' 
                    OR Activity_Group__c = 'Modification')
                  AND (Status = 'New' OR Status = 'Open')
                ORDER BY EndDate DESC];

        if(woli.size() > 0)        
            dt = woli.get(0).EndDate;

        if(wo.SAP_Priority__c.equals(SP_EMERGENCY)) {
            for(WorkOrderLineItem woliRecord : woli) {
                if(woliRecord.Activity_Type__c.equals(AT_EMERGENCY)) {
                    dt = woliRecord.EndDate;
                     return dt;
                }
            }
        }

        return dt;
    }
    //STSK0024819 : Start
    @AuraEnabled
    public static List<picklistEntry> getPicklistOptionsClosureResol(Object caseType){
        List<picklistEntry> lstofOptions = new List<picklistEntry>();
        Map<String,string> picklistOptions = new Map<String,string>();
        Schema.DescribeFieldResult field = WorkOrder.Closure_Resolution__c.getDescribe();
        for (Schema.PicklistEntry f : field.getPicklistValues())
           picklistOptions.put(f.getValue(),f.getLabel());
        Map<Object,List<Schema.PicklistEntry>> mapPicklistOptions =  getDependentPicklistValues(WorkOrder.Closure_Resolution__c);
        for(Schema.PicklistEntry entry : mapPicklistOptions.get(caseType)){
            lstofOptions.add(new picklistEntry(entry.getValue(),picklistOptions.get(entry.getValue())));
        }
        
        return lstofOptions;
        
        
    }
    Private static Map<Object,List<Schema.PicklistEntry>> getDependentPicklistValues(Schema.sObjectField dependentField){
        Map<Object,List<Schema.PicklistEntry>> dependentPicklistValues = new Map<Object,List<Schema.PicklistEntry>>();
        Schema.DescribeFieldResult dependentFieldResult = dependentField.getDescribe();
        Schema.sObjectField controllerField = dependentFieldResult.getController();
        if(controllerField == null){
            return null;
        } 
        Schema.DescribeFieldResult controllerFieldResult = controllerField.getDescribe();
        List<Schema.PicklistEntry> controllerValues = (controllerFieldResult.getType() == Schema.DisplayType.Boolean ? null : controllerFieldResult.getPicklistValues());
        String base64map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        for (Schema.PicklistEntry entry : dependentFieldResult.getPicklistValues()){
            if (entry.isActive()){
                List<String> base64chars = String.valueOf(((Map<String,Object>)JSON.deserializeUntyped(JSON.serialize(entry))).get('validFor')).split('');
                for (Integer i = 0; i < controllerValues.size(); i++){
                    Object controllerValue = (controllerValues == null ? (Object) (i == 1) : (Object) (controllerValues[i].isActive() ? controllerValues[i].getLabel() : null));
                    Integer bitIndex = i / 6;
                    Integer bitShift = 5 - Math.mod(i, 6 );
                    if(controllerValue == null || (base64map.indexOf(base64chars[bitIndex]) & (1 << bitShift)) == 0){
                        continue;
                    } 
                    if (!dependentPicklistValues.containsKey(controllerValue)){
                        dependentPicklistValues.put(controllerValue, new List<Schema.PicklistEntry>());
                    }
                    dependentPicklistValues.get(controllerValue).add(entry);
                }
            }
        }
        return dependentPicklistValues;
    }
    //STSK0024819 : End
    @AuraEnabled
    public static String saveWorkOrder(String woObj, String woliList) {
        WorkOrder woRecord = (WorkOrder) JSON.deserialize(woObj, WorkOrder.Class);
        List<WorkOrderLineItem> woliRecordList = (List<WorkOrderLineItem>) JSON.deserialize(woliList, List<WorkOrderLineItem>.Class);
        List<WorkOrderLineItem> woliToUpdate = new List<WorkOrderLineITem>();
        Boolean allWOLIDoNotProcess = true;

        try 
        {
            if(woRecord.Machine_Release__c != null) {
                String customerTimeZone = 'America/New_York'; // Default
                for(ERP_Timezone__c varTZ : [select Id, Name, Salesforce_timezone__c from ERP_Timezone__c where Name =: woRecord.Account.ERP_Timezone__c]) {
                    customerTimeZone = varTZ.Salesforce_timezone__c.substring(varTZ.Salesforce_timezone__c.indexof('(') + 1, varTZ.Salesforce_timezone__c.indexof(')'));
                }
                Datetime machineDtTime = woRecord.Machine_Release__c;
                woRecord.Customer_Malfunction_End__c = machineDtTime.format('dd-MMM-yyyy h:mm a', customerTimeZone);
            }

            //woRecord.Accepted_Time__c = System.now();
            woRecord.Status = 'Submitted';
            woRecord.Interface_Status__c = 'Process';
            woRecord.Customer_Machine_Release__c =  woRecord.Machine_Release__c.format('dd-MM-yyyy h:mm a');
            if(woRecord.SAP_Malfunction_End__c == null)woRecord.SAP_Malfunction_End__c = VFSL_Utility.constructDateTimeGMT(woRecord.Machine_Release__c.format('dd-MM-yyyy h:mm a'));
            System.debug('woRecord.Machine_Release__c: ' + woRecord.Machine_Release__c);

            for(WorkOrderLineItem woli: woliRecordList) {
                if((woli.Type__c == 'Tools' || woli.Type__c == 'Parts Removal') && woli.Status == 'New') {
                    woli.Status = 'Submitted';
                }

                if(woli.Status != 'Closed' && woli.Status != 'Canceled') {
                    woli.Status = 'Submitted';
                    if(woli.Interface_Status__c != 'Do Not Process'){
                        woli.Interface_Status__c = 'Process';
                    	if(woli.Contract_Line_Item__c == null)woli.Billing_Type__c = null;
                    	if(woli.SAP_Billable__c)woli.SAP_Billable__c = false;
                    } 
                }

                if((woli.Type__c == 'Parts Install' && woli.Source_of_Parts__c == 'C - Customer Spares') || woli.Type__c == 'Parts Removal' || woli.Type__c == 'Tools' || 
                    ((woli.Type__c == 'Travel' || woli.Type__c == 'Labor') && woli.On_Job_Training__c == true)) {
                        woli.Status = 'Submitted';
                        woli.Interface_Status__c = 'Do Not Process';
                }

                woliToUpdate.add(woli);
            }
            //if(woliToUpdate.size() > 0)
                //update woliToUpdate;

            for(WorkOrderLineItem woli : woliRecordList) {
                if(!woli.Interface_Status__c.equals(STATUS_DNP))
                    allWOLIDoNotProcess = false;
            }

            if(allWOLIDoNotProcess) {
                woRecord.Interface_Status__c = STATUS_DNP;
                woRecord.SAP_Status__c = STATUS_NA;
            }

            update woRecord;
            if(woliToUpdate.size() > 0) update woliToUpdate;
            
        } catch (DMLException ex) {
            String errorMessage = ex.getMessage();
            Integer occurence;
            if (ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION')){
                occurence = errorMessage.indexOf('FIELD_CUSTOM_VALIDATION_EXCEPTION,') + 34;
                errorMessage = errorMessage.mid(occurence, errorMessage.length());
                occurence = errorMessage.lastIndexOf(':');
                errorMessage = errorMessage.mid(0, occurence);
            } else {
                errorMessage = ex.getMessage();
            }
            return errorMessage;
        }
        return null;
    }
     //STSK0024819
     public class picklistEntry implements Comparable{
        @AuraEnabled public string pvalue{get;set;}
        @AuraEnabled public string label{get;set;}
        
        public picklistEntry(string pvalue,string text) {
            this.pvalue = pvalue;
            this.label = text;            
        }
        
        public Integer compareTo(Object compareTo) {
            picklistEntry compareToOppy = (picklistEntry)compareTo;
            Integer returnValue = 0;
            if (pvalue > compareToOppy.pvalue)
                returnValue = 1;
            else if (pvalue < compareToOppy.pvalue)
                returnValue = -1;        
            return returnValue;       
        }
    }
}
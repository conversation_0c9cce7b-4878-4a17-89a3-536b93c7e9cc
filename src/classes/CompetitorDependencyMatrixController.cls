public class CompetitorDependencyMatrixController {
    static String csvString='Product Type,Vendor,Model\n';
    @AuraEnabled(cacheable=true)
    public static String getMatrixDependency() {
        //static String csvString='Product Type,Vendor,Model\n';
        String sessionId=UserInfo.getSessionID();
        String userId = UserInfo.getUserId();
        Set<Id> usersInGroup = new Set<Id>();
        
        //List<GroupMember> gm = [SELECT UserOrGroupId,Group.name FROM GroupMember WHERE Group.name='NVIB dependency report access']; 
        for (GroupMember gm:[SELECT UserOrGroupId,Group.name FROM GroupMember WHERE Group.name='NVIB dependency report access']){
            usersInGroup.add(gm.UserOrGroupId);
        }
        if(!usersInGroup.contains(userId)){
            return 'You do not have access to this feature';
        }
        else{
        Map<string,List<string>> prodVendorPicklistValuesMap = new Map<string,List<string>>();
        prodVendorPicklistValuesMap= DependentPicklistController.getDependentPicklistValues(Competitor__c.Vendor__c);

        Map<string,List<string>> vendProdnamePicklistValuesMap = new Map<string,List<string>>();
        vendProdnamePicklistValuesMap = DependentPicklistController.getDependentPicklistValues(Competitor__c.Model__c);

        for(String key:prodVendorPicklistValuesMap.keyset()){
            for(String st:prodVendorPicklistValuesMap.get(key)){
                if(vendProdnamePicklistValuesMap.containskey(st)){
                    for(String vp:vendProdnamePicklistValuesMap.get(st)){
                        if(!vendProdnamePicklistValuesMap.get(st).isEmpty()){
                            csvString+=key+','+st+','+vp+'\n';
                        }
                    }
                }
                else if(!vendProdnamePicklistValuesMap.containskey(st)){
                    csvString+=key+','+st+','+''+'\n';
                }
            }
        }
        getPicklistByUIApi('Vendor__c');
        getPicklistByUIApi('Model__c');
        return csvString;
        }
        //return csv2;
    }
	@future(callout=true)
    public static void getPicklistByUIApi(String fieldName){
        String objectType ='Competitor__c';
        Schema.SObjectType convertToObj = Schema.getGlobalDescribe().get(objectType);
        Schema.DescribeSObjectResult res = convertToObj.getDescribe();
        PicklistUtilUiApi.PicklistEntries entries = new PicklistUtilUiApi.PicklistEntries();
        List<Schema.RecordTypeInfo> RT = res.getRecordTypeInfos();
        Map<String,List<String>> picklistRecordTypeMap = new Map<String,List<String>>();

        for(Schema.RecordTypeInfo recInfo :RT){
            List<String> pickValues = new List<String>();
            if(recInfo.getName()!='Master'){
                entries = PicklistUtilUiApi.getPicklistValues(objectType,recInfo.getRecordTypeId(),fieldName);
                if(entries!=null){
                	for(PicklistUtilUiApi.PicklistEntry pe : entries.values){
                    	pickValues.add(pe.value);
                	}
                    if(!picklistRecordTypeMap.containsKey(recInfo.getName())){
                    	picklistRecordTypeMap.put(recInfo.getName(),pickValues);
                	}
                }
            }
            else break;
        }

        List<String> recordTypeNames = new List<String>(picklistRecordTypeMap.keyset());
        String csvInUi='Record Type,'+fieldName+'\n';
        for(String st:recordTypeNames){
            for(String pe:picklistRecordTypeMap.get(st)){
                csvInUi+=st+','+pe+'\n';
            }
        }
        ContentVersion cv = new ContentVersion();
        cv.Title = fieldName+'RT dependency '+date.today().format(); //title of the file
        cv.PathOnClient = '/dependencies.csv'; // full path within Salesforce this can just be the name of file to be in the library
        cv.VersionData = Blob.valueOf(csvInUi); //file data 
        insert cv;
    }
}
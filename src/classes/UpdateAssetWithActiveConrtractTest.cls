@isTest
public class UpdateAssetWithActiveConrtractTest {
    
    
    @testSetup static void setup(){
        ERP_Pcode__c pcode = VFSL_TestDataUtility.getERPPCode();
        insert pcode;
        Product2 prodVar = VFSL_TestDataUtility.getProduct(pcode.Id);
        prodVar.Product_Type__c = 'Model';
        insert prodVar;
        Pricebook2 pbVar = new Pricebook2(
            name = 'TestPB',
            isActive = true
        );
        insert pbVar;
        
        Id pricebookId = Test.getStandardPricebookId();
        Product2 prod = [SELECT Id FROM Product2 LIMIT 1];
        Pricebook2 pb = [SELECT Id FROM Pricebook2 LIMIT 1];
        PricebookEntry pbs = new PriceBookEntry();
        pbs.Product2Id = prod.Id;
        pbs.Pricebook2Id = pricebookId;
        pbs.UnitPrice = 2;
        insert pbs;
        
        PriceBookEntry pbi = new PriceBookEntry();
        pbi.Product2Id = prod.Id;
        pbi.Pricebook2Id = pb.Id;
        pbi.UseStandardPrice = false;
        pbi.UnitPrice = 1;
        pbi.IsActive = true;
        insert pbi;
        
        Asset as1 = VFSL_TestDataUtility.createAsset('TestAsset1');
        as1.Product2Id = prod.Id;
        as1.Billing_Type__c = 'P – Paid Service';
        insert as1;
        
        
        //ServiceContract sc1 = VFSL_TestDataUtility.createServiceContract('test suirg');
        ServiceContract sc1 = new ServiceContract(name = 'test service contract');
       	//sc.AccountId = account.Id;
        sc1.SAP_Order_Reason__c = 'HS1';
        sc1.Pricebook2Id = pb.Id;
        sc1.SAP_Contract_Type__c = 'ZH1';
        insert sc1;
        
        ContractLineItem cl1 = VFSL_TestDataUtility.createContractLineItem(as1, sc1);
        cl1.UnitPrice = 1;
        cl1.Quantity = 1;
        cl1.PricebookEntryId = pbi.Id;
        cl1.StartDate = System.today();
        cl1.EndDate = System.today().addDays(365);
        insert cl1;
        List<ContractLineItem> contractList = [
            SELECT id, ServiceContractId,StartDate, Line_Status__c, Billing_Type__c ,AssetId, Asset.Billing_Type__c from contractLineItem];
        system.debug('contractList ' + contractList);
    }
    static testMethod void testExecute1(){
        
        test.starttest();
        UpdateAssetWithActiveConrtract b = new  UpdateAssetWithActiveConrtract();
        Database.executeBatch(b,200);
        test.stoptest();
    }
}
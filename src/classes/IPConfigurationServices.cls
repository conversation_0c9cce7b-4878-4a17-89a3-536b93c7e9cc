/*
* Author: <PERSON><PERSON><PERSON>
* Created Date: 27-05-2024
* Project : FOCUS 25 (Unity 3.0) - STRY0224734
* Purpose - This class contains all the helper methods of IPConfigurationTriggerHandler
*/
public with sharing class IPConfigurationServices {
    
    public static void setFieldsBeforeInsert(List<IP_Configuration__c> newIPCList){
        for(IP_Configuration__c ipc : newIPCList){
            if(ipc.SaaSLicense__c){
                ipc.Qty__c = ipc.ActiveLicenseCount__c;
            }
            ipc.InterfaceStatus__c = 'Process';
        }
    }

    public static void setFieldsBeforeUpdate(List<IP_Configuration__c> newIPCList,Map<Id,IP_Configuration__c> oldIPCMap){
        List<IP_Configuration__c> cancelledIPConfigList = new List<IP_Configuration__c>();

        for(IP_Configuration__c ipc : newIPCList){
            if(ipc.ActiveLicenseCount__c != oldIPCMap.get(ipc.Id).ActiveLicenseCount__c){
                ipc.InterfaceStatus__c = 'Process';
                if(ipc.ActiveLicenseCount__c <= 0){
                    ipc.ExpirationDate__c = ipc.StartDate__c;
                    cancelledIPConfigList.add(ipc);
                }else{
                    if(ipc.Status__c != 'Inactive'){
                        ipc.EntitlementId__c = OILActions.generateUUID();
                    }
                    if(ipc.Status__c != 'Installed'){
                        ipc.Status__c = 'Installed';
                    }
                    if(ipc.ExpirationDate__c != null){
                        ipc.ExpirationDate__c = null;
                    }
                }

                if(ipc.SaaSLicense__c){
                    ipc.Qty__c = ipc.ActiveLicenseCount__c;
                }
            }

            if(
                ipc.ActiveLines__c > 0 &&
                ipc.ActiveLines__c != oldIPCMap.get(ipc.Id).ActiveLines__c
            ){
                ipc.InterfaceStatus__c = 'Process';
            }
        }

        if(cancelledIPConfigList.size() > 0){
            OILActions.cancelOrderItemLicense(cancelledIPConfigList);
        }
    }

    public static void afterUpdateOperations(List<IP_Configuration__c> newIPCList,Map<Id,IP_Configuration__c> oldIPCMap){
        Map<Id,Id> licenseContainerMap = new Map<Id,Id>();

        for(IP_Configuration__c ipc : newIPCList){
            if(
                ipc.ActiveLicenseCount__c != oldIPCMap.get(ipc.Id).ActiveLicenseCount__c ||
                (
                    ipc.ActiveLines__c > 0 &&
                    ipc.ActiveLines__c != oldIPCMap.get(ipc.Id).ActiveLines__c
                ) ||
                ipc.ActiveLicenseCount__c == 0
            ){
                licenseContainerMap.put(ipc.Id,ipc.LicenseContainer__c);
            }
        }

        Boolean isFlag = true;
        for(IP_Configuration__c ipc : [
            SELECT Id,EntitlementId__c,
            (SELECT Id,OrderItem__r.Order.Status FROM OrderItemLicenses__r 
            ORDER BY CreatedDate DESC LIMIT 1)
            FROM IP_Configuration__c WHERE 
            Id IN :licenseContainerMap.keySet()
        ]){
            if(
                ipc.OrderItemLicenses__r.size() > 0 &&
                ipc.OrderItemLicenses__r[0].OrderItem__r.Order.Status == 'Activated'
            ){
                isFlag = false;
                break;
            }
        }

        if(isFlag){
            Map<Id,IP_Configuration__c> updatedIPConfigMap = new Map<Id,IP_Configuration__c>();
            for(IP_Configuration__c ipc: [
                SELECT Id,EntitlementId__c FROM IP_Configuration__c WHERE 
                LicenseContainer__c IN :licenseContainerMap.values() AND 
                Id NOT IN :licenseContainerMap.keySet() AND 
                Status__c = 'Installed' AND 
                ActiveLicenseCount__c > 0 AND 
                StartDate__c <= TODAY AND 
                EndDate__c >= TODAY
            ]){
                ipc.EntitlementId__c = OILActions.generateUUID();
                updatedIPConfigMap.put(ipc.Id,ipc);
            }
            
            if(!updatedIPConfigMap.isEmpty()){
                update updatedIPConfigMap.values();
            }
        }
    }

    public static void updateIPConfigFields(List<IP_Configuration__c> newIPCList,Map<Id,IP_Configuration__c> oldIPCMap){
        Map<Id,IP_Configuration__c> mapEquipmentNbrChangeIPC = new Map<Id,IP_Configuration__c>();
        List<IP_Configuration__c> materialNumberChangeIPC = new List<IP_Configuration__c>();
        //STRY0183000 - Setting Asset if record's Equipment Number field is non-null every time the trigger runs - Rohan Shisode
        List<IP_Configuration__c> setAssetIPCList = new List<IP_Configuration__c>();

        Set<String> equipmentNumberSet = new Set<String>();
        Map<String,Asset> assetMap = new Map<String,Asset>();

        Set<String> materialNumberSet = new Set<String>();
        Map<String,Product2> productMap = new Map<String,Product2>();

        for(IP_Configuration__c ipc : newIPCList){
            if(ipc.Equipment_Nbr__c != null){
                //STRY0183000 - Rohan Shisode
                equipmentNumberSet.add(ipc.Equipment_Nbr__c);
                setAssetIPCList.add(ipc);
                if(ipc.Equipment_Nbr__c != oldIPCMap?.get(ipc.Id).Equipment_Nbr__c){
                    mapEquipmentNbrChangeIPC.put(ipc.Id,ipc);
                }
            }

            if(
                ipc.Material_Number__c != null &&
                ipc.Material_Number__c != oldIPCMap?.get(ipc.Id).Material_Number__c
            ){
                materialNumberChangeIPC.add(ipc);
                materialNumberSet.add(ipc.Material_Number__c);
            }
        }

        if(equipmentNumberSet.size() > 0){
            List<Asset> assetList = new List<Asset>([
                SELECT Id,Name,SAP_Reference__c,PCSN__c
                FROM Asset WHERE 
                SAP_Reference__c IN :equipmentNumberSet
            ]);
            
            for(Asset asset : assetList){
                assetMap.put(asset.SAP_Reference__c,asset);
            }

            for(IP_Configuration__c ipc : setAssetIPCList){
                if(assetMap.containsKey(ipc.Equipment_Nbr__c)){
                    ipc.Asset__c = assetMap.get(ipc.Equipment_Nbr__c).Id;
                    if(mapEquipmentNbrChangeIPC.containsKey(ipc.Id)){
                        mapEquipmentNbrChangeIPC.get(ipc.Id).PCSN__c = assetMap.get(ipc.Equipment_Nbr__c).PCSN__c;
                    }
                }
            }
        }

        if(materialNumberSet.size() > 0){
            List<Product2> productList = new List<Product2>([
                SELECT Id,Name,ProductCode 
                FROM Product2 WHERE 
                ProductCode IN :materialNumberSet
            ]);
            
            for(Product2 product: productList){
                productMap.put(product.ProductCode,product);
            }

            for(IP_Configuration__c ipc : materialNumberChangeIPC){
                if(productMap.containsKey(ipc.Material_Number__c)){
                    ipc.Product__c = productMap.get(ipc.Material_Number__c).Id;
                    ipc.Configuration_Item__c = productMap.get(ipc.Material_Number__c).Name;
                }
            }
        }
    }

    public static void addLicenseContainer(List<IP_Configuration__c> newIPCList){
        List<IP_Configuration__c> addLicenseContainerList = new List<IP_Configuration__c>();
        Set<Id> setAssetId = new Set<Id>();

        for(IP_Configuration__c ipc : newIPCList){
            if(
                ipc.LicenseContainer__c == null &&
                ipc.Asset__c != null &&
                ipc.SaaSLicense__c
            ){
                addLicenseContainerList.add(ipc);
                setAssetId.add(ipc.Asset__c);
            }
        }

        Map<String,LicenseContainer__c> mapAssetToLicenseContainer = new Map<String,LicenseContainer__c>();
        for(LicenseContainer__c licenseContainer : [
            SELECT Id,Asset__c FROM LicenseContainer__c WHERE
            Asset__c IN :setAssetId
        ]){
            mapAssetToLicenseContainer.put(licenseContainer.Asset__c,licenseContainer);
        }

        List<LicenseContainer__c> newLicenseContainerList = new List<LicenseContainer__c>();
        List<LicenseContainer__c> updateLicenseContainerList = new List<LicenseContainer__c>();
        List<IP_Configuration__c> noLicenseContainerIPCList = new List<IP_Configuration__c>();

        Set<Id> setNewLicenseContainerId = new Set<Id>();
        Set<Id> setAssetAlreadyLicenseContainerId = new Set<Id>();

        for(IP_Configuration__c ipc : addLicenseContainerList){
            if(mapAssetToLicenseContainer.containsKey(ipc.Asset__c)){
                ipc.LicenseContainer__c = mapAssetToLicenseContainer.get(ipc.Asset__c).Id;

                if(
                    ipc.StartDate__c != null &&
                    ipc.EndDate__c != null && 
                    ipc.StartDate__c <= System.today() && 
                    ipc.EndDate__c >= System.today()
                ){
                    updateLicenseContainerList.add(mapAssetToLicenseContainer.get(ipc.Asset__c));
                }
            }else if(!setAssetAlreadyLicenseContainerId.contains(ipc.Asset__c)){
                LicenseContainer__c newLicenseContainer = new LicenseContainer__c();
                newLicenseContainer.Asset__c = ipc.Asset__c;
                newLicenseContainer.IssueLicense__c = true;
                setAssetAlreadyLicenseContainerId.add(newLicenseContainer.Asset__c);
                newLicenseContainerList.add(newLicenseContainer);
                noLicenseContainerIPCList.add(ipc);
            }else{
                noLicenseContainerIPCList.add(ipc);
            }
        }

        Database.SaveResult[] saveResultList = Database.insert(newLicenseContainerList);
        for(Database.SaveResult saveResult: saveResultList){
            if(saveResult.isSuccess()){
                setNewLicenseContainerId.add(saveResult.getId());
            }
        }

        Map<String,LicenseContainer__c> newAssetToLCMap = new Map<String,LicenseContainer__c>();
        for(LicenseContainer__c licenseContainer : [
            SELECT Id,Asset__c FROM LicenseContainer__c WHERE 
            Id IN :setNewLicenseContainerId
        ]){
            newAssetToLCMap.put(licenseContainer.Asset__c,licenseContainer);
        }

        for(IP_Configuration__c ipc : noLicenseContainerIPCList){
            if(newAssetToLCMap.containsKey(ipc.Asset__c)){
                ipc.LicenseContainer__c = newAssetToLCMap.get(ipc.Asset__c).Id;
            }
        }

        if(updateLicenseContainerList.size() > 0){
            updateLicenseContainer(updateLicenseContainerList,null);
        }
    }

    public static void updateLicenseContainerBeforeUpdate(List<IP_Configuration__c> newIPCList,Map<Id,IP_Configuration__c> oldIPCMap){
        Set<Id> updateLicenseContainerId = new Set<Id>();
        Set<Id> setBaseOrderItemId = new Set<Id>();
        Set<Id> setIPConfigId = new Set<Id>();

        for(IP_Configuration__c ipc : newIPCList){
            if(ipc.BaseOrderItem__c != null){
                setBaseOrderItemId.add(ipc.BaseOrderItem__c);
                setIPConfigId.add(ipc.Id);
            }
        }

        Set<Id> setExpiredIPConfigId = new Set<Id>();
        if(!setBaseOrderItemId.isEmpty()){
            for(OrderItemLicense__c orderItemLicense : [
                SELECT Id, Entitlement__c FROM OrderItemLicense__c WHERE 
                Entitlement__c IN :setIPConfigId AND 
                OrderItem__c IN :setBaseOrderItemId AND 
                Status__c = 'Expired'
            ]){
                setExpiredIPConfigId.add(orderItemLicense.Entitlement__c);
            }
        }

        for(IP_Configuration__c ipc : newIPCList){
            if(
                !setExpiredIPConfigId.contains(ipc.Id) && 
                ipc.SaaSLicense__c && 
                ipc.LicenseContainer__c != null &&
                (
                    (ipc.StartDate__c != oldIPCMap.get(ipc.Id).StartDate__c) ||
                    (ipc.EndDate__c != oldIPCMap.get(ipc.Id).EndDate__c) ||
                    (ipc.Status__c != oldIPCMap.get(ipc.Id).Status__c) ||
                    (ipc.ActiveLicenseCount__c != oldIPCMap.get(ipc.Id).ActiveLicenseCount__c) 
                )
            ){
                updateLicenseContainerId.add(ipc.LicenseContainer__c);
            }
        }

        if(updateLicenseContainerId.size() > 0){
            updateLicenseContainer(null,updateLicenseContainerId);
        }
    }

    private static void updateLicenseContainer(List<LicenseContainer__c> updateLicenseContainerList,Set<Id> setLicenseContainerId){
        if(updateLicenseContainerList != null){
            for(LicenseContainer__c licenseContainer : updateLicenseContainerList){
                licenseContainer.IssueLicense__c = true;
            }
            update updateLicenseContainerList;
        }else if(setLicenseContainerId != null){
            List<LicenseContainer__c> changedLicenseContainerList = new List<LicenseContainer__c>();
            for(LicenseContainer__c licenseContainer: [
                SELECT Id,IssueLicense__c FROM LicenseContainer__c WHERE 
                Id IN :setLicenseContainerId
            ]){
                licenseContainer.IssueLicense__c = true;
                changedLicenseContainerList.add(licenseContainer);
            }
            update changedLicenseContainerList;
        }
    }
}
@isTest
public class APTS_NegotiationSalesSharClassTest {
    
    @testSetup
    static void createData(){
        
        //  Inserting User Record
        User userRecord = APTS_TestDataUtility.createUser('Test Admin');
        userRecord.email = '<EMAIL>';
        insert userRecord;
        
        User userRecord1 = APTS_TestDataUtility.createUser('Test Admin1');
        userRecord1.email = '<EMAIL>';
        insert userRecord1;

        
        // Getting recordtypeId for Account.
        String strRecordTypeId = [Select Id From RecordType Where SobjectType = 'Account' and Name = :VarianConstants.ACCOUNT_SOLD_TO].Id;
        
        // Inserting account record.
        Account accRecord = APTS_TestDataUtility.createAccount('Test Account');
        accRecord.CurrencyIsoCode = 'USD';
        accRecord.Country__c = 'USA';
        accRecord.Sub_Region__c = 'West';
        accRecord.RecordTypeId = strRecordTypeId;
        accRecord.BillingPostalCode = '67E EEy';
        accRecord.District_Sales_Manager__c = userRecord.Id;
        accRecord.Account_Type__c = 'Site Partner';
        insert accRecord;
        System.assert(accRecord.Id != null);
        
        
        // Inserting contact record.
        Contact contactRecord = APTS_TestDataUtility.createContact(accRecord.Id);
        contactRecord.MailingState = 'AZ';
        contactRecord.MailingCity = 'SUN CITY';
        contactRecord.MailingStreet = '13184 N 103RD AVE';
        contactRecord.MailingPostalCode = '85351';
        insert contactRecord;
        System.assert(contactRecord.Id != null);
        
        // Inserting opportunity record.
        Opportunity oppRecord = APTS_TestDataUtility.createOpportunity(accRecord.Id);
        oppRecord.Account_Sector__c = 'Government';
        oppRecord.Payer_Same_as_Customer__c = 'Yes'; 
        oppRecord.Expected_Close_Date__c = system.today(); 
        oppRecord.Existing_Equipment_Replacement__c = 'Yes'; 
        oppRecord.Deliver_to_Country__c ='India';
        oppRecord.Primary_Contact_Name__c = contactRecord.Id;
        oppRecord.F_Fiscal_Quarter__c = 'Q3';
        oppRecord.Account__c = accRecord.Id;
        oppRecord.Tender__c = 'No';
        insert oppRecord;
        System.assert(oppRecord.Id != null);
        
        // Inserting site record for the Oracle quote.
        BigMachines__Configuration_Record__c siteRecord = APTS_TestDataUtility.createSite();
        insert siteRecord;
        System.assert(siteRecord.Id != null);
        
        // Inserting oracle quote record.
        BigMachines__Quote__c quoteRecord = APTS_TestDataUtility.createQuote(oppRecord.Id, siteRecord.Id, accRecord.Id);
        quoteRecord.BMI_Region__c = 'NA';
        insert quoteRecord;
        System.assert(quoteRecord.Id != null);
        
        Test.startTest();
        Negotiation_Request__c negotiationRecord = APTS_TestDataUtility.createNegotiation(quoteRecord.Id, contactRecord.Id, userRecord.Id, accRecord.Id);
        negotiationRecord.Agreement_Types__c = 'ISA' ;
        negotiationRecord.Extra_Participants_Emails__c = userRecord.LastName;
        negotiationRecord.Additional_Participants_Record_ids__c = userRecord.Id;
        negotiationRecord.Site_Name__c = accRecord.Id;
        //insert negotiationRecord;
        
        Negotiation_Request__c negotiationRecord1 = APTS_TestDataUtility.createNegotiation(quoteRecord.Id, contactRecord.Id, userRecord.Id, accRecord.Id);
        negotiationRecord1.Agreement_Types__c = 'BAA' ;
        negotiationRecord1.Opportunity__c= null;
        negotiationRecord1.Extra_Participants_Emails__c = userRecord.LastName;
        negotiationRecord.Additional_Participants_Record_ids__c = userRecord.Id;
        negotiationRecord1.Legal_Purchasing_Entity_Name__c = accRecord.Id;
        //insert negotiationRecord1;
        
        insert new List<Negotiation_Request__c>{negotiationRecord,negotiationRecord1};
            Test.stopTest();
        Negotiation_Request__Share shareRecord = new Negotiation_Request__Share();
        shareRecord.AccessLevel = 'Edit';
        shareRecord.RowCause = 'Apex_Sharing__c';
        shareRecord.ParentId = negotiationRecord.Id;
        shareRecord.UserOrGroupId = userRecord.id;
        insert shareRecord;
        
        Apttus__APTS_Agreement__c agreementObj3 = new Apttus__APTS_Agreement__c();
        agreementObj3.Name = 'Test SGA';
        agreementObj3.Planned_Closure_Date__c = System.today();
        Id agrRecordTypeId1 = Schema.SObjectType.Apttus__APTS_Agreement__c.getRecordTypeInfosByName().get('SGA').getRecordTypeId();
        agreementObj3.RecordTypeId = agrRecordTypeId1;
        agreementObj3.Apttus__Account__c = accRecord.Id;
        agreementObj3.Agreement_Type__c = 'SGA';
        agreementObj3.Apttus__Related_Opportunity__c = null;
        insert agreementObj3;
        System.assert(agreementObj3.Id != NULL);
    }
    
    @isTest
    public static void testNRSHaringDeletion() {
        
        list < user > User = [SELECT Id, email, username FROM User where isactive = true limit 2];
        
        List<Negotiation_Request__c> nrRecList = [Select Id, Name, Sales_Admin__c , Sales_Person__c,Agreement_Types__c 
                                                From Negotiation_Request__c
                                               ];
        
        for(Negotiation_Request__c nrRec : nrRecList){
            nrRec.Sales_Admin__c = User[0].id;
            nrRec.Sales_Person__c= User[0].id;
            nrRec.Finance_Review__c = User[1].id;
        }
        
        System.debug('User-->' + JSON.serialize(User));
        System.debug('nrRecList-->' + JSON.serialize(nrRecList));
        Test.startTest();
        	update nrRecList;
            for(Negotiation_Request__c nrRec : nrRecList){
                nrRec.Sales_Admin__c = User[0].id;
                nrRec.Sales_Person__c= User[1].id;
                nrRec.Finance_Review__c = User[1].id;
            }
            update nrRecList;
        Test.stopTest();
        
        
    }
    
    
    @isTest
    public static void testcustomReviwerSharingRules() {
        
        list < user > User = [SELECT Id FROM User WHERE Name = 'James Bond'
                              and Department = ' US CORP Legal Operations'
                             ];
        
        Test.startTest();
        Negotiation_Request__c agreementObj1 = [Select Id, Name, Sales_Admin__c , Sales_Person__c ,Agreement_Types__c 
                                                From Negotiation_Request__c where Agreement_Types__c !=null limit 1
                                               ];
        
        Negotiation_Request__c agreementObj2 = [Select Id, Name, Sales_Admin__c , Sales_Person__c,Agreement_Types__c 
                                                From Negotiation_Request__c where Agreement_Types__c = 'VarianConstants.AGREEMENT_ISA'
                                               ];
        
        Negotiation_Request__c NRObj3 = [Select Id, Name, Sales_Admin__c , Sales_Person__c,Agreement_Types__c ,Legal_Purchasing_Entity_Name__c,Site_Name__c,Customer_Contact_Name__c,OwnerID,
                                         Service_Payment_Terms__c,Sales_Payment_Terms__c,Quote_Value__c,Opportunity__c, RecordTypeId,VIS_Regional_Manager__c 
                                         From Negotiation_Request__c where Agreement_Types__c = 'VarianConstants.AGREEMENT_BAA'
                                        ];
        
        Apttus__APTS_Agreement__c agreementObj3 = [Select Id, Name,Agreement_Type__c ,Apttus__Account__c
                                                   From Apttus__APTS_Agreement__c where Apttus__Account__c !=null LIMIT 1
                                                  ];
        
        
        Map<Id, Map<String,Apttus__APTS_Agreement__c>> accAgreementMap = new Map<Id, Map<String,Apttus__APTS_Agreement__c>>();
        accAgreementMap.put(agreementObj3.Apttus__Account__c, new Map<String,Apttus__APTS_Agreement__c> {agreementObj3.Agreement_Type__c=> agreementObj3});
        List<Apttus__APTS_Agreement__c> agreementList = NegotiationRequestTriggerHandler.getToBeUpsertedIndivualAgreementRecordsAccount(NRObj3, accAgreementMap);
        
        //Check the list has entires if so perform below logic
        if (User.size() > 0) {
            agreementObj2.Sales_Admin__c = User[0].id;
            agreementObj2.Sales_Person__c= User[1].id;
            update agreementObj2;
            
            system.assertEquals(agreementObj2.Sales_Admin__c , user[0].id);
            system.assertEquals(agreementObj2.Sales_Person__c, user[1].id);
            System.assert(agreementObj2.Id != NULL);
            System.assert(agreementObj1.Id != NULL);
        }
        
        
        if (agreementObj2.Sales_Admin__c != agreementObj1.Sales_Admin__c ||
            agreementObj2.Sales_Person__c!= agreementObj1.Sales_Person__c) {
                
                //Query the records with the RowCause = 'Apex Sharing' and IDs of updated record
                list < Negotiation_Request__Share > sharesToDelete = [select id, AccessLevel, RowCause, ParentId, UserOrGroupId
                                                                      from Negotiation_Request__Share 
                                                                      where ParentId =: agreementObj2.Id
                                                                      AND RowCause = 'Apex_Sharing__c'
                                                                      LIMIT 1
                                                                     ];
                
                // Test for only one manual share on job.
                //System.assertEquals(sharesToDelete[0].size(), 1, 'Set the object\'s sharing model to Private.');
                
                // Test attributes of manual share.
                System.assertEquals(sharesToDelete[0].AccessLevel, 'Edit');
                System.assertEquals(sharesToDelete[0].RowCause, 'Apex_Sharing__c');
                System.assertEquals(sharesToDelete[0].ParentId, agreementObj2.Id);
                System.assertNotEquals(sharesToDelete[0].UserOrGroupId, null);
                System.assert(sharesToDelete[0].Id != NULL);
                System.assert(sharesToDelete[0].AccessLevel != NULL);
                System.assert(sharesToDelete[0].RowCause != NULL);
                System.assert(sharesToDelete[0].ParentId != NULL);
                System.assert(sharesToDelete[0].UserOrGroupId != NULL);
                
                update sharesToDelete;
                
                // Test invalid agreement Id.
                delete agreementObj1;
                
                //Delete the all the query records                            
                if (!sharesToDelete.isEmpty()) {
                    
                    // Delete the records
                    Database.DeleteResult[] deleteResults1 = Database.delete(sharesToDelete, false);
                    deleteResults1 = Database.delete(sharesToDelete, false);
                }
                
                //Check the list has entires if so perform below logic
                if (User.size() > 0) {
                    
                    // Query agreement sharing records.
                    List < Negotiation_Request__Share > aShrs = [SELECT Id, UserOrGroupId, AccessLevel,
                                                                 RowCause FROM Negotiation_Request__Share 
                                                                 WHERE ParentId =: agreementObj1.Id AND UserOrGroupId =: user[0].id
                                                                ];
                    
                    // Test for only one manual share on job.
                    System.assertEquals(aShrs.size(), 1, 'Set the object\'s sharing model to Private.');
                    
                    // Test attributes of Apex share.
                    System.assertEquals(aShrs[0].AccessLevel, 'Read');
                    System.assertEquals(aShrs[0].RowCause, 'Apex_Sharing__c');
                    System.assertEquals(aShrs[0].UserOrGroupId, user[0].id);
                    System.assert(aShrs[0].Id != NULL);
                    System.assert(aShrs[0].UserOrGroupId != NULL);
                    System.assert(aShrs[0].AccessLevel != NULL);
                    System.assert(aShrs[0].RowCause != NULL);
                    
                    // Test invalid agreement Id.
                    delete agreementObj1;
                    
                    //Delete the records
                    Database.DeleteResult[] deleteResults = Database.delete(aShrs, false);
                    deleteResults = Database.delete(aShrs, false);
                }
                Test.stopTest();
                
            }
        
        
    }
}
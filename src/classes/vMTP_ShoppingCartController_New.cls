public class vMTP_ShoppingCartController_New {
    public static final String CLASSNAME = 'vMTP_ShoppingCartController_New'; 
    String termsandConds='';
    
    
    public class initWrapper {
        @AuraEnabled
        public boolean isInternalUser {get;set;}
        @AuraEnabled
        public Account getCartAccount {get;set;}
        @AuraEnabled
        public string getLoggedInUserAccountId {get;set;}
        @AuraEnabled
        public Contact getContact {get;set;}
        @AuraEnabled
        public List<vMIP_Sales_Org__c> getSalesOrg {get;set;}
        @AuraEnabled
        public List<vMIP_Catalog_Model__c> getCatalogModel {get;set;}
        @AuraEnabled
        public List<vMC_Order_Metadata__c> getShippingFieldsByCountry {get;set;}
        @AuraEnabled
        public List<wrapperClass_New> getCartItemLineList {get;set;}
        @AuraEnabled
        public string supportEmail {get;set;}
         @AuraEnabled
        public Map<string,string> getPOSubmittedNotification {get;set;}
        
        public initWrapper (boolean ifReviewScreen) {
            /*fetch Internal User */
            User usr =[SELECT Id,contact.accountId, contactId FROM User WHERE id =:userInfo.getUserId() LIMIT 1];
            string accId = usr.contact.accountId;
            this.getLoggedInUserAccountId = accId;
            switch on accId{
                when null{
                    this.isInternalUser = true;
                }
                when else{
                    this.isInternalUser = false;
                    /*fetch Contact Info */
                    this.getContact = vMTP_ShoppingCartController_New.getContactDetails(usr.ContactId);
                }
                
            }
            /*fetch Account Info*/
            Account acc = vMTP_ShoppingCartController_New.getAccountFromCart();
            this.getCartAccount = acc;
            /*fetch Sales Org */
            this.getSalesOrg = [SELECT Default_Plant__c, Product_Model__c, Sales_Org__c, Document_Type__c, Distribution_Channel__c, Division__c, 
                                Condition_Type__c, Sales_Office__c, Sales_Group__c, Shipping_Condition__c, PO_Submitted_Notification__c, Warehouse_Notification__c 
                                FROM vMIP_Sales_Org__c WHERE Country__c=:acc.ISO_Country__c];
            this.getCatalogModel = [select Inventory_Check_Required__c, Product_Model__c, Is_Price_Editable__c from vMIP_Catalog_Model__c];
            this.getCartItemLineList = getCartItemLineList(isInternalUser, getLoggedInUserAccountId);
            //Incoterms
            this.getShippingFieldsByCountry= [SELECT Country_Code__c, Incoterms__c, Is_Customer_Carrier_Required__c, Hide_Incoterms_for_Customer__c, vMC_Support_Email__c FROM vMC_Order_Metadata__c WHERE Country_Code__c=: acc.ISO_Country__c];
        	String supportEmail = '<EMAIL>';        	
        	System.debug('Account=='+acc);
            List<vMC_Order_Metadata__c> orderMetadataObj = [Select vMC_Support_Email__c, Country_Code__c From vMC_Order_Metadata__c Where Country_Code__c =: acc.ISO_Country__c];
            System.debug('orderMetadataObj=='+orderMetadataObj);
            if( orderMetadataObj.size() > 0 ){ supportEmail = orderMetadataObj[0].vMC_Support_Email__c; }
            this.supportEmail = supportEmail;
            list<vMIP_Sales_Org__c> lstSO = [select Product_Model__c,PO_Submitted_Notification__c from vMIP_Sales_Org__c];
            map<string,string> mapPO =  new map<string,string>();
            for(vMIP_Sales_Org__c so :lstSO){
                if(!mapPO.containsKey(so.Product_Model__c)){
                 mapPO.put(so.Product_Model__c, so.PO_Submitted_Notification__c);
                }
            }
            this.getPOSubmittedNotification = mapPO;
        }
    }
    
    @AuraEnabled
    public static initWrapper onInit(boolean ifReviewScreen){
        return new initWrapper(ifReviewScreen);
    }
    
    @AuraEnabled
    public static Account getAccountFromCart(){
        string accId = '';
        vMC_CartItem__c cart = new vMC_CartItem__c();
        cart = [select Id, Account__c from vMC_CartItem__c where OwnerId =: UserInfo.getUserId() limit 1];
        if(cart != null) {
            accId = cart.Account__c;
            return [select id, name,SFDC_Account_Ref_Number__c,Country__c, ISO_Country__c, Territories1__c from Account where id=:accId]; // vMarket Project, 159, added Territories1__c to the query - Tulasi
        }
        else
            return null;
    }
    
    @AuraEnabled
    public static Contact getContactDetails(string conId){
        Contact con = [Select Id, Name, Email, Account.Id, Account.Name, Account.SFDC_Account_Ref_Number__c, Account.Country__c, Phone, Contact_Email__c, MailingAddress From Contact Where Id=: conId Limit 1];
        if(con != null)
            return con;
        else
            return null;
    }
    
    @AuraEnabled 
    public static List<wrapperClass_New> getCartItemLineList(boolean ifInternalUser, string accId) { 
        Boolean ifAuthenticated = getAuthenticated();
        Boolean isPageLoad = ifAuthenticated ? true : false;
        List<wrapperClass_New> listingsDO = new List<wrapperClass_New>();
        try {
            // Get CartItemLines, if user logged in.
            if(ifAuthenticated) {
                listingsDO = getUserCartItemDetails(listingsDO, ifInternalUser, accId); // With Login
                system.debug('listingsDO****'+listingsDO);
            } else {
                if(!isPageLoad) return null; 
            }
            System.debug('8888888'+listingsDO);
            if (listingsDO!=null)
                return listingsDo;
        } catch (Exception e) {
            listingsDo=null;
            system.debug('------- getCartItemLineList  Exception --------'+String.valueOf(e) + string.valueof(e.getLineNumber()));
        }
        return listingsDo;
    }
    
    /* 
* When User is logged in and he add items into the cart.
* Those items will get add to vMarketCartItemLine object
*/
    public static List<wrapperClass_New> getUserCartItemDetails(List<wrapperClass_New> listingsDO, boolean ifInternal, string accId) {
        Decimal Total = 0; Decimal SubTotal = 0; String CurrencyIsoCode;
        
        /* Fetch Cart Item that belongs to current user */
        List<Schema.FieldSetMember> getCartItemFields = SObjectType.vMC_CartItem__c.FieldSets.Fetch_Cart_Details_From_ShoppingCart.getFields();
        string cartItemQuery = 'SELECT ';
        for(Schema.FieldSetMember f : getCartItemFields) {
            cartItemQuery += f.getFieldPath() + ', ';
        }
        cartItemQuery += 'Id, Bill_To_Site__r.ERP_Partner__r.Name, Ship_To_Site__r.ERP_Partner__r.Name,CreatedBy.Name FROM vMC_CartItem__c where OwnerId = \''+UserInfo.getUserId()+'\'' ;
        system.debug('cartItemQuery' + cartItemQuery);
        List<vMC_CartItem__c> Item = Database.query(cartItemQuery);
        
        /* Fetch all Cart Line Items belongs to current user */
        List<Schema.FieldSetMember> getCartItemLinesFields = SObjectType.vMC_CartItemLine__c.FieldSets.Logged_In_User_Cart_Lines.getFields();
        string cartItemLineQuery = 'SELECT ';
        for(Schema.FieldSetMember f : getCartItemLinesFields) {
            cartItemLineQuery += f.getFieldPath() + ', ';
        }
        cartItemLineQuery += 'Id,vMTP_Stripe_Subscription_Plan_Id__c,Product_Name__c FROM vMC_CartItemLine__c where vMC_CartItem__c = \''+Item[0].Id+'\'';
        
        List<vMC_CartItemLine__c> itemLine = Database.query(cartItemLineQuery);
        system.debug('itemLine---'+itemLine);
        
        Contact con = new Contact(); 
        if(Item[0].Order_Contact__c != null) {
            con = [SELECT Id, Name FROM Contact where Id=: Item[0].Order_Contact__c];
        }
        List<ERP_Partner_Association__c> erp = [SELECT id, Name,ERP_Partner_Number__c, Partner_Function__c, Sales_Org__c, ERP_Partner__r.Name, ERP_Partner__r.City__c, ERP_Partner__r.State_Province_Code__c, 
                                                ERP_Partner__r.Zipcode_Postal_Code__c,ERP_Partner__r.Country_Code__c,Customer_Account__r.Name, Customer_Account__r.Id, Partner_Street__c, 
                                                Partner_Street_line_2__c,Partner_City__c, Partner_State__c, Partner_Zipcode_postal_code__c, Partner_Country__c FROM ERP_Partner_Association__c
                                                WHERE Id=: Item[0].Bill_To_Site__c OR Id=: Item[0].Ship_To_Site__c];
        /* Check size of cartItem records */
        if (Item.size() > 0) {
            if(Item[0].account__c!=null){
                accId=Item[0].account__C;
            }
            /* Check size of cartItemLines inside cartItem records */
            if (itemLine.size() > 0) {
                set<Id> appIds = new set<Id>(); 
                set<Id> prodIds = new set<Id>();
                List<vMC_CartItemLine__c> thirdPartyCartLineItems = new List<vMC_CartItemLine__c>();
                List<vMC_CartItemLine__c> varianProductCartLineItems = new List<vMC_CartItemLine__c>();
                Map<Id, vMC_CartItemLine__c> mapProdIdWithItem = new Map<Id, vMC_CartItemLine__c>();
                Map<string, vMC_CartItemLine__c> mapProductTypeWithItem = new Map<string, vMC_CartItemLine__c>();
                set<Id>  appSubscritionPlanId = new set<Id>();
                Map<Id, vMTP_Stripe_Subscription_Plan__c> mapAppSubscritionPlan =new  Map<Id, vMTP_Stripe_Subscription_Plan__c>();
                for (vMC_CartItemLine__c line: itemLine) {
                    if(line.Product__c != null) {
                        prodIds.add(line.Product__c);
                        mapProdIdWithItem.put(line.Product__c, line);
                        mapProductTypeWithItem.put('Varian Products', line);
                        varianProductCartLineItems.add(line);
                    }
                    else if(line.vMTP_App__c !=null) {
                        mapProductTypeWithItem.put(line.vMTP_App__c, line);
                        thirdPartyCartLineItems.add(line);
                        appIds.add(line.vMTP_App__c);
                        system.debug('itemLine---'+itemLine);
                        if(line.vMTP_Stripe_Subscription_Plan_Id__c!=null){
                            appSubscritionPlanId.add(line.vMTP_Stripe_Subscription_Plan_Id__c);
                        }
                    }
                }
                system.debug('appSubscritionPlanId ->' +appSubscritionPlanId);
                if(appIds.size() > 0) {
                    List<vMTP_App__c> listings  = fetchThirdPartyApps(appIds);
                    if(appSubscritionPlanId.size() > 0)
                        mapAppSubscritionPlan = getSubscriptionPlans(appSubscritionPlanId);
                    
                    for(vMTP_App__c appDetail : listings) {
                        CurrencyIsoCode = appDetail.CurrencyIsoCode;
                        string appSubTerm;
                        decimal appSubPrice =0;
                        if(appDetail.subscription__c){
                            appSubTerm = mapAppSubscritionPlan.get(appDetail.id).vMTP_Duration_Type__c;
                            appSubPrice = mapAppSubscritionPlan.get(appDetail.id).vMTP_Price__c;
                        }
                        listingsDO.add(new wrapperClass_New(appDetail,  mapProductTypeWithItem.get(appDetail.Id)    , 1, ifInternal,accId, null, Item[0], erp, con,appSubPrice,appSubTerm, 'Third Party'));
                        SubTotal = (decimal) SubTotal+appDetail.Price__c;
                        Total = SubTotal.setScale(2);
                    }
                }
                if(prodIds.size() > 0) {
                    Map<string, vMIP_Catalog_Rule__c> brMap = fetchProductRules(prodIds);
                    system.debug('brMap' + brMap);
                    for(vMC_CartItemLine__c il : itemLine) {
                        if(il.Product__c != null) {
                            listingsDO.add(new wrapperClass_New(null, il, mapProdIdWithItem.get(il.Product__c).Quantity__c, ifInternal,accId, brMap.get(il.Product__r.ProductCode), Item[0], erp, con,null,null, mapProdIdWithItem.get(il.Product__c).Product_Model__c));
                        }
                    }
                }
                system.debug('---------- Size------ '+String.valueOf(Item)+listingsDO);
                if(listingsDO.size()==0) listingsDO=null;// Added for Globalisation
                
            }
            return listingsDO;
        } else {
            return null;
        }
    } 
    
  
    public static Boolean getAuthenticated() {
        try {
            List<User> UserProfileList = [Select Id, vMarket_User_Role__c From User where id = : UserInfo.getUserId() limit 1];
            if (UserProfileList.size() > 0) {
                String Profile = UserProfileList[0].vMarket_User_Role__c;
                return (!Profile.contains('Guest') && Profile!=null) ? true : false;
            }
        } catch(Exception e){}
        return false;
    }
    
    public static List<vMTP_App__c> fetchThirdPartyApps(set<Id> appIds) {
        string tpApprovalStatus = 'Published';
        boolean isActive = true;
        List<Schema.FieldSetMember> getThirdPartyAppFields = SObjectType.vMTP_App__c.FieldSets.Shopping_Cart_Third_party_Fields.getFields();
        string thirdPartyQuery = 'SELECT ';
        for(Schema.FieldSetMember f : getThirdPartyAppFields) { 
            thirdPartyQuery += f.getFieldPath() + ', ';
        }
        thirdPartyQuery += 'Id, subscription__c,(Select Rating__c From vMTP_Comments__r) FROM vMTP_App__c where ApprovalStatus__c =\''+tpApprovalStatus+'\'' +' AND isActive__c =' + isActive+ ' AND ID IN ';
        thirdPartyQuery += ':appIds';
        system.debug('thirdPartyQuery' + thirdPartyQuery);
        return Database.query(thirdPartyQuery);
    }
    
    public static List<Brachy_Catalog__c> fetchBrachyProducts(set<Id> prodIds) {
        List<Schema.FieldSetMember> getBrachyFields = SObjectType.Brachy_Catalog__c.FieldSets.Shopping_Cart_Brachy_Fields.getFields();
        string brachyQuery = 'SELECT ';
        for(Schema.FieldSetMember f : getBrachyFields) { 
            brachyQuery += f.getFieldPath() + ', ';
        }
        brachyQuery += 'Id FROM Brachy_Catalog__c where Product__c IN ';
        brachyQuery += ':prodIds';        
        return Database.query(brachyQuery);
    }
    
    public static Map<string, Brachy_Rule__c> fetchBrachyRules(set<Id> prodIds) {
        List<Brachy_Rule__c> brList = new List<Brachy_Rule__c>();
        Map<string, Brachy_Rule__c> mapProductCodeWithBrachyRule = new Map<string, Brachy_Rule__c>();
        brList = [Select Discount_Off__c, Discount_Type__c,Start_Date__c,Expiration_Date__c,Product_Number__c, Product__c,Max_Qty__c From Brachy_Rule__c WHERE Product__c  IN:prodIds];
        for (brachy_Rule__c br : brList) {
            if(!mapProductCodeWithBrachyRule.containsKey(br.Product_Number__c)) {
                mapProductCodeWithBrachyRule.put(br.Product_Number__c, br);
            }
        }
        return mapProductCodeWithBrachyRule;
    }
    
    public static Map<string, vMIP_Catalog_Rule__c> fetchProductRules(set<Id> prodIds) {
        List<vMIP_Catalog_Rule__c> brList = new List<vMIP_Catalog_Rule__c>();
        Map<string, vMIP_Catalog_Rule__c> mapProductCodeWithBrachyRule = new Map<string, vMIP_Catalog_Rule__c>();
        brList = [Select Discount_Off__c, Discount_Type__c,Start_Date__c,Expiration_Date__c,Product_Number__c, Product__c,Max_Qty__c From vMIP_Catalog_Rule__c WHERE Product__c  IN:prodIds];
        for (vMIP_Catalog_Rule__c br : brList) {
            if(!mapProductCodeWithBrachyRule.containsKey(br.Product_Number__c)) {
                mapProductCodeWithBrachyRule.put(br.Product_Number__c, br);
            }
        }
        return mapProductCodeWithBrachyRule;
    }
    
    @AuraEnabled
    public static boolean deleteFromDB (string itemId, string itemType) {
        return vMC_CartItemUtil_New.removeAppFromCart(itemId, itemType);
    }
    
    @AuraEnabled
    public static Map<string, List<vMIP_Inventory__c>> checkInventory (List<string> prodIds) {
        system.debug('prodIds-->>>>>>>'+ prodIds);
        Map<string, List<vMIP_Inventory__c>> mapInventoryMaterialQty = new Map<string,List<vMIP_Inventory__c>>();
        List<vMIP_Inventory__c> inventory = [SELECT Error_Message__c,Material_Description__c,Material__c,Name,Plant_Description__c,Plant__c,Unrestricted_Quantity__c FROM vMIP_Inventory__c WHERE Material__c IN: prodIds];
        for(vMIP_Inventory__c inv : inventory) {
            if(!mapInventoryMaterialQty.containsKey(inv.Material__c)) {
                mapInventoryMaterialQty.put(inv.Material__c, new List<vMIP_Inventory__c>());
            }
            mapInventoryMaterialQty.get(inv.Material__c).add(inv);
        }
        system.debug('mapInventoryMaterialQty-->>>>>>>'+ mapInventoryMaterialQty);
        return mapInventoryMaterialQty;
    }
    
    
    @AuraEnabled
    public static vMC_TaxShippingResponse getShippingDataFromSAP(string shipToNumber,string accountId,string baProdCodePriceMap,
                                                                 List<Decimal> tpAppPriceList, string country, string currencyCode) {
        
        system.debug('tpAppPriceList-----'+tpAppPriceList);
        system.debug('baProdCodePriceMap-----'+baProdCodePriceMap);
        // create the response object
        HTTPResponse resp = sendSAPShippingRequest(shipToNumber,accountId,baProdCodePriceMap,tpAppPriceList, country, currencyCode);
        system.debug('resp->>>'+resp.getBody());
        
        vMC_TaxShippingResponse shippingResp=vMC_TaxShippingResponse.parse(resp.getBody());
        system.debug('^^^^'+shippingResp);
        
        return shippingResp;
        
    }
    @TestVisible
    private static Map<string,string> fetchShippingMaterialCode(set<string> shippingMethods, string country, set<string> defaultPlant){
        Map<string,string> mapShippingMaterial = new Map<string,string>();
        for(vMIP_Inventory__c inv : [select id,Shipping_Material__c,Shipping_Material_Label__c from vMIP_Inventory__c where Shipping_Material_Label__c IN: shippingMethods AND Country__c=:country AND Plant__c IN:defaultPlant]) {
            mapShippingMaterial.put(inv.Shipping_Material_Label__c, inv.Shipping_Material__c);
        }
        return mapShippingMaterial;
    }
    
    private static HttpResponse sendSAPShippingRequest(string shipToNumber,string accountId,string baProdCodePriceMap,List<Decimal> tpAppPriceList,
                                                       string country, string currencyCode){
        HttpRequest req = new HttpRequest(); 
        req.setEndpoint('callout:vMarket_SAP_Credentials');//Tulasi
        //req.setEndpoint('callout:SAP_vMTP_Pricing');                                                   
        req.setMethod('POST');
        req.setHeader('X-SAPWebService-URL',Label.vMC_SAP_URL_Prod_Pricing); //Tulasi
        req.setHeader('Content-Type','application/json');
        req.setHeader('Accept','application/json');
        string body = getShippingRequestBody(shipToNumber,accountId,baProdCodePriceMap,tpAppPriceList,country, currencyCode);
        req.setbody(body);
        req.setTimeout(120000);
        system.debug('req-------' + req.getBody());
        Http http = new Http();
        HttpResponse response = http.send(req);
        System.debug('---response--'+response.getBody());
        
        string logResult = createVmcLogRecord(body,response.getBody() ,'');
        if(string.isNotEmpty(logResult))
            System.debug('---Log Created-- logResult--'+logResult);   
        else
            System.debug('---Log Creation Failed--');     
        
        return response;
    }
    
    public static string getShippingRequestBody(string shipToNumber,string accountId,string baProdCodePriceMap,List<Decimal> tpAppPriceList,
                                                string country, string currencyCode){
        
        List<vMC_TaxShippingRequest_New.IT_PRICE_DETAILS> lstItPriceDetails = new List<vMC_TaxShippingRequest_New.IT_PRICE_DETAILS>();
        List<PricingJSON> lstPricing = (List<PricingJSON>) JSON.deserialize(baProdCodePriceMap, List<PricingJSON>.class);
        system.debug('lstPricing---'+lstPricing);
        
        String sfdcBaseURL = URL.getSalesforceBaseUrl().toExternalForm();
        Map<String, String> erpPartners = new Map<String, String>();
        if(accountId <> null && !sfdcBaseURL.containsIgnoreCase('sfdev1')){
            for(ERP_Partner_Association__c erpPartner : [
                SELECT id, Name, ERP_Partner_Number__c, Sales_Org__c, Customer_Account__r.Name,
                Customer_Account__r.Id FROM ERP_Partner_Association__c
                WHERE Customer_Account__r.Id =: accountId
                AND Partner_Function__c = 'SP=Sold-to party' 
                ORDER BY CreatedDate
            ]){
                erpPartners.put(erpPartner.Sales_Org__c, erpPartner.ERP_Partner_Number__c);
            }
        }
        
        vMC_TaxShippingRequest_New taxShipReq= new vMC_TaxShippingRequest_New();
                                                    
                                                    /*imPartner Details */
            vMC_TaxShippingRequest_New.IM_PARTNER imPartner=new vMC_TaxShippingRequest_New.IM_PARTNER();
            if(sfdcBaseURL.containsIgnoreCase('sfdev1'))
                imPartner.SOLDTO='********';//shipToNumber;
            else
                imPartner.SOLDTO=erpPartners.get('0661');
            
            imPartner.SHIPTO=shipToNumber;//shipToNumber; imPartner.SHIPTO='********';//shipToNumber;
            imPartner.BILLTO='';
            
            /*For Third Party Apps */
            
            /*imHeader Details */
            vMC_TaxShippingRequest_New.IM_HEADER tp_imHeader=new vMC_TaxShippingRequest_New.IM_HEADER();
            tp_imHeader.DOCTYPE='ZHBD';
            tp_imHeader.SALESORG='0661';
            tp_imHeader.DISTRIBUTION_CH='10';
            tp_imHeader.DIVISION='10';
            tp_imHeader.CUSTREF='test po';
            tp_imHeader.CUSTREFDT=DateTime.now().format('yyyyMMdd');
            tp_imHeader.PMNTTRMS='';
            tp_imHeader.PRODUCT_MODEL = 'Third Party';
            
            system.debug('tpAppPriceList' + tpAppPriceList.size());
            List<vMC_TaxShippingRequest_New.IT_ITEMS_IN> tpItems=new List<vMC_TaxShippingRequest_New.IT_ITEMS_IN>();
            for(integer temp=0;temp<tpAppPriceList.size();temp++){
                tpItems.add(new vMC_TaxShippingRequest_New.IT_ITEMS_IN('TAX_MARKETPLACE_AP','1',string.valueof(tpAppPriceList[temp]),'',currencyCode, 'PR00'));
            }
            system.debug('tpItems' + tpItems);
            if(tpAppPriceList.size()>0)
                lstItPriceDetails.add(new vMC_TaxShippingRequest_New.IT_PRICE_DETAILS(tp_imHeader, imPartner, tpItems));
                                                    
        if(lstPricing.size() > 0) {
            Map<string, List<PricingJSON>> mapModelToProducts = new Map<string, List<PricingJSON>>();
            for(PricingJSON objPricing:lstPricing){
                if(!mapModelToProducts.containsKey(objPricing.productModel)) {
                    mapModelToProducts.put(objPricing.productModel, new List<PricingJSON>());
                }
                mapModelToProducts.get(objPricing.productModel).add(objPricing);
            }
            
            
            
            /*For Varian Products */
            for(string model : mapModelToProducts.keyset()) {
                vMC_TaxShippingRequest_New.IM_PARTNER erpPartner = new vMC_TaxShippingRequest_New.IM_PARTNER();
                if(sfdcBaseURL.containsIgnoreCase('sfdev1')){
                    erpPartner.SOLDTO = '********';//shipToNumber;
                }else{
                    erpPartner.SOLDTO = erpPartners.get(mapModelToProducts.get(model)[0].salesOrg);
                    //if(erpPartner.SOLDTO != null)
                    //system.debug('erpPartner123.SOLDTO----'+erpPartner.SOLDTO);
                }
                
                erpPartner.SHIPTO = shipToNumber;
                
                vMC_TaxShippingRequest_New.IM_HEADER vp_imHeader=new vMC_TaxShippingRequest_New.IM_HEADER();
                vp_imHeader.DOCTYPE= mapModelToProducts.get(model)[0].docType;
                vp_imHeader.SALESORG= mapModelToProducts.get(model)[0].salesOrg;
                vp_imHeader.DISTRIBUTION_CH= mapModelToProducts.get(model)[0].distributionChannel;
                vp_imHeader.SALESOFF= mapModelToProducts.get(model)[0].salesOffice;
                vp_imHeader.SALESGRP= mapModelToProducts.get(model)[0].salesGroup;
                vp_imHeader.SHIPCOND= mapModelToProducts.get(model)[0].shippingCondition;
                vp_imHeader.DIVISION = mapModelToProducts.get(model)[0].division;
                vp_imHeader.CUSTREF='test po';
                vp_imHeader.CUSTREFDT=DateTime.now().format('yyyyMMdd');
                vp_imHeader.PMNTTRMS='';
                vp_imHeader.PRODUCT_MODEL = model;
                
                List<vMC_TaxShippingRequest_New.IT_ITEMS_IN> varianItems=new List<vMC_TaxShippingRequest_New.IT_ITEMS_IN>();
                for(PricingJSON objPricing:mapModelToProducts.get(model)) {
                    varianItems.add(new vMC_TaxShippingRequest_New.IT_ITEMS_IN(objPricing.pCode,String.valueOf(objPricing.pQty),objPricing.price,'',currencyCode, objPricing.conditionType));
                }
                taxShipReq.X3RD_PROD_CODE='L';
                lstItPriceDetails.add(new vMC_TaxShippingRequest_New.IT_PRICE_DETAILS(vp_imHeader, erpPartner, varianItems));
                system.debug('lstItPriceDetails' + lstItPriceDetails);
                system.debug('lstItItemsIn' + varianItems);
            } 
        }
        taxShipReq.IT_PRICE_DETAILS = lstItPriceDetails;
        
        system.debug('lstItPriceDetails' + lstItPriceDetails);
       
        string params = JSON.serialize(taxShipReq);
        system.debug('params--->>>'+params);
        return params;
    }
    
    @AuraEnabled
    public static Id createNewOrder(string paymentMethod, boolean isLegal,string accountId, boolean isInternalUserFlag,string termsandCondtions, decimal totalPrice, string orderContactId) {
        Id orderId = null;
        vMC_CartItem__c cartItem= vMC_StripeCheckOut_New.getCartItems();
        Map<string,List<vMC_CartItemLine__c>> mapProductModelWithItems= vMC_StripeCheckOut_New.getCurrentUserCartLineItems(cartItem.Id);
        Id ownerId= null;
        Id UserId = fetchOrderContactUser(orderContactId);        
        if(isInternalUserFlag){
                ownerId=UserId;
        } else {
            ownerId=UserInfo.getUserId();
        }
        if(paymentMethod=='PO')
            orderId = vMC_CreateOrder_New.createOrderAndOLI(mapProductModelWithItems, cartItem, ownerId, termsandCondtions, '', vMC_Constants.orderPendingCustomerPO, vMC_Constants.orderPaymentMethod);
         else if(totalPrice==0)
            orderId = vMC_CreateOrder_New.createOrderAndOLI(mapProductModelWithItems, cartItem, ownerId, termsandCondtions, '', 'Order Received', 'Paid');
        return orderId;
    }
    
     //Tulasi STRY0209732 Start
     @AuraEnabled 
    public static boolean updateTaxDetails (string cartItemId,string poNumber ) {
        vMC_CartItem__c ci = new vMC_CartItem__c(Id =cartItemId,PO_Number__c =poNumber  );
            update ci;
        return true;
     }
	 //Tulasi STRY0209732 End
    
    @AuraEnabled 
    public static void updateCartLineItem (Map<string, decimal> mapBrachyIdWithQuantity, string selectedPaymentMethod) {
        system.debug('mapBrachyIdWithQuantity' + mapBrachyIdWithQuantity +' selectedPaymentMethod -> ' +selectedPaymentMethod);
        List<Id> cartLineId = new List<Id>();
        for(string str: mapBrachyIdWithQuantity.keyset()) {
            cartLineId.add(Id.valueOf(str));
        }
        
        
        /* Fetch all cartitemslines belongs to current user */
        vMC_CartItem__c Item = [select Id, (select Id, Product__c, Quantity__c from vMC_CartItemLines__r WHERE Product__c IN: cartLineId) from vMC_CartItem__c 
                                where OwnerId =: UserInfo.getUserId() LIMIT 1];
        /*Adding Payment Method to cart on click of CheckOut button*/
        if(selectedPaymentMethod!=null){    
            Item.Payment_Method__c = selectedPaymentMethod;
            update Item;
        }
        
        system.debug('Item.vMC_CartItemLines__r' + Item.vMC_CartItemLines__r);
        List<vMC_CartItemLine__c> items = new List<vMC_CartItemLine__c>();
        for (vMC_CartItemLine__c line: Item.vMC_CartItemLines__r) {
            for(string str: mapBrachyIdWithQuantity.keyset()) {
                if(str == line.Product__c) {
                    line.Quantity__c = mapBrachyIdWithQuantity.get(str);
                    items.add(line);
                }
            }
        }
        system.debug('items' + items);
        update items;
        
    }
    
    @AuraEnabled 
    /*Method to update Cart when User fills information and proceed*/
    public static void updateCart (string thirdPartyJSON, string brachyJSON, string accountId,string shipToId, string billToId,String shipToAddress, String billToAddress, 
                                   string userFieldValues,Boolean isInternalUserFlag, string selectedPaymentMethod) {
        system.debug('userFieldValues--->>'+userFieldValues);
        //system.debug('selectedPaymentMethod--->>'+selectedPaymentMethod);                               
        List<dataWrapper> tpWrapperList = new List<dataWrapper>(); 
        List<dataWrapper> brachyWrapperList= new List<dataWrapper>();
        List<destination> shippingInfo = new List<destination>();
        
        /*Deserialize Third Party JSON */
        try {
            tpWrapperList = (List<dataWrapper>)JSON.deserialize(thirdPartyJSON,List<dataWrapper>.class);
        }
        catch(Exception e) {
            system.debug(' Error: '+e.getMessage());
            throw new AuraHandledException(' Error: '+e.getMessage());
        }
        /*Deserialize Brachy JSON */
        try {
            brachyWrapperList = (List<dataWrapper>)JSON.deserialize(brachyJSON,List<dataWrapper>.class);
        }
        catch(Exception e) {
            system.debug(' Error: '+e.getMessage());
            throw new AuraHandledException(' Error: '+e.getMessage());
        }
        /*Deserialize Shopping Info Object where User enter all shipping information */
        system.debug('userFieldValues' + userFieldValues);
        system.debug('tpWrapperList' + tpWrapperList);
        try {
            shippingInfo =  (List<vMTP_ShoppingCartController_New.destination>)JSON.deserialize(userFieldValues,List<vMTP_ShoppingCartController_New.destination>.class);
            //system.debug(' shippingInfo: '+shippingInfo);
        }
        catch(Exception e) {
            system.debug(' Error: '+e.getMessage()+e.getLineNumber());
            throw new AuraHandledException(' Error: '+e.getMessage() +e.getLineNumber());
        }
        List<vMC_CartItemLine__c> cliToUpdate = new List<vMC_CartItemLine__c>();
        Map<Id, vMC_CartItemLine__c> cliMapToUpdate = new Map<Id, vMC_CartItemLine__c>();
        
        vMC_CartItem__c cart = [Select ID From vMC_CartItem__c where OwnerId=: UserInfo.getUserId() LIMIT 1];
        cart.Account__c = accountId;
        cart.Bill_To_Site__c = billToId;
        cart.Legal_Confirmation_By__c = UserInfo.getUserId();
        cart.Legal_Confirmation_Date__c = DateTime.now();
        cart.Shipping_Note__c = shippingInfo[0].shippingNote;
        cart.Shipping_Contact_Attn__c = shippingInfo[0].shipToName;
        cart.Shipping_Contact_Email__c = shippingInfo[0].shipToEmail;
        //cart.Shipping_Contact_Phone__c = shippingInfo[0].shipToPhone;
        
        //cart.vMC_FOB__c = 'FOB:Origin';
        cart.Payment_Term__c = shippingInfo[0].netTerm;
        cart.vMC_Billing_Address__c = billToAddress!=''? billToAddress: '';
        cart.Ship_To_Site__c= shipToId;
        cart.vMC_Shipping_Address__c = shipToAddress;
        cart.Order_Contact__c = shippingInfo[0].orderContactId;
        cart.Payment_Method__c = selectedPaymentMethod;
        if(shippingInfo[0].customerCarrierSeleted != null) {
            cart.Customer_Carrier__c = shippingInfo[0].customerCarrierSeleted;
            cart.Customer_Carrier_Number__c = shippingInfo[0].customerCarrierNo;  
            cart.Customer_Carrier_Type__c = shippingInfo[0].customerCarrierType; 
        } else {
            cart.Customer_Carrier__c = false;
            cart.Customer_Carrier_Number__c = '';  
            cart.Customer_Carrier_Type__c = '';
        }
        
        system.debug('cart***'+cart);
        try {
            update cart;
        } 
        catch(Exception e) {
            system.debug(' Error: '+e.getMessage());
            throw new AuraHandledException(' Error: '+e.getMessage());
        }                                  
        
        Map<string, List<vMC_CartItemLine__c>> mapCLi = vMC_StripeCheckOut_New.getCurrentUserCartLineItems(cart.Id);
        for(string model : mapCLi.keySet()){
            boolean bool = false;
            for(vMC_CartItemLine__c cli : mapCLi.get(model)) {
                if(model == 'Third Party' ) {
                    for(dataWrapper tp : tpWrapperList) {
                        if(cli.vMTP_App__c == tp.appId) {
                            cli.vMTP_App__c = tp.appId;
                            cli.Product_Model__c = model;
                            if(!cli.IsSubscribed__c)
                                cli.Unit_Price__c = tp.unitPrice;
                            cli.Quantity__c = tp.qty;
                            if(bool)
                                cli.Tax__c = 0;
                            else
                                cli.Tax__c = tp.tax;
                            cli.RecordTypeId = Schema.SObjectType.vMC_CartItemLine__c.getRecordTypeInfosByName().get('Third Party App').getRecordTypeId();
                            cli.vMC_CartItem__c = cart.Id;
                            cli.Shipping_Cost__c = 0;
                            cliToUpdate.add(cli);
                            bool = true;
                        }
                    }
                    
                } else {
                    for(dataWrapper vp : brachyWrapperList) {
                        if(cli.Product__c == vp.prodId) {
                            cli.Product__c = vp.prodId;
                            cli.Product_Model__c = model;
                            cli.Unit_Price__c = vp.unitPrice;
                            cli.Discounted_Price__c = vp.discountedPrice!=null?vp.discountedPrice:vp.unitPrice;
                            cli.Quantity__c = vp.qty;
                            cli.Sales_Org__c = vp.salesOrg;
                            cli.Document_Type__c = vp.docType;
                            cli.Default_Plant__c = vp.defaultPlant;
                            cli.Distribution_Channel__c = vp.distributionChannel;
                            cli.Sales_Office__c = vp.salesOffice;
                            cli.Sales_Group__c = vp.salesGroup;
                            cli.Shipping_Condition__c = vp.shippingCondition;
                            cli.Condition_Type__c = vp.conditionType;
                            cli.PO_Submitted_Notification__c = vp.PoSubmittedNotification;
                            cli.Warehouse_Notification__c = vp.warehouseNotification;
                            cli.Division__c = vp.division;
                            cli.vMC_FOB__c = vp.fob;
                            if(bool) {
                                cli.Shipping_Cost__c = 0;
                                cli.Tax__c = 0;
                            } 
                            else {
                                cli.Shipping_Cost__c = vp.shippingCost;
                                cli.Tax__c = vp.tax;
                            }
                            cli.RecordTypeId = Schema.SObjectType.vMC_CartItemLine__c.getRecordTypeInfosByName().get('Varian Product').getRecordTypeId();
                            cli.vMC_CartItem__c = cart.Id;
                            if(shippingInfo[0].customerCarrierSeleted) {
                                cli.Customer_Carrier_Shipping_Method__c = vp.cc_ShippingMethod;
                                cli.Customer_Carrier_Requested_Delivery_Date__c = vp.cc_reqDeliveryDate;
                                cli.Shipping_Method__c = '';
                                cli.Requested_Delivery_Date__c = NULL;
                            } else {
                                cli.Shipping_Method__c = vp.shippingMethod;
                                if(vp.reqDeliveryDate != NULL)
                                    cli.Requested_Delivery_Date__c = vp.reqDeliveryDate;
                                else
                                    cli.Requested_Delivery_Date__c = NULL;
                                cli.Customer_Carrier_Shipping_Method__c = '';
                                cli.Customer_Carrier_Requested_Delivery_Date__c = NULL;
                            }
                            system.debug('cli' + cli);
                            cliToUpdate.add(cli);
                            bool = true;
                        }
                    }
                }
            }            
        }
        system.debug('cliToUpdate--->>'+cliToUpdate);                              
        try {
            for(vMC_CartItemLine__c cli: cliToUpdate) {
                if(!cliMapToUpdate.containsKey(cli.Id)) {
                    cliMapToUpdate.put(cli.Id, cli);
                }
            }
            
            update cliMapToUpdate.values();
        }
        catch(Exception ex) {
            system.debug(' Error: '+ex.getMessage());
            String errMsg = ex.getMessage() + ' at line ' + ex.getLineNumber().format() + '' + ex.getStackTraceString() + ' in ' + CLASSNAME;
            
            // Handle Exception in API Logger Object
            throw new AuraHandledException(' Error: '+ex.getMessage());
        }
    }
    
    
    
    @AuraEnabled
    public static boolean deleteCart (string paymentType) {
        boolean isDeleted=false;
        List<vMC_CartItem__c> Item = new List<vMC_CartItem__c>();
        List<vMC_CartItemLine__c> itemLine = new List<vMC_CartItemLine__c>();
        try{
            if(paymentType == vMC_Constants.orderPaymentMethod) {
                string brachyCartLineRecordType = Schema.SObjectType.vMC_CartItemLine__c.getRecordTypeInfosByName().get(vMC_Constants.brachyCartLineRecordType).getRecordTypeId();
                itemLine = [select Id, vMTP_App__c, Brachy_Product__c, Quantity__c, vMC_CartItem__c from vMC_CartItemLine__c WHERE RecordTypeId =:brachyCartLineRecordType AND vMC_CartItem__r.createdby.id =: UserInfo.getUserId()];
                vMC_CartItem__c cart = new vMC_CartItem__c(Id = itemLine[0].vMC_CartItem__c);
                cart.Payment_Method__c = 'Credit Card';
                update cart;
                delete itemLine;
            } else {
                Item = [select Id, (select Id, vMTP_App__c, Brachy_Product__c, Quantity__c from vMC_CartItemLines__r) from vMC_CartItem__c 
                        where createdby.id =: UserInfo.getUserId()];
                delete Item;
            }
            isDeleted=true; 
        }
        catch(Exception ex) {
            system.debug(' Error: '+ex.getMessage());
            String errMsg = ex.getMessage() + ' at line ' + ex.getLineNumber().format() + '' + ex.getStackTraceString() + ' in ' + CLASSNAME;
            
            // Handle Exception in API Logger Object
            throw new AuraHandledException(' Error: '+ex.getMessage());
        }
        return isDeleted;
    }
    
    public class dataWrapper {
        @AuraEnabled
        public Id appId {get;set;}
        @AuraEnabled
        public Id prodId {get;set;}
        @AuraEnabled
        public String prodCode {get;set;}
        @AuraEnabled
        public decimal unitPrice {get;set;}
        @AuraEnabled
        public decimal qty {get;set;}
        @AuraEnabled
        public string rId {get;set;}
        @AuraEnabled
        public decimal discountedPrice {get;set;}
        @AuraEnabled
        public decimal appSubPrice {get;set;}
        @AuraEnabled
        public String appSubTerm {get;set;}
        @AuraEnabled
        public decimal tax {get;set;}
        @AuraEnabled
        public string model {get;set;}
        @AuraEnabled
        public string shippingMethod {get;set;}
        @AuraEnabled
        public Date reqDeliveryDate {get;set;}
        @AuraEnabled
        public string cc_ShippingMethod {get;set;}
        @AuraEnabled
        public Date cc_reqDeliveryDate {get;set;}
        @AuraEnabled
        public decimal shippingCost {get;set;}
        @AuraEnabled
        public string salesOrg {get;set;}
        @AuraEnabled
        public string docType {get;set;}
        @AuraEnabled
        public string defaultPlant {get;set;}
        @AuraEnabled
        public String distributionChannel {get;set;}
        @AuraEnabled
        public String salesOffice {get;set;}
        @AuraEnabled
        public String salesGroup {get;set;}
        @AuraEnabled
        public String shippingCondition {get;set;}
        @AuraEnabled
        public String division {get;set;}
        @AuraEnabled
        public String conditionType {get;set;}
        @AuraEnabled
        public String warehouseNotification {get;set;}
        @AuraEnabled
        public String PoSubmittedNotification {get;set;}
        @AuraEnabled
        public String fob {get;set;}
        
        public dataWrapper(Id appId, Id prodId,String prodCode, decimal unitPrice, decimal qty, string rId, decimal discountedPrice,decimal appSubPrice,String appSubTerm) {
            this.appId = appId;
            this.prodId = appId;
            this.prodCode = prodCode;
            system.debug('appSubPrice ' + appSubPrice);
            system.debug('appSubPrice thisis ' + this.appSubPrice);
            if(appSubPrice > 0 )
                this.unitPrice = appSubPrice;
            else
                this.unitPrice = unitPrice;
            this.qty = qty;
            this.discountedPrice = discountedPrice;
            this.appSubPrice = appSubPrice;
            this.appSubTerm = appSubTerm;
            this.tax = tax;
            this.rId = Schema.SObjectType.vMC_OrderItemLine__c.getRecordTypeInfosByName().get(rId).getRecordTypeId();
            this.model = model;
            this.shippingMethod = shippingMethod;
            this.reqDeliveryDate = reqDeliveryDate;
            this.cc_ShippingMethod = cc_ShippingMethod;
            this.cc_reqDeliveryDate = cc_reqDeliveryDate;
            this.shippingCost= shippingCost;      
        }
    }
    
    @AuraEnabled
    public static boolean getVerifiedACHCustomer()
    {
        List<vMC_ACH__c> achLst = new List<vMC_ACH__c>();
        achLst=[Select id,name,Account_Number__c,Customer__c,Bank__c,Status__c from vMC_ACH__c where user__C=:userinfo.getuserid() and Status__c='Verified'];
        
        if(achLst!=null && achLst.size()>0)
            return true;
        else
            return false;        
        
    }
    
    @AuraEnabled
    public static Id fetchBrachyManger (string shiptoId) {
        string shippingtoAddress='';
        Id salesMgr=null;
        try{
            ERP_Partner_Association__c objERP=[select id,Partner_Zipcode_postal_code__c from ERP_Partner_Association__c where id=:shiptoId];
            if(objERP!=null){
                Territory_Team__c objTerr=[SELECT Brachy_Sales_Manager__r.Name FROM Territory_Team__c WHERE Zip__c=:objERP.Partner_Zipcode_postal_code__c LIMIT 1];
                if(objTerr!=null){
                    salesMgr=objTerr.Brachy_Sales_Manager__c;
                }
            }
        }
        catch(Exception e){}
        return salesMgr;
    }
    
    public static Id fetchOrderContactUser (string contactId) {
        system.debug('contactId--'+contactId);
        Id orderUserId = UserInfo.getUserId();
        try{
            if(string.isNotBlank(contactId)) {
                List<User> usr = [select id from User where contactId=:contactId and isActive=true LIMIT 1];
                if(usr.size() >0){
                    orderUserId = usr[0].Id;
                }   
            }   
        }
        catch(Exception ex){
            system.debug('exception ' + ex.getMessage() + ex.getLineNumber());
        }
        return orderUserId;
    }  
    
    
    /** Fethching terms and Conditions from Custom Metadata type **/
    @AuraEnabled(cacheable=true)
    public static vMC_Terms_and_Conditions__mdt fetchTermsConds (String accountId) {        
        Account accountObj = [select Id, Name, Super_Territory1__c, Territories1__c From Account Where Id =:accountId];            
        string region = '';
        if( accountObj.Super_Territory1__c == 'Americas' ){
            region = 'NA';    
        }else if(accountObj.Territories1__c == 'EMEA'){
            region = 'EMEA';
        }else if(accountObj.Territories1__c == 'APAC'){
            region = 'APAC';
        }        
        
        string termsandConds='';
        vMC_Terms_and_Conditions__mdt vMCTermsConds;
        
        try{
            vMCTermsConds=[Select Id,MasterLabel,TC__c,Region__c from vMC_Terms_and_Conditions__mdt where Region__c =: region];
            termsandConds = vMCTermsConds.TC__c;
            
            if( region == 'APAC' ){
            	System.debug('APAC TnC==' + termsandConds);
                //Get ANZ_Terms_Conditions Static Resource URL
                StaticResource static_resource = [SELECT Id, Name, SystemModStamp
                                  FROM StaticResource 
                                  WHERE Name = 'ANZ_Terms_Conditions'
                                  LIMIT 1];
				String url_file_ref = '/resource/'
                    + String.valueOf(((DateTime)static_resource.get('SystemModStamp')).getTime())
                    + '/' 
                    + static_resource.get('Name');

				system.debug('url_file_ref==' + url_file_ref);
                
                vMCTermsConds.TC__c = termsandConds.replace('<a>ANZ TnC StaticResorce</a>','<a href="'+ url_file_ref +'" target="_blank">FORM RAD 10473A ANZ</a>');
            }
        }
        catch(Exception e){
            system.debug('Exception ' +e.getMessage());
        }
        return vMCTermsConds;
    }
    public static string createVmcLogRecord(string request, string response , string errorMessage ) {
        string logRecoedId = null;
        DateTime dt = DateTime.now();
        String currentDateTime = dt.format('MM-dd-yyyy hh:mm:ss');
        system.debug('dt----'+dt);
        system.debug('currentDateTime----'+currentDateTime);
        try{
            vMC_Logs__c obj = new vMC_Logs__c();
            obj.name = 'Vmc Tax -'+' '+currentDateTime;         
            obj.Request__c = request;
            obj.Response__c = response;
            obj.User_Id__c = userInfo.getUserId();
            obj.Error_Message__c = errorMessage;            
            insert  obj;
            logRecoedId =  obj.id;
            system.debug('logRecoedId----'+logRecoedId);
            return logRecoedId;
            
        }
        catch(Exception e){
            system.debug('Exception----'+e.getMessage());
            return logRecoedId;
        }
        
    }
    
    public static Map<Id, vMTP_Stripe_Subscription_Plan__c> getSubscriptionPlans(set<id> ids) {
        List<vMTP_Stripe_Subscription_Plan__c> subscriptionPlanList = [SELECT Id, Name, Is_Active__c, vMTP_App__c, vMTP_Duration_Type__c, 
                                                                       vMTP_Price__c, vMTP_Stripe_Subscription_Plan_Id__c 
                                                                       FROM vMTP_Stripe_Subscription_Plan__c WHERE id in :ids ];
        Map<Id, vMTP_Stripe_Subscription_Plan__c> mapAppSubscritionPlan = new Map<Id, vMTP_Stripe_Subscription_Plan__c>();
        if(subscriptionPlanList!=null && subscriptionPlanList.size() > 0) {
            for(vMTP_Stripe_Subscription_Plan__c subscriptionPlan :subscriptionPlanList ){
                mapAppSubscritionPlan.put(subscriptionPlan.vMTP_App__c,subscriptionPlan );
            }
            
        }
        
        return mapAppSubscritionPlan;
    }
    /*
    @AuraEnabled
    public static Map<String,String> getCustomLabelMap(String language){        
        return vMC_Utility.getCustomLabelMap(language);
    }  
    */
    public class PricingJSON {
        public String pCode;
        public Integer pQty;
        public String price;
        public string shippingMethod;
        public string defaultPlant;
        public string productModel;
        public string salesOrg;
        public string docType;
        public string distributionChannel;
        public string salesOffice;
        public string salesGroup;
        public string shippingCondition;
        public String division;
        public String conditionType;
    }
    
    public class destination {
        public string fob;
        public string customerCarrierNo;
        public string customerCarrierType;
        public string customerShippingMethod;
        public String shippingMethod;
        public string shippingNote;
        public string shipToName;
        public string shipToPhone;
        public string shipToEmail;
        public string netTerm;
        public boolean preDefinedNetTerm;
        public string customerCarrierDeliveryDate;
        public string customerCarrierPreference;
        public string orderContactId;
        public boolean customerCarrierSeleted;
    }
}
/*Test Class : MvHomeBodyHeaderControllerTest*/
public with sharing class MvSAMLLoginHelper {
    public static String getSAMLURL(String samlssoconfigid) {
        String pathSuffix = 's/login';
        string communityUrl = !Test.isRunningTest() ? Network.getLoginUrl(Network.getNetworkId()).replace(pathSuffix,'') : 'dummy URL';
        System.debug('commURL: '+communityUrl);
        SamlSsoConfig ssoConfig = [SELECT Id FROM SamlSsoConfig WHERE Developername = :samlssoconfigid];
        string startUrl1 = communityUrl+'s/';
        String requestSAMLURL = Auth.AuthConfiguration.getSamlSsoUrl(communityUrl, startUrl1, ssoConfig.Id);
        System.debug('final SAML: '+requestSAMLURL);
        return requestSAMLURL;
    }
}
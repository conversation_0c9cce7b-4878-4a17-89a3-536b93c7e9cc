/**
 *  Class Name: VFSL_CreateInstallationPlanController  
 *  Description:  
 *  Company: Varian FSL
 *  CreatedDate: 6/10/2019 
 *  Test Class: 
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  
 *            
 */
public without sharing class VFSL_CreateInstallationPlanController {
      
    public ERP_WBS__c aliasforsearchwbsproject {get;set;}
    public static Map<String,ERP_WBS__c> selectedWBSWrapperMap1 = new Map<String,ERP_WBS__c>(); 
    public static boolean allowAcceptanceDate {get;set;}
    public static boolean allowAcceptanceDate1 = true;
    public Map<String,Asset> equipmentIsntProdMap {get;set;}
    public static Map<String,Asset> equipmentIsntProdMap1 = new Map<String,Asset>();
    public static List<Sales_Order_Item__c> selectedSOIList{get;set;}
    public List<WorkOrderLineItem> woLILines = new list<WorkOrderLineItem>();
    public static Map<Id,ERP_WBS__c> id2wbsmap = new Map<Id,ERP_WBS__c>();
    public Case caseRecord {get;set;}
    public static WorkOrder workOrderRecord; 
    Map<Id,ERP_WBS__c> mapOFErpWBS = new Map<Id,ERP_WBS__c> ();
    public static boolean isUpdated = false;
    //Pagination variables and methods
    @TestVisible private static  integer totalRecs = 0;
    @TestVisible private static integer OffsetSize = 0;
    @TestVisible private static integer LimitSize= 80;
    private static FeatureFlags__mdt installSACheck = FeatureFlags__mdt.getInstance('Install_SA');
    
    public static final String LINE_STATUS_AVAILABLE = 'Available for planning';
    
    public VFSL_CreateInstallationPlanController() {}
    
    @AuraEnabled(cacheable=true) 
    public static List<Product2> getSearchProductByName (String searchTerm){
        String key = '%' + searchTerm + '%';
        if(key !=''){
          return [SELECT Id,Name FROM Product2 
                  WHERE Name LIKE :key AND Product_Type__c = 'Model' ORDER BY Name ASC LIMIT 100];
        }else{
            return null;
        }
    }
    
    @AuraEnabled(cacheable=true) 
    public static List<Sales_Order__c> getSalesOrderDetails (List<Id> salesOrderList){  
        if(!salesOrderList.isEmpty()){
           return [SELECT Id,Name,SimpleSO__c 
                   FROM Sales_Order__c WHERE Id IN :salesOrderList]; 
        }else{
            return null;
        }
    }
    
    @AuraEnabled(cacheable=true)
    public static String getCurrentTimezoneKey(){
        return [SELECT Id, TimeZoneSidKey 
                FROM User WHERE Id = :userInfo.getUserId()].TimeZoneSidKey;
    }

    //Shubham - Added this method as part of PWO redesign - STRY0172857
    @AuraEnabled(cacheable=true)
    public static String getPlanningVISRecordTypeId() 
    {
        String rectypeId = VFSL_Utility.getObjectRecordTypeMap('WorkOrder').get('Planning_VIS');
        return rectypeId;
    }
    
    @AuraEnabled(cacheable=true)
    public static WBSWrapperResponse getWorkOrderDetailsUpdateInstallation(String workOrderId){
        WBSWrapperResponse resp = new WBSWrapperResponse();
        WorkOrder wo = [SELECT Id,WorkOrderNumber,Owner.Name,AccountId,Account.Name,LocationId,
                               Location.Name,StartDate,EndDate,Top_Level_Asset__c,
                               Top_Level_Asset__r.Name, Installation_Manager__c,Internal_Comments__c,
                               Installation_Manager__r.Name,Subject,Installation_Type__c 
                        FROM WorkOrder 
                        WHERE Id = :workOrderId LIMIT 1];
        resp.Id = wo.Id;
        resp.workOrderName = wo.WorkOrderNumber;
        resp.locationName = wo.Location.Name;
        resp.locationId = wo.LocationId;
        resp.accountName = wo.Account.Name;
        resp.accountId = wo.AccountId;
        resp.ownerName = wo.Owner.Name;
        resp.ownerId = wo.OwnerId;
        resp.InstallationManagerName = wo.Installation_Manager__r.Name;
        resp.InstallationManagerId = wo.Installation_Manager__c;
        resp.TopLevelAssetName = wo.Top_Level_Asset__r.Name;
        resp.TopLevelAssetId = wo.Top_Level_Asset__c;
        resp.subject = wo.Subject;
        resp.installationType = wo.Installation_Type__c;
        resp.internalComments = wo.Internal_Comments__c;
        if(wo.StartDate <> null){
            Date myDate = date.newinstance(wo.StartDate.year(),wo.StartDate.month(),wo.StartDate.day());
            resp.startDateUpdate = wo.StartDate;
            resp.startDate = myDate;
        }
        if(wo.EndDate <> null){
            Date myDate = date.newinstance(wo.EndDate.year(),wo.EndDate.month(),wo.EndDate.day());
            resp.endDateUpdate = wo.EndDate;
            resp.endDate = myDate;
        }
        return resp;
    }

    @AuraEnabled(cacheable=false)
    public static WBSWrapperResponse getWorkOrderDetailsUpdateInstallationRefresh(String workOrderId){
        WBSWrapperResponse resp = new WBSWrapperResponse();
        // STRY0126923- Added Installation_Manager_Email__c in SOQL query
        //STRY0172857 - Shubham - Added newly added fields on WO to SOQL query
        WorkOrder wo = [SELECT Id,Owner.Name, AccountId, Account.Name, LocationId,Location.Name,
                               StartDate,EndDate,Top_Level_Asset__c, Top_Level_Asset__r.Name, 
                               Installation_Manager__c, Installation_Manager_Email__c, Internal_Comments__c,
                               Description, Installation_Manager__r.Name,Subject,Installation_Type__c,
                               Customer_Profile__c,Project_Type__c, Upgrade_Type__c, 
                               Fullscale__c, Fullscale_Project_Type__c, Citrix_Environment__c, 
                               Additional_Project_Scope__c, Date_Ship_Request_Submitted__c, 
                               Project_SharePoint__c, Kickoff_Date__c, Tbox_Upgrade_Date__c, 
                               Prod_Go_Live_Date__c, Current_Version__c,To_Version__c,
                               Alternatives_to_Access_Servers__c, Customer_Contact__c, Project_SharePoint2__c  
                        FROM WorkOrder 
                        WHERE Id =:workOrderId LIMIT 1];
        
        resp.Id = wo.Id;
        resp.locationName = wo.Location.Name;
        resp.locationId = wo.LocationId;
        resp.accountName = wo.Account.Name;
        resp.accountId = wo.AccountId;
        resp.ownerName = wo.Owner.Name;
        resp.ownerId = wo.OwnerId;
        resp.InstallationManagerEmail = wo.Installation_Manager_Email__c; // STRY0126923 - Assign Installation_Manager_Email__c
        resp.InstallationManagerName = wo.Installation_Manager__r.Name;
        resp.InstallationManagerId = wo.Installation_Manager__c;
        resp.internalComments = wo.Description;
        resp.TopLevelAssetName = wo.Top_Level_Asset__r.Name;
        resp.TopLevelAssetId = wo.Top_Level_Asset__c;
        resp.subject = wo.Subject;
        resp.installationType = wo.Installation_Type__c;
        if(wo.StartDate <> null){
            Date myDate = date.newinstance(wo.StartDate.year(),wo.StartDate.month(),wo.StartDate.day());
            resp.startDateUpdate = wo.StartDate;
            resp.startDate = myDate;
        }
        if(wo.EndDate <> null){
            Date myDate = date.newinstance(wo.EndDate.year(),wo.EndDate.month(),wo.EndDate.day());
            resp.endDateUpdate = wo.EndDate;
            resp.endDate = myDate;
        }
        //STRY0172857 - Shubham CHanges for PWO redesign - changes start
        resp.projectType=wo.Project_Type__c;
        resp.upgradeType=wo.Upgrade_Type__c;
        resp.fullscale=wo.Fullscale__c;
        resp.fullscaleProjectType=wo.Fullscale_Project_Type__c;
        resp.citrixEnvironment=wo.Citrix_Environment__c;
        resp.additionalProjectScope=wo.Additional_Project_Scope__c;
        resp.dateShipRequestSubmitted=wo.Date_Ship_Request_Submitted__c;
        resp.projectSharePoint=wo.Project_SharePoint2__c;
        resp.kickoffDate=wo.Kickoff_Date__c;
        resp.tboxUpgradeDate=wo.Tbox_Upgrade_Date__c;
        resp.prodGoLiveDate=wo.Prod_Go_Live_Date__c;
        resp.customerProfile=wo.Customer_Profile__c;
        resp.currentVersion=wo.Current_Version__c;
        resp.toVersion=wo.To_Version__c;
        resp.alternativesToSmartConnect=wo.Alternatives_to_Access_Servers__c;
        resp.customerContact=wo.Customer_Contact__c;
    //STRY0172857 - Shubham CHanges for PWO redesign - changes End
        return resp;
    }

    @AuraEnabled(cacheable=true) 
    public static List<Account> getSearchAccountByName (String searchTerm){
        String key = '%' + searchTerm + '%';
        if(key !=''){
          return [SELECT Id,Name,BillingCity,BillingState,BillingCountry,Account_Type__c,Ext_Cust_Id__c 
                  FROM Account 
                  WHERE Name LIKE :key ORDER BY Name ASC LIMIT 10000];
        }else{
            return null;
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<Sales_Order__c> getSearchProjectByName (String searchTerm){
        String key = '%' + searchTerm + '%';
        if(key !=''){
          return [SELECT Id,Name FROM Sales_Order__c WHERE Name Like :key ORDER BY Name ASC limit 200];
        }else{
            return null;
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<User> getSearchInstallationManagerByName (String searchTerm){
        String key = '%' + searchTerm + '%';
        if(key !=''){
            return [
                SELECT Id,Name, Email 
                FROM User 
                WHERE (Profile.Name = 'VMS FSL - Service Admin' OR Profile.Name = 'VMS FSL - Service User')
                    AND IsActive = true AND Name Like :key ORDER BY Name ASC limit 200
            ];
        }else{
            return null;
        }
    }

    @AuraEnabled(cacheable=true) 
    public static List<Asset> getSearchAssetByName (String searchTerm, String loc){
        String key = '%' + searchTerm + '%';
        if(key !=''){
            return [SELECT Id,Name,Status,Location.Name,Product2.Name,Parent_Asset_Text__c, 
                           LocationId,SAP_Equipment_Description__c,InstallDate 
                    FROM Asset 
                    WHERE (Name LIKE :key OR Product2.Name LIKE :key) 
                        AND Location.Name = :loc ORDER BY Name ASC LIMIT 1000];
        }else{
            return null;
        }
    }

    @AuraEnabled(cacheable=true) 
    public static List<Asset> getSearchAssetByNamePopup (String searchTerm){
        String key = '%' + searchTerm + '%';
        if(key !=''){
            return [SELECT Id,Name FROM Asset WHERE Name LIKE :key ORDER BY Name ASC LIMIT 1000];
        }else{
            return null;
        }
    }

    @AuraEnabled(cacheable=true) 
    public static List<Schema.Location> getSearchLocationByName (String searchTerm) {
        String key = '%' + searchTerm + '%';
        if(key !='') {
             //Added Account as part of STRY0157845
            return [SELECT Id, Name, SAP_Functional_Location__c, Account__c, LocationType, City__c, State__c, Country__c 
                    FROM Location 
                    WHERE (Name Like :key OR SAP_Functional_Location__c Like :key OR City__c Like :key)  
                      AND LocationType = 'Customer' AND Active__c = true 
                    ORDER BY Name ASC limit 100
            ];   
        } else {
            return null; 
        }
    }

    @AuraEnabled(cacheable=false)
    public static List<Schema.Location> getSearchLocationByAccount(String accountId){
        if(accountId <> '' && accountId <> null){
          return [SELECT Id,Name,SAP_Functional_Location__c,City__c,State__c,Country__c 
                  FROM Location WHERE Account__c =:accountId]; 
        }else{
            return null;
        }
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Schema.Location> getSearchLocationName(String searchTerm){
         String key = '%' + searchTerm + '%';
        if(key !='') {
             //Added account as part of STRY0157845
            return [SELECT Id, Name, Account__c, Account__r.Name, SAP_Functional_Location__c, 
                           LocationType, City__c, State__c, Country__c 
                    FROM Location 
                    WHERE (Name LIKE :key OR SAP_Functional_Location__c Like :key OR City__c Like :key)  
                      AND LocationType = 'Customer' AND Active__c = true 
                    ORDER BY Name ASC limit 100
            ];    
         
        } else {
            return null; 
        }
    }
    
    @AuraEnabled(cacheable=false)
    public static void remove(String woliId){
        String msg = 'Success';
        string trimmedWoliId =  String.valueOf(woliId).substring(0, 16);
        Id woRecordTypeId = Schema.SObjectType.WorkOrder.getRecordTypeInfosByDeveloperName().get('Planning_VIS').getRecordTypeId();
        WorkOrderLineItem woli = [SELECT Id,AssetId,Sales_Order_Item__c,WorkOrderId 
                                  FROM WorkOrderLineItem 
                                  WHERE Id = :woliId OR Id =: trimmedWoliId LIMIT 1];
        WorkOrder wo = [SELECT Id,AssetId,RecordTypeId 
                        FROM WorkOrder 
                        WHERE Id= :woli.WorkOrderId];
        Sales_Order_Item__c soi = [SELECT Id,Asset__c,NEW__c 
                                   FROM Sales_Order_Item__c 
                                   WHERE Id= :woli.Sales_Order_Item__c LIMIT 1];
        Id ipId;
        if(soi != null){
            try{
                ipId = soi.Asset__c;
                soi.Case_Line_Status__c = 'Available for planning';
                update soi;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
            if(wo.AssetId!=null && woli.Id != null && wo.AssetId == woli.AssetId && soi.NEW__c == 'New'){
                wo.AssetId = null;
                try{
                    update wo;
                }
                catch(Exception ex){
                    if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                        throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                    }else{
                        throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                    }
                }
            }
            if(woli.Id != null && wo.Id != null && wo.recordtypeid == woRecordTypeId){
                checkCaseLineWDStatus(woli.Id);
                if(hasCLAnyOpenUCWDL != null && hasCLAnyOpenUCWDL) {
                    woli.Status = 'Canceled';
                    woli.AssetId = null;
                    try{
                        delete woli;// should be Update woli;
                    }
                    catch(Exception ex){
                        if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                            throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                        }else{
                            throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                        }
                    }
                }else{
                    try{
                        delete woli;
                    }
                    catch(Exception ex){
                        if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                            throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                        }else{
                            throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                        }
                    }
                }   
            }else if (woli.Id != null && wo.Id != null && wo.recordtypeid != woRecordTypeId) {
                try{
                    delete woli;
                }
                catch(Exception ex){
                    if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                        throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                    }else{
                        throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                    }
                }
            }
            if(soi != null && soi.Asset__c!=null){
                List<Asset> ipListRemove = [SELECT Id,SAP_Reference__c,PCSN__c FROM Asset WHERE Id  = :ipId];
                if(ipListRemove.isEmpty()){
                    system.debug('No IP FOUND');
                }else{
                    Asset ip = ipListRemove[0];
                    if(ip.SAP_Reference__c == null){
                        try{
                            /* STRY0124133 - Start - Remove placeholder reference from all the cases */
                            List<Case> caseList = [Select Id, AssetId, Asset_Top_Level__c From Case Where AssetId =: ip.Id OR Asset_Top_Level__c =: ip.Id];
                            if(caseList.size() > 0) {
                                for(Case cs:caseList) {
                                    if(cs.AssetId == ip.Id){
                                        cs.AssetId = null;
                                    } 
                                    if(cs.Asset_Top_Level__c == ip.Id){
                                        cs.Asset_Top_Level__c = null;
                                    } 
                                }
                                update caseList;
                            }
                            /* STRY0124133 - End - Remove placeholder reference from all the cases */
                            delete ip;
                        }
                        catch(Exception ex){
                            if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                                throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                            }else{
                                throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                            }
                        }
                    }
                    soi.Asset__c = null;
                    try{
                        update soi;
                    }
                    catch(Exception ex){
                        if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                            throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                        }else{
                            throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                        }
                    }
                }
            }
        }
    } 
     
    /* This is no longer relevant as not called from anywhere.
    public static void setPSLineToClosed(String woliId){
        WorkOrderLineItem woli = [SELECT Id,AssetId,Sales_Order_Item__c,WorkOrderId 
                                  FROM WorkOrderLineItem 
                                  WHERE Id= :woliId LIMIT 1];
        WorkOrder wo = [SELECT Id,AssetId FROM WorkOrder WHERE Id=:woli.WorkOrderId];
        Sales_Order_Item__c soi = [SELECT Id,Asset__c,NEW__c FROM Sales_Order_Item__c WHERE Id=:woli.Sales_Order_Item__c LIMIT 1];

        if(woli.Id != null) {
            List<WorkOrderLineItem> psWDLs = new List<WorkOrderLineItem>();

            Id PSRecTypeID = Schema.SObjectType.SVMXC__Service_Order_Line__c.getRecordTypeInfosByName().get('Products Serviced').getRecordTypeId();
            Map<Id,WorkOrderLineItem> wdlMap = new Map<Id,WorkOrderLineItem>([
                SELECT Id,LineItemNumber,RecordTypeId,Status 
                FROM WorkOrderLineItem WHERE Id=: woliId AND WorkOrder.Status != 'Closed'
            ]);
            Map<Id,WorkOrderLineItem> wdlMap1 = new Map<Id,WorkOrderLineItem>([
                SELECT Id,LineItemNumber,RecordTypeId,Status 
                FROM WorkOrderLineItem 
                WHERE ParentWorkOrderLineItemId IN: wdlMap.KeySet() AND WorkOrder.Status != 'Closed' 
            ]);
            wdlMap.putAll(wdlMap1);
            for(WorkOrderLineItem wdl:wdlMap.values()) {
                if(wdl.RecordTypeId==PSRecTypeID) {
                    wdl.Status = 'Closed';
                    psWDLs.add(wdl);
                }
            }
            if(!psWDLs.isEmpty()) {
                update psWDLs;
            }
        }
    }*/

    public static boolean hasCLAnyOpenUCWDL;

    public static void checkCaseLineWDStatus(String woliId){
        Id woLineRecordTypeId = Schema.SObjectType.WorkOrderLineItem.getRecordTypeInfosByDeveloperName().get('VIS_Usage').getRecordTypeId();
        hasCLAnyOpenUCWDL = false;
        WorkOrderLineItem woli = [SELECT Id,AssetId,Sales_Order_Item__c,WorkOrderId,RecordTypeId 
                                  FROM WorkOrderLineItem 
                                  WHERE Id=:woliId LIMIT 1];
        WorkOrder wo = [SELECT Id,AssetId FROM WorkOrder WHERE Id=:woli.WorkOrderId];
        Sales_Order_Item__c soi = [SELECT Id,Asset__c,NEW__c 
                                   FROM Sales_Order_Item__c 
                                   WHERE Id=:woli.Sales_Order_Item__c LIMIT 1];

        if(woli.Id!=null) {
            Map<Id,WorkOrderLineItem> wdlMap = new Map<Id,WorkOrderLineItem>([
                SELECT Id,LineItemNumber,RecordTypeId,Status 
                FROM WorkOrderLineItem Where Id=: woliId AND WorkOrder.Status != 'Closed'
            ]);
            for(WorkOrderLineItem wdl:wdlMap.values()) { 
                if(wdl.RecordTypeId==woLineRecordTypeId && wdl.Status!='Closed') {
                    hasCLAnyOpenUCWDL = true;
                    break;
                }
            }
        }
    }

    @AuraEnabled(cacheable=true) 
    public static List<Asset> getSearchTopAssetByName (String locationId){
        String key = '%' + locationId+ '%';
        if(key !=''){
          return [SELECT Id,Name,Status,SAP_Equipment_Description__c,InstallDate 
                  FROM Asset 
                  WHERE LocationId =: locationId AND ParentId = Null];
        }else{
            return null;
        }
    }

     // STRY0130065 - Commented cacheble true
    @AuraEnabled//(cacheable=true)
    public static List<WOLIWrapperResponseChangeType> getLinetems(String woId){
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        List<WorkOrderLineItem> getLineItems = [
                SELECT Id,StartDate,Change_Type__c, LineItemNumber, WorkOrder.WorkOrderNumber,WorkOrder.Status, 
                    Sales_Order_Item__c,Sales_Order_Item__r.NEW__c,Sales_Order_Item__r.Name,Sales_Order_Item__r.Product__c,
                    Sales_Order_Item__r.Product__r.Name,Sales_Order_Item__r.Sales_Order__c,Sales_Order_Item__r.Product_Model__r.Name,
                    Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Requested_delivery_date__c,Part__c,Part__r.Name,
                    AssetId,Asset.Name,LocationId,Location.Name,Product2Id,Asset.RootAssetId,Asset.RootAsset.Name,
                    Product2.Name,Asset_Name__c,SAP_WBS_Number__c,SAP_WBS__c,Asset.Product2.Name,  
                    Asset.SAP_Reference__c, Asset.SAP_Functional_Location__c, Sales_Order_Item__r.Status__c, Sales_Order_Item__r.Reject_Reason__c
                FROM WorkOrderLineItem
                WHERE WorkOrderId=:woId AND RecordTypeId=:woLineRecordTypeId AND Sales_Order_Item__c <> null
            ];
        List<WOLIWrapperResponseChangeType> woliChangeTypeList = new List<WOLIWrapperResponseChangeType>();
        for(WorkOrderLineItem woli:getLineItems){
            WOLIWrapperResponseChangeType obj = new WOLIWrapperResponseChangeType();
            obj.assetTooltip = woli.AssetId <> null ? 'SAP Reference - '+woli.Asset.SAP_Reference__c+'\nSAP Functional Location - '+ woli.Asset.SAP_Functional_Location__c: '';
            obj.Id = '/'+woli.Id;
            obj.lineItemNumber = woli.LineItemNumber;
            obj.soi_Change_Type = woli.Sales_Order_Item__r.NEW__c;
            obj.line_Change_Type = woli.Change_Type__c;
            obj.top_LevelId = woli.Asset.RootAssetId <> null ? '/'+woli.Asset.RootAssetId :'';
            obj.top_Level = woli.Asset.RootAsset.Name <> null ? woli.Asset.RootAsset.Name : '';
            obj.installed_ProductId = woli.AssetId <> null ? '/'+woli.AssetId : '';
            obj.installed_Product = woli.Asset.Name <> null ? woli.Asset.Name : '';
            obj.product_Model = woli.Part__r.Name;
            obj.workOrderStatus = woli.WorkOrder.Status;
            obj.location = woli.Location.Name <> null ? woli.Location.Name:'';
            obj.locationId = woli.LocationId <> null ? '/'+woli.LocationId:'';
            obj.erp_WBS_Element = woli.SAP_WBS_Number__c <> null ? woli.SAP_WBS_Number__c:'';
            obj.erp_WBS_ElementId = woli.SAP_WBS__c <> null ?'/'+woli.SAP_WBS__c:'';
            obj.soi_Product = woli.Sales_Order_Item__r.Product__r.Name;
            obj.so_Item_Number = woli.Sales_Order_Item__r.Name;
            obj.so_Item_NumberId = '/'+woli.Sales_Order_Item__c;
            obj.sales_Order = woli.Sales_Order_Item__r.Sales_Order__r.Name;
            obj.sales_OrderId = '/'+woli.Sales_Order_Item__r.Sales_Order__c;
            obj.start_Date = woli.StartDate;
            obj.requested_Delivery_Date = woli.Sales_Order_Item__r.Requested_delivery_date__c;
            obj.lineStatus = woli.Sales_Order_Item__r.Status__c;
            obj.lineStatusColor = woli.Sales_Order_Item__r.Reject_Reason__c=='V2-Clerical Cancellation' ? 'slds-text-color_error' : 'slds-text-color_success';
            woliChangeTypeList.add(obj);
        }
        return woliChangeTypeList;
    }

    // STRY0130065 - Commented cacheble true
    @AuraEnabled//(cacheable=false)
    public static List<WOLIWrapperResponseChangeType> getLineItemsAfterDelete(String woId){
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        List<WorkOrderLineItem> getLineItems = [
            SELECT Id,StartDate,Change_Type__c, LineItemNumber, WorkOrder.WorkOrderNumber,WorkOrder.Status, 
                Sales_Order_Item__c,Sales_Order_Item__r.NEW__c,Sales_Order_Item__r.Name,Sales_Order_Item__r.Product__c,
                Sales_Order_Item__r.Product__r.Name,Sales_Order_Item__r.Product_Model__r.Name,
                Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Requested_delivery_date__c,Part__c,Part__r.Name,
                AssetId,Asset.Name,LocationId,Location.Name,Product2Id,Asset.RootAssetId,Asset.RootAsset.Name,
                Product2.Name,Location.SAP_Functional_Location__c,Asset_Name__c,SAP_WBS_Number__c,SAP_WBS__c,Asset.Product2.Name, 
                Asset.SAP_Reference__c, Asset.SAP_Functional_Location__c, Sales_Order_Item__r.Status__c, Sales_Order_Item__r.Reject_Reason__c
            FROM WorkOrderLineItem
            WHERE WorkOrderId=:woId AND RecordTypeId=:woLineRecordTypeId AND Sales_Order_Item__c <> null
                ORDER BY LineItemNumber
        ];  //Q2C: Dipali Chede: Remove WBS/NWA references

        List<WOLIWrapperResponseChangeType> woliChangeTypeList = new List<WOLIWrapperResponseChangeType>();

        for(WorkOrderLineItem woli:getLineItems){
            WOLIWrapperResponseChangeType obj = new WOLIWrapperResponseChangeType();
            obj.assetTooltip = woli.AssetId <> null ? 'SAP Reference - '+woli.Asset.SAP_Reference__c+'\nSAP Functional Location - '+ woli.Asset.SAP_Functional_Location__c: '';
            obj.Id = '/'+woli.Id;
            obj.lineItemNumber = woli.LineItemNumber;
            obj.soi_Change_Type = woli.Sales_Order_Item__r.NEW__c;
            obj.line_Change_Type = woli.Change_Type__c;
            obj.top_LevelId = woli.Asset.RootAssetId <> null ? '/'+woli.Asset.RootAssetId :'';
            obj.top_Level = woli.Asset.RootAsset.Name <> null ? woli.Asset.RootAsset.Name : '';
            obj.installed_ProductId = woli.AssetId <> null ? '/'+woli.AssetId : '';
            obj.installed_Product = woli.Asset.Name <> null ? woli.Asset.Name : '';
            obj.product_Model = woli.Part__r.Name;
            obj.workOrderStatus = woli.WorkOrder.Status;
            obj.location = woli.Location.Name <> null ? woli.Location.Name:'';
            obj.locationId = woli.LocationId <> null ? '/'+woli.LocationId:'';
            obj.sapFunctionalLoc = woli.Location.SAP_Functional_Location__c;
            obj.erp_WBS_Element = woli.SAP_WBS_Number__c <> null ? woli.SAP_WBS_Number__c:'';
            obj.erp_WBS_ElementId = woli.SAP_WBS__c <> null?'/'+woli.SAP_WBS__c:'';
            obj.soi_Product = woli.Sales_Order_Item__r.Product__r.Name;
            obj.so_Item_Number = woli.Sales_Order_Item__r.Name;
            obj.so_Item_NumberId = '/'+woli.Sales_Order_Item__c;
            obj.sales_Order = woli.Sales_Order_Item__r.Sales_Order__r.Name;
            obj.sales_OrderId = '/'+woli.Sales_Order_Item__r.Sales_Order__c;
            obj.start_Date = woli.StartDate;
            obj.requested_Delivery_Date = woli.Sales_Order_Item__r.Requested_delivery_date__c;
            obj.lineStatus = woli.Sales_Order_Item__r.Status__c;
            obj.lineStatusColor = woli.Sales_Order_Item__r.Reject_Reason__c=='V2-Clerical Cancellation' ? 'slds-text-color_error' : 'slds-text-color_success';
            woliChangeTypeList.add(obj);
        }
        return woliChangeTypeList;
    }

    @AuraEnabled(cacheable=false) 
    public static List<WOLIWrapperResponseChangeType> createPWO(String workOrderId,String subject,String accountId,
                                                    String locationId,String topLevelAssetId,String ownerId,
                                                    String installationManagerId,String installationType,
                                                    String internalComments,String selectedSOIList,Date startDate,Date endDate, String customerProfile,
                                                    String projectType, String upgradeType, String fullscale, String fullscaleType, String citrix,
                                                    List<String> projectScope, Date requestSubmitDate, String projectSharepoint, Date kickoffDate,
                                                    Date tboxUpgradeDate, Date prodGoLive, String currentVersion, String toVersion, 
                                                    String alternativesToSmartConnect, String customerContact, String salesOrderId, Integer countSA){
        List<WorkOrderLineItem> woLILines = new List<WorkOrderLineItem>();               
        String projScope = String.join(projectScope, ';');
        Case caseRecord = new Case();
        Id caserectypeid = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Installation').getRecordTypeId();
        caseRecord.RecordTypeId = caserectypeid;
        caseRecord.Subject = subject;
        //Add additional Install Case fields
        caseRecord.AccountId = accountId;
        caseRecord.Location__c = locationId;
        //Added null check as part of STRY0157845
        caseRecord.AssetId = !String.isEmpty(topLevelAssetId) ? topLevelAssetId : null;
        caseRecord.Asset_Top_Level__c =  !String.isEmpty(topLevelAssetId) ? topLevelAssetId : null;
        //STRY0157845 - End
        caseRecord.Priority = 'Low'; 
        //insert caseRecord;

        Id worectypeid = VFSL_Utility.getRecordTypeMap().get('WorkOrder').get('Planning_VIS');
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        WorkOrder wo = new WorkOrder();                                               
        wo.Subject = subject;
        wo.recordtypeid = worectypeid;
         
        if(accountId <> null && accountId != ''){
            wo.AccountId = accountId;
        }
        if(locationId <> null && locationId != ''){
            wo.LocationId = locationId;
        }
        if(installationManagerId <> null && installationManagerId != ''){
            wo.Installation_Manager__c = installationManagerId;
            wo.Installation_Manager_Email__c = [SELECT Id,Email FROM User WHERE Id= :installationManagerId].Email;
        }
            
        /*
         * VPS Logic - Start
         */                                                
                                                        
        if(String.isNotBlank(salesOrderId)) {
            Set<String> selectedSoiIdSet = new Set<String>();
            
            if(String.isNotBlank(selectedSOIList)){
                List<SelectedSalesOrderWrapper> results = 
                  (List<SelectedSalesOrderWrapper>) JSON.deserialize(selectedSOIList,List<SelectedSalesOrderWrapper>.class);
                
                for(SelectedSalesOrderWrapper resp : results){
                    selectedSoiIdSet.add(resp.salesOrderItemRecordId.substringAfter('/'));
                }
            }
            
            Sales_Order__c soRec = [SELECT Id, ERP_Document_Type__c,
                                     (SELECT Id, Asset__c, Asset__r.LocationId 
                                      FROM Sales_Order_Items__r 
                                      WHERE Sales_Order__c = :salesOrderId AND Id IN :selectedSoiIdSet)
                                    FROM Sales_Order__c 
                                    WHERE Id = :salesOrderId 
                                    LIMIT 1]; 
            
            if(soRec != null && String.isNotBlank(soRec.ERP_Document_Type__c) && soRec.ERP_Document_Type__c == 'ZPT0') {
                wo.VPS_Work_Order__c = true;
                Id childSoiAssetId;
                
                for(Sales_Order_Item__c soiRec : soRec.Sales_Order_Items__r) {
                    if(soiRec.Asset__c != null) {
                        childSoiAssetId = soiRec.Asset__c;
                        break;
                    }
                }
                
                if(childSoiAssetId != null) {
                    wo.Top_Level_Asset__c = childSoiAssetId;
                }
            }
        }
                                                        
        /*
         * VPS Logic - End
         */
                                                        
        if(topLevelAssetId <> null && topLevelAssetId != '' ){
            wo.Top_Level_Asset__c = topLevelAssetId;
            wo.AssetId = topLevelAssetId;
        }
        if(installationType <> null && installationType != '' && installationType <> 'None' ){
            wo.Installation_Type__c = installationType;
        }
        if(internalComments <> null && internalComments != '' ){
            wo.Description = internalComments;
        }
        if(ownerId <> null && ownerId != '' ){
             wo.OwnerId = ownerId;
             wo.Project_Manager__c = ownerId; 
             wo.Project_Manager_Email__c = [SELECT Id,Email From User Where Id=:ownerId].Email;
        }
           
        wo.Purpose_of_Visit__c = 'New Installation';
        if(installationManagerId != null && installationManagerId != ''){
            List<ServiceTerritory> serviceTerritory = [SELECT Id, District_Manager__c 
                                                       FROM ServiceTerritory 
                                                       WHERE District_Manager__c =: installationManagerId LIMIT 1];
        
            if(serviceTerritory.size() > 0){
                wo.Preferred_Service_Territory__c =serviceTerritory[0].Id;
            }
        }
        if(startDate <> null){
            Integer d = startDate.day();
            Integer mo = startDate.month();
            Integer yr = startDate.year();
            DateTime DT = DateTime.newInstance(yr, mo, d);
            wo.StartDate = DT;
        }
        if(endDate <> null){
            Integer d = endDate.day();
            Integer mo = endDate.month();
            Integer yr = endDate.year();
            DateTime DT = DateTime.newInstance(yr, mo, d);
            wo.EndDate = DT;
        }
        //STRY0172857 Shubham -- Adding changes for PWO redesign - changes start
        if(customerProfile!=null){
            wo.Customer_Profile__c=customerProfile;
        }
        if(projectType!=null){
            wo.Project_Type__c=projectType;
        }
        if(upgradeType!=null){
            wo.Upgrade_Type__c=upgradeType;
        }
        if(fullscale!=null){
            wo.Fullscale__c=fullscale;
        }
        if(fullscaleType!=null){
            wo.Fullscale_Project_Type__c=fullscaleType;
        }
        if(citrix!=null){
            wo.Citrix_Environment__c=citrix;
        }
        if(projectScope!=null){
            wo.Additional_Project_Scope__c=projScope;
        }
        if(requestSubmitDate!=null){
            wo.Date_Ship_Request_Submitted__c=requestSubmitDate;
        }
        if(projectSharepoint!=null){
            wo.Project_SharePoint2__c=projectSharepoint;
        }
        if(kickoffDate!=null){
            wo.Kickoff_Date__c=kickoffDate;
        }
        if(tboxUpgradeDate!=null){
            wo.Tbox_Upgrade_Date__c=tboxUpgradeDate;
        }
        if(prodGoLive!=null){
            wo.Prod_Go_Live_Date__c=prodGoLive;
        }
        if(currentVersion!=null){
            wo.Current_Version__c=currentVersion;
        }
        if(toVersion!=null){
            wo.To_Version__c=toVersion;
        }
        if(alternativesToSmartConnect!=null){
            wo.Alternatives_to_Access_Servers__c=alternativesToSmartConnect;
        }
        if(customerContact!=null){
            wo.Customer_Contact__c=customerContact;
        }
        //STRY0172857 Shubham -- Adding changes for PWO redesign - changes end
        caseRecord.AssetId = wo.AssetId;
        caseRecord.Asset_Top_Level__c = wo.Top_Level_Asset__c;
        caseRecord.Preferred_Service_Territory__c = wo.Preferred_Service_Territory__c;
        insert caseRecord;
                                                        
        wo.CaseId = caseRecord.Id;
            
        try{
            insert wo;
            //STRY0205136 Service Appointment Creation for PWO - START
            //Pass in for number of SAs requested.  Right now for single.
            if(installSACheck != null && installSACheck.Active__c){
                Integer finalCount = countSA != null ? countSA : 1; //if cleared, then keep 1 default
                List<ServiceAppointment> listSA = VFSL_ServiceAppointment.createSAForPWO(wo,finalCount);
                insert listSA; //STRY0205136 Service Appointment Creation.  Consider combination.
            }
            //STRY0205136 Service Appointment Creation for PWO - END
        }catch(DMLException e) {
            throw new AuraHandledException(processErrorMessage(e.getDmlMessage(0)));
        } 

        List<WorkOrderLineItem>  woliToBeInserted = CreateWorkOrderLineItems1(selectedSOIList,wo.Id);
        insert woliToBeInserted;
        woLILines = [SELECT Id,StartDate,Change_Type__c, LineItemNumber,WorkOrderId, WorkOrder.WorkOrderNumber,WorkOrder.Status, 
                        Sales_Order_Item__c,Sales_Order_Item__r.NEW__c,Sales_Order_Item__r.Name,Sales_Order_Item__r.Product__c,
                        Sales_Order_Item__r.Product__r.Name,Sales_Order_Item__r.Product_Model__r.Name,
                        Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Requested_delivery_date__c,Part__c,Part__r.Name,
                        AssetId,Asset.Name,LocationId,Location.Name,Product2Id,Asset.RootAssetId,Asset.RootAsset.Name,
                        Product2.Name,Asset_Name__c,SAP_WBS_Number__c,SAP_WBS__c, SAP_WBS__r.ERP_Project_Nbr__c,Asset.Product2.Name,
                        Asset.SAP_Reference__c, Asset.SAP_Functional_Location__c, Sales_Order_Item__r.Status__c, Sales_Order_Item__r.Reject_Reason__c 
                    FROM WorkOrderLineItem 
                    WHERE WorkOrderId=:wo.Id AND RecordTypeId=:woLineRecordTypeId];
        wo.Sales_Order__c = woLILines[0].Sales_Order_Item__r.Sales_Order__c <>  null ? woLILines[0].Sales_Order_Item__r.Sales_Order__c : null;
        wo.SAP_Project_Text__c  = woLILines[0].SAP_WBS__c <> null?woLILines[0].SAP_WBS__r.ERP_Project_Nbr__c:woLILines[0].SAP_WBS__r.ERP_Project_Nbr__c;//Q2C: Dipali Chede: Remove WBS/NWA reference
        update wo;

        List<WOLIWrapperResponseChangeType> woliChangeTypeList = new List<WOLIWrapperResponseChangeType>();

        for(WorkOrderLineItem woli: woLILines){
            WOLIWrapperResponseChangeType obj = new WOLIWrapperResponseChangeType();
            obj.assetTooltip = woli.AssetId <> null ? 'SAP Reference - '+woli.Asset.SAP_Reference__c+'\nSAP Functional Location - '+ woli.Asset.SAP_Functional_Location__c: '';
            obj.Id = '/'+woli.Id;
            obj.workOrderId = wo.Id;
            obj.lineItemNumber = woli.LineItemNumber;
            obj.soi_Change_Type = woli.Sales_Order_Item__r.NEW__c;
            obj.line_Change_Type = woli.Change_Type__c;
            obj.top_LevelId = woli.Asset.RootAssetId <> null ? '/'+woli.Asset.RootAssetId :'';
            obj.top_Level = woli.Asset.RootAsset.Name <> null ? woli.Asset.RootAsset.Name : '';
            obj.installed_ProductId = woli.AssetId <> null ? '/'+woli.AssetId : '';
            obj.installed_Product = woli.Asset.Name <> null ? woli.Asset.Name : '';
            obj.product_Model = woli.Part__r.Name;
            obj.workOrderStatus = woli.WorkOrder.Status;
            obj.location = woli.Location.Name <> null ? woli.Location.Name:'';
            obj.locationId = woli.LocationId <> null ? '/'+woli.LocationId:'';
            obj.erp_WBS_Element = woli.SAP_WBS_Number__c <> null ? woli.SAP_WBS_Number__c:'';
            obj.erp_WBS_ElementId = woli.SAP_WBS__c <> null?'/'+woli.SAP_WBS__c:'';
            obj.soi_Product = woli.Sales_Order_Item__r.Product__r.Name;
            obj.so_Item_Number = woli.Sales_Order_Item__r.Name;
            obj.so_Item_NumberId = '/'+woli.Sales_Order_Item__c;
            obj.sales_Order = woli.Sales_Order_Item__r.Sales_Order__r.Name;
            obj.sales_OrderId = '/'+woli.Sales_Order_Item__r.Sales_Order__c;
            obj.start_Date = woli.StartDate;
            obj.requested_Delivery_Date = woli.Sales_Order_Item__r.Requested_delivery_date__c;
            obj.lineStatus = woli.Sales_Order_Item__r.Status__c;
            obj.lineStatusColor = woli.Sales_Order_Item__r.Reject_Reason__c=='V2-Clerical Cancellation' ? 'slds-text-color_error' : 'slds-text-color_success';
            woliChangeTypeList.add(obj);
        }
        return woliChangeTypeList;
    }

    @AuraEnabled(cacheable=false)
    public static Id getPreferredServiceTerritory(String installationManagerId){
        if(installationManagerId <> null && installationManagerId <> ''){
            List<ServiceTerritory> serviceTerritory = [SELECT Id,Name, District_Manager__c 
                                                        FROM ServiceTerritory
                                                        WHERE District_Manager__c =: installationManagerId LIMIT 1];
            if(serviceTerritory.size() > 0){
                return serviceTerritory[0].Id;
            }else{ 
                return null;
            }
        }
        return null;
    } 
    
    public static String processErrorMessage(String message) {
        String errorMsg = '';
        if(message.containsIgnoreCase('bad value for restricted picklist field')) {
            String salesOrg = message.replace('bad value for restricted picklist field: ','');
            salesOrg = salesOrg.replace(': [Sales_Org__c]',''); 
            errorMsg = 'Please put in a ticket to get PWO updated for Sales Org ' + salesOrg;
        } else {
            errorMsg = message;
        }
        return errorMsg;
    }
    
    @AuraEnabled(cacheable=false)
    public static List<WOLIWrapperResponseChangeType> createWorkOrderLineItemForUpdateInstallation(String selectedSOIList,String workOrderId){
        isUpdated = true;
        List<WorkOrderLineItem> woLILines = new List<WorkOrderLineItem>();
        List<WorkOrderLineItem>  woliToBeInserted = CreateWorkOrderLineItems1(selectedSOIList,workOrderId);
        insert woliToBeInserted;
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        woLILines = [SELECT Id,StartDate,Change_Type__c, LineItemNumber,WorkOrderId, WorkOrder.WorkOrderNumber,WorkOrder.Status, 
                        Sales_Order_Item__c,Sales_Order_Item__r.NEW__c,Sales_Order_Item__r.Name,Sales_Order_Item__r.Product__c,
                        Sales_Order_Item__r.Product__r.Name,Sales_Order_Item__r.Product_Model__r.Name,
                        Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Requested_delivery_date__c,Part__c,Part__r.Name,
                        AssetId,Asset.Name,LocationId,Location.Name,Product2Id,Asset.RootAssetId,Asset.RootAsset.Name,
                        Product2.Name,Asset_Name__c,SAP_WBS_Number__c,SAP_WBS__c, SAP_WBS__r.ERP_Project_Nbr__c,Asset.Product2.Name, 
                        Asset.SAP_Reference__c, Asset.SAP_Functional_Location__c, Sales_Order_Item__r.Status__c, Sales_Order_Item__r.Reject_Reason__c 
                    FROM WorkOrderLineItem
                    WHERE WorkOrderId=:workOrderId AND RecordTypeId=:woLineRecordTypeId
                        ORDER BY LineItemNumber];
        List<WOLIWrapperResponseChangeType> woliChangeTypeList = new List<WOLIWrapperResponseChangeType>();
        for(WorkOrderLineItem woli:woLILines){
            WOLIWrapperResponseChangeType obj = new WOLIWrapperResponseChangeType();
            obj.assetTooltip = woli.AssetId <> null ? 'SAP Reference - '+woli.Asset.SAP_Reference__c+'\nSAP Functional Location - '+ woli.Asset.SAP_Functional_Location__c: '';
            obj.Id = '/'+woli.Id;
            obj.lineItemNumber = woli.LineItemNumber;
            obj.soi_Change_Type = woli.Sales_Order_Item__r.NEW__c;
            obj.line_Change_Type = woli.Change_Type__c;
            obj.top_LevelId = woli.Asset.RootAssetId <> null ? '/'+woli.Asset.RootAssetId :'';
            obj.top_Level = woli.Asset.RootAsset.Name <> null ? woli.Asset.RootAsset.Name : '';
            obj.installed_ProductId = woli.AssetId <> null ? '/'+woli.AssetId : '';
            obj.installed_Product = woli.Asset.Name <> null ? woli.Asset.Name : '';
            obj.product_Model = woli.Part__r.Name;
            obj.workOrderStatus = woli.WorkOrder.Status;
            obj.location = woli.Location.Name <> null ? woli.Location.Name:'';
            obj.locationId = woli.LocationId <> null ? '/'+woli.LocationId:'';
            obj.erp_WBS_Element = woli.SAP_WBS_Number__c <> null ? woli.SAP_WBS_Number__c:'';
            obj.erp_WBS_ElementId = woli.SAP_WBS__c <> null?'/'+woli.SAP_WBS__c:'';
            obj.soi_Product = woli.Sales_Order_Item__r.Product__r.Name;
            obj.so_Item_Number = woli.Sales_Order_Item__r.Name;
            obj.so_Item_NumberId = '/'+woli.Sales_Order_Item__c;
            obj.sales_Order = woli.Sales_Order_Item__r.Sales_Order__r.Name;
            obj.sales_OrderId = '/'+woli.Sales_Order_Item__r.Sales_Order__c;
            obj.start_Date = woli.StartDate;
            obj.requested_Delivery_Date = woli.Sales_Order_Item__r.Requested_delivery_date__c;
            obj.lineStatus = woli.Sales_Order_Item__r.Status__c;
            obj.lineStatusColor = woli.Sales_Order_Item__r.Reject_Reason__c=='V2-Clerical Cancellation' ? 'slds-text-color_error' : 'slds-text-color_success';
            woliChangeTypeList.add(obj);
        }
        return woliChangeTypeList;
    }

    @AuraEnabled(cacheable=false) 
    public static WBSWrapperResponse addMethod1(String response){  
        boolean otherCategoriesPresent = false;
        boolean alreadyPlanned = false;
        Set<String> wbsElementIds = new Set<String>();
        Set<String> SOIIds = new Set<String>();
        List<Asset> instProdList = new List<Asset>();
        Set<String> instProdSet = new Set<String>();
        Set<Id> selectedRowsId = new Set<Id>();
        String  site_partner_code; 
        String sitepartnername='';
        String workOrderLocationName = '';
        String workOrderAccountName = '';
        String ownername= Userinfo.getuserid();
        String erpReferenceNumber;
        Date workOrderStartDate;
        Date workOrderEndDate;
        Id locationId; // Added as part of STRY0157845
        workOrderRecord = new WorkOrder(OwnerId=Userinfo.getuserid());
        List<Schema.Location> listLocation = new List<Schema.Location>();
        List<SelectSalesOrderResponse> results = (List<SelectSalesOrderResponse>) JSON.deserialize(response,List<SelectSalesOrderResponse>.class);
        for(SelectSalesOrderResponse resp : results){
            selectedRowsId.add(resp.salesItemId.substringAfter('/'));
        }
        /* Query no longer has Site_Partner_Name__c and Parent_Item_Name__c because SOI object in SFQA1 doesn't have those fields */
        String soql= 'Select Id,Name,Sales_Order__c,Sales_Order__r.Name,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, SOI_Location__c, Requested_delivery_date__c , ERP_Equipment_Number__c, Asset__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, Asset__r.Top_Level_Asset__c, Asset__r.RootAssetId, New__c,Site_Partner__r.Name, '+
              ' ERP_Material_Number__c , Sales_Order_Number__c , Parent_Item__c ,Parent_Item__r.Name,Site_Partner__c,ERP_Item_Category__c,Product__c, Asset__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c '+
              ' FROM Sales_Order_Item__c  where Id IN:selectedRowsId';
        /*String soql= 'Select Id,Name,Sales_Order__c,Sales_Order__r.Name,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, SOI_Location__c, Site_Partner_Name__c, Parent_Item_Name__c, Requested_delivery_date__c , ERP_Equipment_Number__c, Asset__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, Asset__r.Top_Level_Asset__c, Asset__r.RootAssetId, New__c,Site_Partner__r.Name, '+
              ' ERP_Material_Number__c , Sales_Order_Number__c , Parent_Item__c ,Parent_Item__r.Name,Site_Partner__c,ERP_Item_Category__c,Product__c, Asset__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c '+
              ' FROM Sales_Order_Item__c  where Id IN:selectedRowsId';*/

        selectedSOIList = (List<Sales_Order_Item__c>)database.query(soql);
        List<SelectedSalesOrderWrapper> selectedList = new List<SelectedSalesOrderWrapper>();
        for(Sales_Order_Item__c soi : selectedSOIList){ 
            SelectedSalesOrderWrapper obj = new SelectedSalesOrderWrapper();
            locationId = soi.SOI_Location__c;
            wbsElementIds.add(soi.WBS_Element__c);
            if(soi.ERP_Item_Category__c != 'Z001' && soi.ERP_Item_Category__c != 'ZN01'
               && soi.ERP_Item_Category__c != 'ZN18' && soi.ERP_Item_Category__c != 'Z018'){
                otherCategoriesPresent = true; 
            }
            if(soi.Case_Line_Status__c == 'Already Planned' 
                && (soi.ERP_Item_Category__c == 'Z001' || soi.ERP_Item_Category__c == 'ZN01' || soi.ERP_Item_Category__c == 'ZN18' || soi.ERP_Item_Category__c == 'Z018')){
                alreadyPlanned = true; 
            }
            if(site_partner_code == null){
                if(soi.ERP_Site_Partner_Code__c != null){
                    site_partner_code = soi.ERP_Site_Partner_Code__c;
                }else if(soi.Sales_Order__r.ERP_Site_Partner__c != null ){
                    site_partner_code = soi.Sales_Order__r.ERP_Site_Partner__c;
                }else{
                    //no site partner code found
                }
            }
            obj.accountNumber = soi.Site_Partner__r.Name;
            obj.salesOrderNumber = soi.Sales_Order__r.Name;
            obj.salesOrderRecordId = soi.Sales_Order__c <> null ? '/'+soi.Sales_Order__c : null;
            obj.salesOrderItemNumber = soi.Name;
            obj.salesOrderItemRecordId = soi.Id <> null ? '/'+soi.Id : null;
            obj.startDate = soi.Start_Date__c;
            obj.requested_Delivery_Date = soi.Requested_delivery_date__c;
            obj.changeType = soi.NEW__c;
            obj.erp_Material_Number = soi.ERP_Material_Number__c;
            obj.material_Description = soi.Material_Description__c;
            obj.erp_Item_Category = soi.ERP_Item_Category__c;
            obj.erp_Sales_Order_Number = soi.Sales_Order_Number__c;
            obj.parentItemNumber = soi.Parent_Item__r.Name;
            obj.parentItemRecordId = soi.Parent_Item__c <> null ? '/'+soi.Parent_Item__c : null;
            obj.product = soi.Product__r.Name;
            obj.productRecordId = soi.Product__c <> null ? '/'+soi.Product__c : null;
            selectedList.add(obj);
        }
        if(otherCategoriesPresent == false && alreadyPlanned == false){
            if(site_partner_code != null){
                // Modified Filter to Id as part of STRY0157845
                listLocation = [SELECT Id, Name, Account__c,Account__r.Name FROM Location 
                                WHERE Id = :locationId LIMIT 1];
                if(listLocation.size() > 0){
                    workOrderRecord.LocationId = listLocation[0].Id <> null ? listLocation[0].Id : null;
                    workOrderRecord.AccountId = listLocation[0].Account__c <> null ? listLocation[0].Account__c : null;
                    workOrderLocationName = listLocation[0].Name <> null ?  listLocation[0].Name : null;
                    workOrderAccountName = listLocation[0].Account__r.Name <> null ? listLocation[0].Account__r.Name : null ;
                }   
            }
            try{
                //Forecast Start Date
                List<ERP_WBS__c> lowestStartDate = [SELECT Id,Forecast_Start_Date__c 
                                                    FROM ERP_WBS__c 
                                                    WHERE NAME IN :wbsElementIds ORDER BY Forecast_Start_Date__c ASC];
                //Forecast End Date
                List<ERP_WBS__c> highestEndDate =[SELECT Id,Forecast_End_Date__c 
                                                  FROM ERP_WBS__c 
                                                  WHERE Name IN :wbsElementIds ORDER BY Forecast_End_Date__c DESC];
                if(lowestStartDate.size() > 0){
                    workOrderStartDate = lowestStartDate[0].Forecast_Start_Date__c;   
                }
                if(highestEndDate.size() > 0){
                    workOrderEndDate = highestEndDate[0].Forecast_End_Date__c;    
                }
            }
            catch(Exception ex){
                System.debug('Error Message'+ex.getMessage());
            }
            if(workOrderRecord.Id == null){
                WBSWrapperResponse responseWrapper = new WBSWrapperResponse();
                responseWrapper.locationName = workOrderLocationName;
                responseWrapper.locationId = workOrderRecord.LocationId;
                responseWrapper.accountName = workOrderAccountName;
                responseWrapper.accountId = workOrderRecord.AccountId;
                responseWrapper.InstallationManagerName = null;
                responseWrapper.InstallationManagerId = null;
                responseWrapper.ownerName = UserInfo.getName();
                responseWrapper.ownerId = UserInfo.getUserId();
                responseWrapper.TopLevelAssetName = null;
                responseWrapper.TopLevelAssetName = null;
                responseWrapper.otherItemCategoriesPresent = false;
                responseWrapper.alreadyPlannedItem = false;
                responseWrapper.selectedSOIList = selectedList;
                responseWrapper.startDate = workOrderStartDate;
                responseWrapper.endDate = workOrderEndDate;
                return responseWrapper;
            }else{
                return null;
            }
        }else{
            WBSWrapperResponse responseWrapper = new WBSWrapperResponse();
                responseWrapper.Id = null; 
                responseWrapper.locationName = null;
                responseWrapper.accountName = null;
                responseWrapper.InstallationManagerName = null;
                responseWrapper.ownerName = null;
                responseWrapper.TopLevelAssetName = null;
                responseWrapper.otherItemCategoriesPresent = otherCategoriesPresent;
                responseWrapper.alreadyPlannedItem = alreadyPlanned;
            return responseWrapper; 
        }
    }

    @AuraEnabled(cacheable=false) 
    public static list<WOLIWrapperResponse>  addWorkOrderDetails(string woId){
        if(woId != null){
            WorkOrder wo = [SELECT Id,Owner.Name,AccountId,Account.Name,LocationId,Location.Name 
                            FROM WorkOrder WHERE ID = :woId];
            List<WorkOrderLineItem> woLILines = [SELECT Id,WorkOrderId,LineItemNumber,WorkOrder.WorkOrderNumber,
                                                    Sales_Order_Item__c,Sales_Order_Item__r.Name,Asset_Name__c,
                                                    SAP_WBS_Number__c,SAP_WBS__c 
                                                 FROM WorkOrderLineItem WHERE WorkOrderId= :woId];
            list<WOLIWrapperResponse> responseWrapperFinal = new list<WOLIWrapperResponse>();
            for(WorkOrderLineItem woli : woLILines){
                WOLIWrapperResponse responseWrapper = new WOLIWrapperResponse();
                responseWrapper.WorkOrderItemName = woLILines[0].LineItemNumber;
                responseWrapper.SalesOrderName = woLILines[0].Sales_Order_Item__r.Name;
                responseWrapper.WorkOrderName = woLILines[0].WorkOrder.WorkOrderNumber;
                responseWrapperFinal.add(responseWrapper);
            }
            return responseWrapperFinal;
        }else{
            return null;
        }
    }

    public class SelectSalesOrderResponseList{
        @AuraEnabled public List<SelectSalesOrderResponse> availableForPlanning{get;set;}
        @AuraEnabled public List<SelectSalesOrderResponse> alreadyPlanning{get;set;} 
    }

    //serach WBS to be added for planning
    @AuraEnabled(cacheable=false) 
    public static SelectSalesOrderResponseList fnSearch11(String sitepartner,String project,String toplevel,string erpItemCategoryVal, string caseLineStatus,String allowAcceptanceDateLwc, List<Id> salesOrderList) {
        System.debug('@fnSearch11');
        String MaterialDescription = '%Advantage Contract%';
        String exludedMaterialNumber = '(\'VC_APPS_TRN\',\'VC_EDUCATION\')'; //STRY0211061 - Removed VC_PROF_SERV from exludedMaterialNumber
        if(allowAcceptanceDateLwc <> null && allowAcceptanceDateLwc <> ''){
            allowAcceptanceDate = Boolean.valueOf(allowAcceptanceDateLwc); 
        }
        //Added by Swati
        Boolean isSimpleSO = false;
        Boolean isHybrid = false;
        if(!salesOrderList.isEmpty()){
            Boolean notSimpleSO = false;
            List<Sales_Order__c> lstSO = [SELECT id, SimpleSO__c FROM Sales_Order__c WHERE id IN :salesOrderList];
            for(Sales_Order__c SO : lstSO){
                if(so.simpleSO__c) {
                    isSimpleSO = true;
                } else {
                    notSimpleSO = true;
                }
            }
            if(isSimpleSO && notSimpleSO) {
                isHybrid = true;
            }
        } 
       
        List<String> ItemCatlist = erpItemCategoryVal.split('/');
        List<String> finalerpItemCatVal = new List<String>();
        
        if(!ItemCatlist.isEmpty()){
            if(!isHybrid) {
                if(isSimpleSO == true){
                    finalerpItemCatVal.add(ItemCatlist[1]);
                }else{
                    finalerpItemCatVal.add(ItemCatlist[0]); 
                }    
            } else {
                finalerpItemCatVal.addAll(ItemCatlist);
            }
        }
        
        String soql = '';
        if(isSimpleSO && !isHybrid) {
            soql= soql +'Select id,Name,Sales_Order__c,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c,Material_Group__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, Requested_delivery_date__c , ERP_Equipment_Number__c, Installed_Product__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, New__c,  '+
              ' ERP_Material_Number__c , Sales_Order_Number__c ,Sales_Order__r.Name, Parent_Item__c ,Parent_Item__r.Name, Site_Partner__c,Site_Partner__r.Name,ERP_Item_Category__c,Product__c,Installed_Product__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c, SimpleSOI__c,Reject_Reason__c, '+
              ' Forecast_End_Date__c,Forecast_Start_Date__c,Description__c,Quantity__c,Installation_Allocation_Quantity__c,'+/*
              ' (Select id, name, Description__c, Status__c, Forecast_End_Date__c,Forecast_Start_Date__c,Acceptance_Date__c from SOI_Event__r Limit 1 ) '+*/
              ' (SELECT Id, ERP_Item_Category__c,Installation_Allocation_Quantity__c FROM Sales_Order_Items__r WHERE ERP_Item_Category__c = \'ZN18\' OR ERP_Item_Category__c = \'Z018\' LIMIT 1) ' + 
              ' FROM Sales_Order_Item__c where id!=null and Reject_Reason__c = null AND ((Material_Group__c = \'000000000\' AND (NOT Material_Description__c LIKE :MaterialDescription )'+
              ' AND (NOT ERP_Material_Number__c IN ' + exludedMaterialNumber + ')) OR (Installation_Allocation_Quantity__c != null))';
        
        } else { //added by Swati
            soql=  soql + 'Select id,Name,Sales_Order__c,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c,Material_Group__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, Requested_delivery_date__c , ERP_Equipment_Number__c, Installed_Product__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, New__c,Quantity__c,  '+
              ' ERP_Material_Number__c , Sales_Order_Number__c ,Sales_Order__r.Name, Parent_Item__c ,Parent_Item__r.Name, Site_Partner__c,Site_Partner__r.Name,ERP_Item_Category__c,Product__c,Installed_Product__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c, SimpleSOI__c, Reject_Reason__c,'+
              ' Forecast_End_Date__c,Forecast_Start_Date__c,Description__c,' +
              ' (Select id,Description__c, Status__c, Forecast_End_Date__c,Forecast_Start_Date__c,Acceptance_Date__c from ERP_WBS__r), '+
              ' (SELECT Id, ERP_Item_Category__c FROM Sales_Order_Items__r WHERE ERP_Item_Category__c = \'ZN18\' OR ERP_Item_Category__c = \'Z018\' LIMIT 1) ' + 
              ' FROM Sales_Order_Item__c where id!=null and Reject_Reason__c = null AND (Material_Group__c = \'000000000\' OR  (Material_Description__c LIKE :MaterialDescription AND Material_Group__c != \'000000000\'))'+
              ' AND (NOT ERP_Material_Number__c IN ' + exludedMaterialNumber + ')';
        }
        String countSoql= 'select count() FROM Sales_Order_Item__c where id!=null and Reject_Reason__c = null ';
        if(sitepartner != null && sitepartner != ''){
            soql = soql + ' and Site_Partner__c = \''+sitepartner+'\'';
            countSoql = countSoql + ' and Site_Partner__c = \''+sitepartner+'\'';
        }
        if(salesOrderList != null && !salesOrderList.isEmpty()){
            soql = soql + ' and Sales_Order__c in :salesOrderList';
            countSoql = countSoql + ' and Sales_Order__c in :salesOrderList';
        }
        if(toplevel != null && toplevel != ''){
            soql = soql + ' and Asset__c = \''+toplevel+'\'';
            countSoql = countSoql + ' and Asset__c = \''+toplevel+'\'';
        }
        // replace erpItemCategoryVal  with finalerpItemCatVal
        if(!finalerpItemCatVal.isEmpty()){
            soql = soql + ' and ERP_Item_Category__c IN :finalerpItemCatVal';
            countSoql = countSoql + ' and ERP_Item_Category__c IN :finalerpItemCatVal';
        }
        if(caseLineStatus != null && caseLineStatus != ''){
            if(caseLineStatus == system.label.Case_Line_Status_AU){
                soql = soql + ' and Sales_Order__r.Block__c!=\'70-Order Cancellation\'' + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
                countSoql = countSoql + ' and Sales_Order__r.Block__c!=\'70-Order Cancellation\'' + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
            }else{
                soql = soql + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
                countSoql = countSoql + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
            }
        } 
        integer OffsetSizeTemp = OffsetSize;
        integer LimitSizeTemp = LimitSize;
        totalRecs = database.countQuery(countSoql);
        //STRY0563313 - removed LIMIT and offset
        //soql = soql + ' LIMIT :LimitSizeTemp OFFSET :OffsetSizeTemp' ;
  
        system.debug('soql: ' + soql);
        List<Sales_Order_Item__c> soiList = (List<Sales_Order_Item__c>)database.query(soql);
        List<Sales_Order_Item__c> validSoiList = new List<Sales_Order_Item__c>();
       
        if(String.isNotBlank(caseLineStatus) && caseLineStatus.equalsIgnoreCase(LINE_STATUS_AVAILABLE)) {
            for(Sales_Order_Item__c soiRec : soiList) {
                if(VFSL_UnityBacklogController.checkSoiMatDesc(soiRec)) {
                    if(soiRec.ERP_Item_Category__c == 'ZN01' || soiRec.ERP_Item_Category__c == 'Z001') {
                        List<Sales_Order_Item__c> childSoiList = (List<Sales_Order_Item__c>)soiRec.Sales_Order_Items__r;
                        if(childSoiList.isEmpty()) {
                            validSoiList.add(soiRec);
                        }
                    } else if(soiRec.ERP_Item_Category__c != 'ZN01' && soiRec.ERP_Item_Category__c != 'Z001') {
                        validSoiList.add(soiRec);
                    }    
                }
            }
            
        } else {
            validSoiList.addAll(soiList);
        }
        
        SelectSalesOrderResponseList response= VFSL_CreateInstallationPlanController.generateSelectSalesOrderItemResponse(validSoiList);
        return response;       
    }

    @AuraEnabled(cacheable=false) 
    public static List<SelectSalesOrderResponse> fnSearch11AlreadyPlanned(String sitepartner,String project,String toplevel,string erpItemCategoryVal, string caseLineStatus,String allowAcceptanceDateLwc) {
        if(allowAcceptanceDateLwc <> null && allowAcceptanceDateLwc <> ''){
            allowAcceptanceDate = Boolean.valueOf(allowAcceptanceDateLwc); 
        }
        String soql= 'Select id,Name,Sales_Order__c,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, Requested_delivery_date__c ,ERP_Equipment_Number__c, Installed_Product__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, New__c,  '+
              ' ERP_Material_Number__c , Sales_Order_Number__c ,Sales_Order__r.Name, Parent_Item__c ,Parent_Item__r.Name, Site_Partner__c,Site_Partner__r.Name,ERP_Item_Category__c,Product__c,Installed_Product__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c, '+
              ' (Select id,Description__c, Status__c, Forecast_End_Date__c,Forecast_Start_Date__c,Acceptance_Date__c from ERP_WBS__r limit 1) '+
              ' FROM Sales_Order_Item__c where id!=null and Reject_Reason__c = null ';
        
        String countSoql= 'select count() FROM Sales_Order_Item__c where id!=null and Reject_Reason__c = null ';
        if(sitepartner != null && sitepartner != ''){
            soql = soql + ' and Site_Partner__c = \''+sitepartner+'\'';
            countSoql = countSoql + ' and Site_Partner__c = \''+sitepartner+'\'';
        }
        if(project != null && project != ''){
            soql = soql + ' and Sales_Order__c  = \''+project+'\'';
            countSoql = countSoql + ' and Sales_Order__c  = \''+project+'\'';
        }
        if(toplevel != null && toplevel != ''){
            soql = soql + ' and Asset__c = \''+toplevel+'\'';
            countSoql = countSoql + ' and Asset__c = \''+toplevel+'\'';
        }
        if(erpItemCategoryVal != null && erpItemCategoryVal != 'None' ){
            soql = soql + ' and ERP_Item_Category__c  = \''+erpItemCategoryVal+'\'';
            countSoql = countSoql + ' and ERP_Item_Category__c  = \''+erpItemCategoryVal+'\'';
        }
        if(caseLineStatus != null && caseLineStatus != ''){
            if(caseLineStatus == system.label.Case_Line_Status_AU){
                soql = soql + ' and Sales_Order__r.Block__c!=\'70-Order Cancellation\'' + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
                countSoql = countSoql + ' and Sales_Order__r.Block__c!=\'70-Order Cancellation\'' + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
            }else{
                soql = soql + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
                countSoql = countSoql + ' and Case_Line_Status__c = \''+caseLineStatus+'\'';
            }
        } 
         
        integer OffsetSizeTemp = OffsetSize;
        integer LimitSizeTemp = LimitSize;
        totalRecs = database.countQuery(countSoql);
        //STRY0563313 - removed LIMIT and offset
        soql = soql + ' LIMIT :LimitSizeTemp OFFSET :OffsetSizeTemp' ;
        List<Sales_Order_Item__c> soiList =  (List<Sales_Order_Item__c>)database.query(soql);
        List<SelectSalesOrderResponse> response= VFSL_CreateInstallationPlanController.fetchAlreadyPlannedSOI(soiList);
        return response;
    }

    @TestVisible
    private static Map<Id,ERP_WBS__c> getWbsMap(Set<Id> relatedWBSids){
        Set<String> erp_Std_Key_Set = new Set<String>{'INST001', 'INST003'};
        Map<Id,ERP_WBS__c> mapOFErpWBS = new Map<Id,ERP_WBS__c>(
                                          [SELECT Id, Status__c,
                                            (SELECT id FROM erp_NWA__r 
                                            WHERE ERP_Status__c INCLUDES ('REL') 
                                                AND ERP_Status__c EXCLUDES('DLFL','CLSD', 'TECO') 
                                                AND ERP_Std_Text_Key__c IN :erp_Std_Key_Set 
                                            LIMIT 1) 
                                           FROM ERP_WBS__c 
                                           WHERE Id IN: relatedWBSids 
                                            AND Status__c   INCLUDES ('REL') 
                                            AND Status__c EXCLUDES('DLFL','CLSD', 'TECO') ]);
        return mapOFErpWBS;
    }
    
    /*@TestVisible
    private static Map<Id,SOI_EVENT__c> getSOIEvntMap(Set<Id> relatedSOIEvntids){
       // Set<String> Std_Key_Set = new Set<String>{'INST001', 'INST003'};
        Map<Id,SOI_Event__c> mapOFSOIEvnt = new Map<Id,SOI_Event__c>(
                                          [Select Id, Status__c
                                           From SOI_EVENT__c 
                                           Where Id IN: relatedSOIEvntids 
                                           AND Status__c = 'Backlog' ]);
                                           //AND Status__c excludes('DLFL','CLSD', 'TECO') 
                                           //and ERP_Status__c  includes ('Backlog') 
                                           //and ERP_Status__c excludes('DLFL','CLSD', 'TECO') 
                                          // and Std_Text_Key__c IN: Std_Key_Set 
        return mapOFSOIEvnt;
    }*/
    
    @TestVisible
    public static List<WorkOrderLineItem> createWorkOrderLineItems1(String selectedSOIListResponse,String workOrderId){
        Set<Id> selectedRowsId = new Set<Id>();
        List<Sales_Order_Item__c> selectedSOIList = new List<Sales_Order_Item__c>();
        Set<String> wbsNameSet = new Set<String>();
        Map<String,ERP_WBS__c> wbsMap = new Map<string,ERP_WBS__c>();
        //Map<String,List<ERP_NWA__c>> mapERPWBS2ERPNWA = new Map<String,List<ERP_NWA__c>>();
        List<ERP_WBS__c> wbsList = new List<ERP_WBS__c>();
        List<Sales_Order_Item__c> wbsItems = new List<Sales_Order_Item__c>();
        List<WorkOrderLineItem> woLILines = new List<WorkOrderLineItem>(); 
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        if(!isUpdated){
            List<SelectedSalesOrderWrapper> results = (List<SelectedSalesOrderWrapper>) JSON.deserialize(selectedSOIListResponse,List<SelectedSalesOrderWrapper>.class);
            for(SelectedSalesOrderWrapper resp : results){
                selectedRowsId.add(resp.salesOrderItemRecordId.substringAfter('/'));
            }
        }else if(isUpdated){
            List<SelectSalesOrderResponse> results = (List<SelectSalesOrderResponse>) JSON.deserialize(selectedSOIListResponse,List<SelectSalesOrderResponse>.class);
            for(SelectSalesOrderResponse resp : results){
                selectedRowsId.add(resp.salesItemId.substringAfter('/'));
            }
        }
        /* Query no longer has Site_Partner_Name__c and Parent_Item_Name__c because SOI object in SFQA1 doesn't have those fields */
        String soql= 'Select Id,Name,Sales_Order__c,Sales_Order__r.Name,Sales_Order__r.Block__c,Location__c ,ERP_Higher_Level_Item__c ,ERP_Site_Partner_Code__c, ' +
              ' Sales_Order__r.ERP_Site_Partner__c, SOI_Location__c, Requested_delivery_date__c , ERP_Equipment_Number__c, Asset__r.Name, ERP_Reference__c, '+
              ' Acceptance_date__c, Asset__r.Top_Level_Asset__c, Asset__r.RootAssetId, New__c,Site_Partner__r.Name, Forecast_Start_Date__c,Forecast_End_Date__c, '+
              ' ERP_Material_Number__c , Sales_Order_Number__c , Parent_Item__c ,Parent_Item__r.Name,Site_Partner__c,ERP_Item_Category__c,Product__c, Asset__c, '+
              ' Status__c,Case_Line_Status__c,WBS_Element__c,Material_Description__c,End_Date__c,Start_Date__c,Product__r.Name,Product_Model__c '+
              ' FROM Sales_Order_Item__c  where Id IN:selectedRowsId';

        selectedSOIList =  (List<Sales_Order_Item__c>)database.query(soql);
        for(Sales_Order_Item__c soiList : selectedSOIList){
            if(soiList.WBS_Element__c != null){
                wbsNameSet.add(soiList.WBS_Element__c);
            }
        }
        wbsList = [SELECT Id,Name,Forecast_End_Date__c,Forecast_Start_Date__c
                   FROM ERP_WBS__c WHERE Name IN :wbsNameSet];
        if(wbsList.Size()>0){
            for(ERP_WBS__c wbs : wbsList){
                wbsMap.put(wbs.Name, wbs);
            }
        } 
        
        for(Sales_Order_Item__c sw : selectedSOIList){ 
            WorkOrderLineItem newcaseline= new WorkOrderLineItem();
            //newcaseline.Id = sw.Id; 
            newcaseline.recordtypeid = woLineRecordTypeId;
            newcaseline.Billing_Type__c = 'I – Installation';
            if(newcaseline.Id == null){
                newcaseline.WorkOrderId = workOrderId;
            }
            if(wbsMap.get(sw.WBS_Element__c) <> null){
                newcaseline.SAP_WBS__c = wbsMap.get(sw.WBS_Element__c).Id;
            }
            newcaseline.AssetId = sw.Asset__c;
            newcaseline.Sales_Order__c = sw.Sales_Order__c;  //Dipali Chede: Q2C Phase 5
            newcaseline.Sales_Order_Item__c = sw.Id;
            
            if(wbsMap != null && wbsMap.containsKey(sw.WBS_Element__c)){
                if(wbsMap.get(sw.WBS_Element__c).Forecast_Start_Date__c != null ){
                    date dtStart = wbsMap.get(sw.WBS_Element__c).Forecast_Start_Date__c;
                    newcaseline.StartDate =  datetime.newInstance(dtStart.year(), dtStart.month(), dtStart.day());
                }
                if(wbsMap.get(sw.WBS_Element__c).Forecast_End_Date__c != null ){
                    date dtEnd = wbsMap.get(sw.WBS_Element__c).Forecast_End_Date__c;
                    newcaseline.EndDate =  datetime.newInstance(dtEnd.year(), dtEnd.month(), dtEnd.day());
                }
            }else{ //Added for Training event
                if(sw.Forecast_Start_Date__c != null){
                    date dtStart = sw.Forecast_Start_Date__c;
                    newcaseline.StartDate =  datetime.newInstance(dtStart.year(), dtStart.month(), dtStart.day());
                }
                if(sw.Forecast_End_Date__c != null){
                    date dtEnd = sw.Forecast_End_Date__c;
                    newcaseline.EndDate =  datetime.newInstance(dtEnd.year(), dtEnd.month(), dtEnd.day());
                } 
            }
            if(sw.SOI_Location__c != null){
                newcaseline.LocationId = sw.SOI_Location__c;
            }
            if(newcaseline.Id == null && workOrderId != null){
                wbsItems.add(new Sales_Order_Item__c (Id=sw.id, Case_Line_Status__c = 'Already planned'));
                woLILines.add(newcaseline);
            }
        }
        upsert wbsItems;
        return woLILines;
    }

    @AuraEnabled
    public static WBSWrapperResponse updateWorkOrderExit(Map<String,String> workOrderMap){
        String workOrderId = workOrderMap.get('Id');
        String locationId = workOrderMap.get('workOrderLocationId');
        String topLevelAsset = workOrderMap.get('topLevelWOId');
        String ownerId = workOrderMap.get('workOrderOwnerId');
        String installationManagerId = workOrderMap.get('workOrderIMId');
        String installationType = workOrderMap.get('workOrderIMId');
        String subject = workOrderMap.get('workOrderSubject');
        String internalComments = workOrderMap.get('internalComments');
        WorkOrder wo = [SELECT Id,Owner.Name,OwnerId, AccountId, Account.Name, LocationId,Location.Name, 
                               Top_Level_Asset__c, Top_Level_Asset__r.Name, Installation_Manager__c,Internal_Comments__c,
                               Installation_Manager__r.Name,Subject,Installation_Type__c 
                        FROM WorkOrder 
                        WHERE ID = :workOrderId LIMIT 1];
        if(locationId <> null && locationId <> '' && locationId <> wo.LocationId  ){
            wo.LocationId = locationId;
        }
        if(topLevelAsset <> null && topLevelAsset <> '' && topLevelAsset <> wo.Top_Level_Asset__c){
            wo.Top_Level_Asset__c = topLevelAsset;
        }
        if(installationManagerId <> null && installationManagerId <> '' 
            && installationManagerId <> wo.Installation_Manager__c){
            wo.Installation_Manager__c = installationManagerId;
        }
        if(ownerId <> null && ownerId <> '' && ownerId <> wo.OwnerId){
            wo.OwnerId = ownerId;
        }
        if(subject <> null && subject <> '' && subject <> wo.Subject ){
            wo.Subject = subject;
        }
        if(internalComments <> null && internalComments <> '' && internalComments <> wo.Internal_Comments__c ){
            wo.Internal_Comments__c = internalComments;
        }
        try{
            update wo;
            WBSWrapperResponse resp = VFSL_CreateInstallationPlanController.getWorkOrderDetailsUpdateInstallation(wo.Id);
            return resp;
        }
        catch(Exception ex){
            return null;
        }
    }

    @AuraEnabled
    public static WBSWrapperResponse updateWorkOrderLocation(Map<String,String> workOrderMap){
        List<WorkOrderLineItem> woLILines = new List<WorkOrderLineItem>();
        String workOrderId = workOrderMap.get('Id');
        String locationId = workOrderMap.get('workOrderLocationId');
        String topLevelAsset = workOrderMap.get('topLevelWOId');
        String ownerId = workOrderMap.get('workOrderOwnerId');
        String installationManagerId = workOrderMap.get('workOrderIMId');
        String installationType = workOrderMap.get('workOrderIMId');
        String subject = workOrderMap.get('workOrderSubject');
        String accountId = workOrderMap.get('workOrderAccountId');
        WorkOrder wo = [SELECT Id,Owner.Name,OwnerId, AccountId, Account.Name, LocationId,Location.Name, 
                               Top_Level_Asset__c, Top_Level_Asset__r.Name, Installation_Manager__c,
                               Installation_Manager__r.Name,Subject,Installation_Type__c 
                        FROM WorkOrder 
                        WHERE ID =:workOrderId Limit 1];
        if(locationId <> null && locationId <> '' && locationId <> wo.LocationId  ){
            wo.LocationId = locationId;
        }

        if(String.isNotBlank(AccountId)){
            wo.AccountId = accountId;
        }
        
        Id woLineRecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('Header');
        woLILines = [SELECT Id,StartDate,Change_Type__c, LineItemNumber,WorkOrderId, WorkOrder.WorkOrderNumber,WorkOrder.Status, 
                        Sales_Order_Item__c,Sales_Order_Item__r.NEW__c,Sales_Order_Item__r.Name,Sales_Order_Item__r.Product__c,
                        Sales_Order_Item__r.Product__r.Name,Sales_Order_Item__r.Product_Model__r.Name,
                        Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Requested_delivery_date__c,
                        AssetId,Asset.Name,LocationId,Location.Name,Product2Id,
                        Product2.Name,Asset_Name__c,SAP_WBS_Number__c,SAP_WBS__c, SAP_WBS__r.ERP_Project_Nbr__c 
                    FROM WorkOrderLineItem
                    WHERE WorkOrderId= :workOrderId AND RecordTypeId= :woLineRecordTypeId];
        for(WorkOrderLineItem woli:woLILines){
            woli.LocationId = wo.LocationId;
        }
        try{
            update wo;
            update woLILines;
            WBSWrapperResponse resp = VFSL_CreateInstallationPlanController.getWorkOrderDetailsUpdateInstallation(wo.Id);
            return resp;
        }
        catch(Exception ex){
            System.debug('Exception Message'+ex.getMessage());
            return null;
        }
    }

    public class WBSWrapperResponse {
        @AuraEnabled public String Id {get;set;}
        @AuraEnabled public String workOrderName {get;set;}
        @AuraEnabled public String locationName {get;set;}
        @AuraEnabled public String locationId {get;set;}
        @AuraEnabled public String accountName {get;set;}
        @AuraEnabled public String accountId {get;set;}
        @AuraEnabled public String  ownerName {get;set;}
        @AuraEnabled public String  ownerId {get;set;} 
        @AuraEnabled public String  InstallationManagerEmail {get;set;} // STRY0126923 - Added new attribute
        @AuraEnabled public String  InstallationManagerName {get;set;}
        @AuraEnabled public String  InstallationManagerId {get;set;}
        @AuraEnabled public String  TopLevelAssetName {get;set;}
        @AuraEnabled public String  TopLevelAssetId {get;set;}
        @AuraEnabled public String  internalComments {get;set;}
        @AuraEnabled public boolean  otherItemCategoriesPresent {get;set;}
        @AuraEnabled public boolean  alreadyPlannedItem {get;set;}
        @AuraEnabled public List<SelectedSalesOrderWrapper> selectedSOIList {get;set;}
        @AuraEnabled public String subject{get;set;}
        @AuraEnabled public String installationType{get;set;}
        @AuraEnabled public Date startDate{get;set;}
        @AuraEnabled public Date endDate{get;set;}
        @AuraEnabled public DateTime startDateUpdate{get;set;}
        @AuraEnabled public DateTime endDateUpdate{get;set;}
        //STRY0172857 -  Shubham Added below variables for PWO redesign
        @AuraEnabled public String projectType {get;set;}
        @AuraEnabled public String upgradeType {get;set;}
        @AuraEnabled public String fullscale {get;set;}
        @AuraEnabled public String fullscaleProjectType {get;set;}
        @AuraEnabled public String citrixEnvironment {get;set;}
        @AuraEnabled public String additionalProjectScope {get;set;}
        @AuraEnabled public Date dateShipRequestSubmitted {get;set;}
        @AuraEnabled public String projectSharePoint {get;set;}
        @AuraEnabled public Date kickoffDate {get;set;}
        @AuraEnabled public Date tboxUpgradeDate {get;set;}
        @AuraEnabled public Date prodGoLiveDate {get;set;}
        @AuraEnabled public String customerProfile {get;set;}
        @AuraEnabled public String currentVersion {get;set;}
        @AuraEnabled public String toVersion {get;set;}
        @AuraEnabled public String alternativesToSmartConnect {get;set;}
        @AuraEnabled public String customerContact {get;set;}
        //STRY0172857 - Shubham - changes end
    }

    public class WOLIWrapperResponse{
        @AuraEnabled public String  WorkOrderItemName {get;set;}
        @AuraEnabled public String  WorkOrderName {get;set;}
        @AuraEnabled public String  SalesOrderName {get;set;}
    }
    
     public class SOIItemsWrapper {
        public Sales_Order_Item__c SalesOrderItemRec{get;set;}
        public string ParentItemName{get;set;}
        public string AccountName{get;set;}
    } 

    public class SelectSalesOrderResponse{
        @AuraEnabled public String SOIEventName{get;set;}   //Added by Swati
        @AuraEnabled public String erpWBSName{get;set;}
        @AuraEnabled public String changeType{get;set;}
        @AuraEnabled public String erpMaterialNumber{get;set;}
        @AuraEnabled public String erpMaterialDescription{get;set;}
        @AuraEnabled public Date iStart{get;set;}
        @AuraEnabled public Date iFinish{get;set;}
        @AuraEnabled public Date acceptanceDate{get;set;}
        @AuraEnabled public String erpItemCategory{get;set;}
        @AuraEnabled public String salesOrderNumber{get;set;}
        @AuraEnabled public String parentItemName{get;set;}
        @AuraEnabled public String parentItemId{get;set;}
        @AuraEnabled public String accountName{get;set;}
        @AuraEnabled public String accountId{get;set;}
        @AuraEnabled public String salesItemNumber{get;set;}
        @AuraEnabled public String salesItemId{get;set;}
        @AuraEnabled public Boolean psAvailable{get;set;}
        @AuraEnabled public Decimal availQty{get;set;}
        @AuraEnabled public String lineStatus{get;set;}
        @AuraEnabled public String lineStatusColor{get;set;}
    }
    
   public static SelectSalesOrderResponseList generateSelectSalesOrderItemResponse(List<Sales_Order_Item__c> soiList){
        Set<Id> relatedWBSids = new Set<Id>();
        Set<Id> relatedSOIEVNTids = new Set<Id>();
        String stringToDate = '';
        SelectSalesOrderResponseList selectList = new SelectSalesOrderResponseList();
        List<SelectSalesOrderResponse> responseList = new List<SelectSalesOrderResponse>();
        List<SelectSalesOrderResponse> alreadyPlanningResponseList = new List<SelectSalesOrderResponse>();
        for(Sales_Order_Item__c soi : soiList){
            if(soi.ERP_WBS__r.size() > 0 ) {
                for(ERP_WBS__c erpwbs : soi.ERP_WBS__r){
                    relatedWBSids.add(erpwbs.id);
                }
            }
            /*Added by Swati
            if(soi.SOI_Event__r.size() > 0 ) {
                for(Soi_Event__c soiEvnt : soi.SOI_Event__r){
                    relatedSOIEVNTids.add(soiEvnt.id);
                }
            }Added by Swati*/
        }
        Map<Id,ERP_WBS__c> mapOFErpWBS = getWbsMap(relatedWBSids);
        //Map<Id,SOI_Event__c> mapOFSOIEvnt = getSoiEvntMap(relatedSOIEVNTids);  //Added by Swati
        for(Sales_Order_Item__c soi:soiList){
            SelectSalesOrderResponse resp = new SelectSalesOrderResponse();
            resp.erpWBSName = soi.WBS_Element__c;
            resp.changeType = soi.NEW__c;
            resp.erpMaterialNumber = soi.ERP_Material_Number__c;
            resp.erpMaterialDescription = soi.Material_Description__c;
            resp.erpItemCategory = soi.ERP_Item_Category__c;
            resp.salesOrderNumber = soi.Sales_Order__r.Name;
            resp.parentItemName = soi.Parent_Item__r.Name;
            resp.parentItemId = soi.Parent_Item__c <> null ? '/'+soi.Parent_Item__c : '';
            resp.accountName = soi.Site_Partner__r.Name;
            resp.accountId = soi.Site_Partner__c <> null ? '/'+soi.Site_Partner__c : '' ;
            resp.salesItemNumber = soi.Name;
            resp.availQty = soi.Quantity__c;
            resp.salesItemId = soi.Id <> null ? '/'+soi.Id:'';
            resp.lineStatus = soi.Status__c;    //STRY0449392 - Added lineStatus and lineStatusColor
            resp.lineStatusColor = soi.Reject_Reason__c=='V2-Clerical Cancellation' ? 'slds-text-color_error' : 'slds-text-color_success';
            if(soi.ERP_WBS__r.size() > 0 ) {
                for(ERP_WBS__c erpwbs : soi.ERP_WBS__r){
                    if(mapOFErpWBS != null && mapOFErpWBS.get(erpwbs.id) != null 
                        && mapOFErpWBS.get(erpwbs.id).erp_NWA__r.size() > 0){
                        resp.psAvailable = true;
                        resp.iStart = erpwbs.Forecast_Start_Date__c;
                        resp.iFinish = erpwbs.Forecast_End_Date__c;
                    }else{
                        resp.psAvailable = false;
                    }
                    resp.acceptanceDate = erpwbs.Acceptance_Date__c;
                     if(erpwbs.Acceptance_Date__c == null || (erpwbs.Acceptance_Date__c != null && allowAcceptanceDate)){
                        if(soi.Case_Line_Status__c == 'Available for planning' &&  
                            (soi.ERP_Item_Category__c == 'Z001' || soi.ERP_Item_Category__c != 'ZN01')){
                            responseList.add(resp);
                        }
                        if(soi.Case_Line_Status__c == 'Already planned'){
                            alreadyPlanningResponseList.add(resp);
                        }  
                    }
                }
            }else if(soi.SimpleSOI__c == true){ 
                resp.iStart = soi.Forecast_Start_Date__c;
                resp.iFinish = soi.Forecast_End_Date__c;
                resp.acceptanceDate = soi.Acceptance_Date__c;
                resp.psAvailable = true;
                if(soi.Acceptance_Date__c == null || (soi.Acceptance_Date__c != null && allowAcceptanceDate)){
                    if(soi.Case_Line_Status__c == 'Available for planning' 
                        && (soi.ERP_Item_Category__c == 'ZN01' || (soi.ERP_Item_Category__c == 'ZN18' && (soi.Material_Group__c == '000000000' || soi.Installation_Allocation_Quantity__c != null )))){
                        responseList.add(resp);
                    }
                    if(soi.Case_Line_Status__c == 'Already planned'){
                        alreadyPlanningResponseList.add(resp);
                    } 
                }
            }
          else{
                resp.psAvailable = false;
                if(soi.Case_Line_Status__c == 'Available for planning' &&  
                   (soi.ERP_Item_Category__c == 'Z001' || soi.ERP_Item_Category__c == 'ZN01' || soi.ERP_Item_Category__c == 'Z018' )){
                    responseList.add(resp);
                }
                if(soi.Case_Line_Status__c == 'Already planned'){
                    alreadyPlanningResponseList.add(resp);
                }
            }
        }
        selectList.availableForPlanning = responseList;
        selectList.alreadyPlanning = alreadyPlanningResponseList;
        return selectList;
    }

    @AuraEnabled(cacheable=false)
    public static List<SelectSalesOrderResponse> fetchAlreadyPlannedSOI(List<Sales_Order_Item__c> soiList){
        Set<Id> relatedWBSids = new Set<Id>();
        List<SelectSalesOrderResponse> responseList = new List<SelectSalesOrderResponse>();
        List<SelectSalesOrderResponse> alreadyPlannedResponseList = new List<SelectSalesOrderResponse>();
        for(Sales_Order_Item__c soi : soiList){
            if(soi.ERP_WBS__r.size() > 0 ) {
                for(ERP_WBS__c erpwbs : soi.ERP_WBS__r){
                    relatedWBSids.add(erpwbs.id);
                }
            }
        }
        Map<Id,ERP_WBS__c> mapOFErpWBS = getWbsMap(relatedWBSids);
        for(Sales_Order_Item__c soi:soiList){
            SelectSalesOrderResponse resp = new SelectSalesOrderResponse();
            resp.erpWBSName = soi.WBS_Element__c;
            resp.changeType = soi.NEW__c;
            resp.erpMaterialNumber = soi.ERP_Material_Number__c;
            resp.erpMaterialDescription = soi.Material_Description__c;
            resp.erpItemCategory = soi.ERP_Item_Category__c;
            resp.salesOrderNumber = soi.Sales_Order__r.Name;
            resp.parentItemName = soi.Parent_Item__r.Name;
            resp.parentItemId = soi.Parent_Item__c <> null ? '/'+soi.Parent_Item__c : '';
            resp.accountName = soi.Site_Partner__r.Name;
            resp.accountId = soi.Site_Partner__c <> null ? '/'+soi.Site_Partner__c : '' ;
            resp.salesItemNumber = soi.Name;
            resp.salesItemId = soi.Id <> null ? '/'+soi.Id:'';
            if(soi.ERP_WBS__r.size() > 0 ) {
                for(ERP_WBS__c erpwbs : soi.ERP_WBS__r){
                    if(mapOFErpWBS != null && mapOFErpWBS.get(erpwbs.id) != null && mapOFErpWBS.get(erpwbs.id).erp_NWA__r.size() > 0){
                        resp.psAvailable = true;
                        resp.iStart = erpwbs.Forecast_Start_Date__c;
                        resp.iFinish = erpwbs.Forecast_End_Date__c;
                    }else{
                        resp.psAvailable = false;
                    }
                    resp.acceptanceDate = erpwbs.Acceptance_Date__c;
                    if(erpwbs.Acceptance_Date__c == null || (erpwbs.Acceptance_Date__c != null && allowAcceptanceDate)){
                        responseList.add(resp);
                    }
                }
            }else{
                resp.psAvailable = false;
                responseList.add(resp);
            }
            if(soi.Case_Line_Status__c  == 'Already planned'){
                alreadyPlannedResponseList.add(resp);
            }
        }
        return alreadyPlannedResponseList;
    }
    
    @AuraEnabled(cacheable=true) 
    public static List<systemWrapper> systemValues(){   
        Map<String,String> uniquestring = new Map<String,String>();
        List<systemWrapper> wrapperResult = new List<systemWrapper>();
        List<Product_FRU__c > listProdPart = new List<Product_FRU__c >();
        listProdPart = [SELECT Id, Name,Top_Level__c,Top_Level__r.Name,Top_Level__r.VC_BOM_Level__c 
                        FROM Product_FRU__c WHERE Top_Level__r.VC_BOM_Level__c = '1' LIMIT 999];
        for(Product_FRU__c objPart : listProdPart) {
            uniquestring.put(objPart.Top_Level__r.Name,objPart.Top_Level__r.Name);
        }
        systemWrapper wrap1 = new systemWrapper();
        wrap1.label = '--None--'; 
        wrap1.value = 'None';
        wrapperResult.add(wrap1);
        for(String s:uniquestring.keyset()){
            systemWrapper wrap = new systemWrapper();
            wrap.label = uniquestring.get(s); 
            wrap.value = s;
            wrapperResult.add(wrap);
        } 
        return wrapperResult; 
    }

    public class systemWrapper{
        @AuraEnabled public String label {get;set;}
        @AuraEnabled public String value {get;set;}
    }
    
    @AuraEnabled(cacheable=true) 
    public static List<productWrapper> productValues(String selectedSystemValue){   
        Map<String,String> uniquestring = new Map<String,String>();
        List<productWrapper> wrapperResult = new List<productWrapper>();
        List<Product_FRU__c > listProdPart = new List<Product_FRU__c >();
        listProdPart = [SELECT Id,Name,Top_Level__c,Top_Level__r.Name,Part__c,Part__r.ProductCode,Part__r.Name 
                        FROM Product_FRU__c WHERE Top_Level__r.Name = :selectedSystemValue LIMIT 999];
        for(Product_FRU__c objPart : listProdPart) {
            uniquestring.put(objPart.Top_Level__r.Name,objPart.Top_Level__r.Name);
        }
        productWrapper wrap1 = new productWrapper();
        wrap1.label = '--None--'; 
        wrap1.value = 'None';
        wrapperResult.add(wrap1);
        for(String s:uniquestring.keyset()){
            productWrapper wrap = new productWrapper();
            wrap.label = uniquestring.get(s); 
            wrap.value = s;
            wrapperResult.add(wrap);
        } 
        return wrapperResult; 
    }

    public class productWrapper{
        @AuraEnabled public String label {get;set;}
        @AuraEnabled public String value {get;set;}
    }
    
    public static Asset createInstalledProduct(WorkOrderLineItem woli, Sales_Order_Item__c soi){
        list<schema.Location> loc = new list<schema.Location>();
        Asset asset = new Asset();
        asset.Sales_Order_Item__c = woli.Sales_Order_Item__r.Id;
        asset.LocationId= woli.LocationId;
        asset.Product2Id = woli.Part__c;
        asset.Sales_Order__c = woli.Sales_Order_Item__r.Sales_Order__c;
         //STRY0142834
        asset.SAP_Sales_Order_Item_Ref__c = woli.Sales_Order_Item__r.Sales_Order__r.Name + '-' + woli.Sales_Order_Item__r.Name;
        asset.SAP_Sales_Order__c = woli.Sales_Order_Item__r.Sales_Order__r.Name;
         //STRY0142834 end
        Id locId =  woli.LocationId;
        
        if(locId != null){
            loc = [SELECT Id,Name,SAP_Functional_Location__c,Plant__c,Account__c 
                   FROM Location WHERE Id = :locId LIMIT 1];
        }
        asset.SAP_Functional_Location__c = loc[0].SAP_Functional_Location__c;
        asset.AccountId = loc[0].Account__c;
        if(soi.ERP_Equipment_Number__c != NULL){
            asset.Name = soi.ERP_Equipment_Number__c;
        }else{
            asset.Name = soi.ERP_Reference__c;
        }
        asset.Status= 'Ordered';
        if(loc[0].Plant__c != null){
            asset.SAP_Plant__c = loc[0].Plant__c;   
        }
        return asset;
    }
    
    public static void updateInstalledProduct(WorkOrderLineItem woli, Sales_Order_Item__c soi, Id ip){
        Asset asset = new Asset();
        asset = [SELECT Id,Product2Id,Sales_Order__c,LocationId FROM Asset WHERE Id = :ip];
        asset.Product2Id = woli.Part__c;
        asset.ParentId = null;
        update asset; 
    }
    
    @AuraEnabled
    public static void changeUpdateExisting(String woliId,String locationId,String assetId){
        WorkOrderLineItem woli = [SELECT Id,AssetId,Part__c , Change_Type__c,Sales_Order_Item__c FROM WorkOrderLineItem WHERE Id = :woliId];
        Sales_Order_Item__c soi = [SELECT Id,Asset__c FROM Sales_Order_Item__c WHERE Id = :woli.Sales_Order_Item__c]; 
        Schema.Location loc = [SELECT Id FROM Location WHERE Id =: locationId];
        List<Asset> InstProd = [SELECT Id,Product2Id,RootAssetId,SAP_Reference__c FROM Asset WHERE Id = :woli.AssetId];
        if(!InstProd.isEmpty() && woli.Change_Type__c != 'Upgrade Existing'){
            if(InstProd[0].SAP_Reference__c== null){
                try{
                    /* STRY0124133 - Start - Remove placeholder reference from all the cases */
                    List<Case> caseList = [SELECT Id, AssetId, Asset_Top_Level__c FROM Case WHERE AssetId = :InstProd[0].Id OR Asset_Top_Level__c = :InstProd[0].Id];
                    if(caseList.size() > 0) {
                        for(Case cs:caseList) {
                            if(cs.AssetId == InstProd[0].Id) cs.AssetId = null;
                            if(cs.Asset_Top_Level__c == InstProd[0].Id) cs.Asset_Top_Level__c = null;
                        }
                        update caseList;
                    }
                    /* STRY0124133 - End - Remove placeholder reference from all the cases */
                    delete InstProd;
                }
                catch(Exception ex){
                    if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                        throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                    }
                    else{
                        throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                    }
                }
            }
        }
               
        woli.Change_Type__c = 'Upgrade Existing';
        woli.LocationId = loc.Id;
       
        if(assetId != null){
            woli.AssetId = assetId;
            List<Asset> objInstProd = [SELECT Id,Product2Id,RootAssetId,SAP_Reference__c 
                                        FROM Asset WHERE Id = :woli.AssetId];
            if(!objInstProd.isEmpty()){
                woli.Part__c =  objInstProd[0].Product2Id;
            }
            soi.Asset__c = assetId;
            setTopLevelExisting(woliId); 
        }else{
            woli.AssetId = null;
            woli.Part__c =  null;
            soi.Asset__c = null;
        }
        try{
            update woli;
            update soi;
            setTopLevelExisting(woliId); 
        }
        catch(Exception ex){
            if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
            }
            else{
                throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
            }
        }
    }
        
    @AuraEnabled
    public static void changeUpdateNew(String woliId,String locationId,String assetId,String productId){
        WorkOrderLineItem woli = [SELECT Id,Product2Id,Part__c,Sales_Order_Item__r.Sales_Order__c,
                                         WorkOrderId,WorkOrder.CaseId, WorkOrder.AssetId,
                                         WorkOrder.Case.AssetId,AssetId,Sales_Order_Item__c,
                                         Sales_Order_Item__r.Asset__c,Change_Type__c,
                                         Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Name
                                  FROM WorkOrderLineItem WHERE Id =: woliId];
        WorkOrder wo = [SELECT Id,AssetId 
                        FROM WorkOrder WHERE Id= :woli.WorkOrderId];
        Sales_Order_Item__c soi = [SELECT Id,Name,Asset__c,Asset__r.Name,ERP_Equipment_Number__c,ERP_Reference__c 
                                   FROM Sales_Order_Item__c WHERE Id = :woli.Sales_Order_Item__c];
        Asset asset = new Asset();
        woli.LocationId = locationId;
        woli.Part__c = productId;
        woli.Change_Type__c = 'New Child';
        //Chandra : Sep 2017 : STSK0012788 : Modified the below logic when the upgrade existing is a PCSN I/P and trying to change to new Child should create a new I/P. 
        if(soi.Asset__c == null || soi.Asset__r.Name != soi.ERP_Reference__c){
            try{
                asset = createInstalledProduct(woli,soi);
                asset.ParentId = assetId;
                asset.Product2Id = productId;
                insert asset;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }else{
            try{
                asset.Id = soi.Asset__c;
                asset.ParentId = assetId;
                asset.Product2Id = productId;
                update asset;    
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }
        
        if(woli.WorkOrder.AssetId == null){
            wo.AssetId = assetId;
            wo.Top_Level_Asset__c = assetId;
        }
        woli.AssetId = asset.Id;
        soi.Asset__c = asset.Id; 
        try{
            update wo;
            update woli;
            update soi;
        }
        catch(Exception ex){
            if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
            }else{
                throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
            }
        }
    }
    
    @AuraEnabled(cacheable=false)
    public static Boolean checkProductType(Id productId){
        list<Product2> p = [SELECT Id, Product_Type__c FROM Product2 WHERE Id =: productId];
        if(!p.isEmpty() && p[0].Product_Type__c != 'Model'){
            return false;
        }
        return true;
    } 
     
    @AuraEnabled
    public static void changeNew(String woliId,String locationId,String assetId,String productId){
        WorkOrderLineItem woli = [SELECT Id,WorkOrderId,Part__c,Sales_Order_Item__r.Sales_Order__c,
                                         WorkOrder.CaseId,WorkOrder.Case.AssetId,AssetId,
                                         Sales_Order_Item__c,Sales_Order_Item__r.Asset__c,Change_Type__c,
                                         Sales_Order_Item__r.Sales_Order__r.Name,Sales_Order_Item__r.Name
                                  FROM WorkOrderLineItem WHERE Id = :woliId];
        Sales_Order_Item__c soi = [SELECT Id,Asset__c,Asset__r.Name,ERP_Equipment_Number__c,ERP_Reference__c 
                                   FROM Sales_Order_Item__c WHERE Id = :woli.Sales_Order_Item__c];
        if(woli.Change_Type__c !='New Top Level'){
            woli.AssetId = null;
            soi.Asset__c = null;
            try{
                update woli;
                update soi;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }
        
        Asset asset = new Asset();
        woli.LocationId = locationId;
        woli.Part__c =  productId;
        woli.Change_Type__c = 'New Top Level';
        
        if((soi.Asset__c == null) ||(soi.Asset__r.Name != soi.ERP_Reference__c)){
            try{
                asset = createInstalledProduct(woli,soi);
                insert asset;
                woli.AssetId= asset.Id;
                update woli;
                soi.Asset__c = asset.Id;
                update soi;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }else{
            try{
                updateInstalledProduct(woli, soi,soi.Asset__c);
                woli.AssetId = soi.Asset__c;
                update woli;
                soi.Asset__c = soi.Asset__c;
                update soi;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }
        setTopLevelNew(woliId); 
    }
    
    private static void setTopLevelExisting(Id woliId){
        WorkOrderLineItem woli = [SELECT Id,WorkOrderId,AssetId FROM WorkOrderLineItem WHERE Id= :woliId];
        WorkOrder wo = [SELECT Id,AssetId,Top_Level_Asset__c FROM WorkOrder WHERE Id= :woli.WorkOrderId];
        List<Asset> ipList = [SELECT Id, RootAssetId FROM Asset WHERE Id = :woli.AssetId];
        if(ipList.size() > 0){
            Asset objInstProd = ipList[0];
            if(wo.AssetId == null){
                wo.AssetId = objInstProd.Id;
                if(objInstProd.RootAssetId == null){
                     wo.Top_Level_Asset__c = objInstProd.Id;
                }else{
                     wo.Top_Level_Asset__c = objInstProd.RootAssetId;
                }
            }
            try{
                update wo;
            }
            catch(Exception ex){
                if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                    throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                }else{
                    throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                }
            }
        }
    }
    
    @TestVisible
    private static void setTopLevelNew(Id woliId){
        WorkOrderLineItem woli = [SELECT Id,WorkOrderId,AssetId FROM WorkOrderLineItem WHERE Id= :woliId];
        WorkOrder wo = [SELECT Id,AssetId,Top_Level_Asset__c FROM WorkOrder WHERE Id= :woli.WorkOrderId];
        List<Asset> objInstProdList = [SELECT Id,Product2.Is_Top__c, RootAssetId FROM Asset WHERE Id= :woli.AssetId];
        if(!objInstProdList.isEmpty()){
            Asset objInstProd = objInstProdList[0];
            if(wo.AssetId == null){
                wo.AssetId = objInstProd.Id;
                if(objInstProd.RootAssetId== null){
                     wo.Top_Level_Asset__c = objInstProd.Id;
                }else{
                     wo.Top_Level_Asset__c = objInstProd.RootAssetId;
                }
                try{
                    update wo;
                }
                catch(Exception ex){
                    if(ex.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION,')){
                        throw new AuraHandledException(ex.getMessage().substringAfter('FIELD_CUSTOM_VALIDATION_EXCEPTION,'));
                    }else{
                        throw new AuraHandledException(ex.getMessage().substringAfter('first error:'));
                    }
                }
            }
        }
    }
    
    @AuraEnabled
    public static PrePopulateWrapperResponse prepopulate(String woliId,String changeType){
        List<Product2> productsChangeNew = new List<Product2>();
        PrePopulateWrapperResponse response = new PrePopulateWrapperResponse();
        Sales_Order_Item__c soi = new Sales_Order_Item__c();
        WorkOrderLineItem woli = [SELECT Id,AssetId,Asset.Name,LocationId,Location.Name,Product2Id,
                                         Product2.Name,WorkOrderId,WorkOrder.CaseId,WorkOrder.Case.AssetId,
                                         Sales_Order_Item__c,Sales_Order_Item__r.Asset__c,Change_Type__c 
                                  FROM WorkOrderLineItem WHERE Id = :woliId];
        try{
            soi = [SELECT Id,Product__c,ERP_Equipment_Number__c,ERP_Reference__c 
                   FROM Sales_Order_Item__c WHERE Id =: woli.Sales_Order_Item__c];
        }
        catch(QueryException ex){
            System.debug('******Exception Message**********'+ex.getMessage());
        }
      
        WorkOrder workOrder = [SELECT Id,Top_Level_Asset__c,Top_Level_Asset__r.Name 
                               FROM WorkOrder WHERE Id =: woli.WorkOrderId LIMIT 1];
        String productId = soi.Product__c;
        List<Product2> ProductModelSelected = new List<Product2>();
        productsChangeNew = [SELECT id,name,ProductCode,IsActive FROM Product2 
                             WHERE Id IN (SELECT Part__c FROM Product_FRU__c WHERE Top_Level__c =: soi.Product__c)];
        if(woli.Product2Id != null){
            response.productId = woli.Product2Id;
            response.productName = woli.Product2.Name;
        }else if(productsChangeNew.size() == 1){
            response.productId = productsChangeNew[0].id;
            response.productName = productsChangeNew[0].name;
        }else{
            response.productId = null;
            response.productName = null;
        } 
        response.locationId = woli.LocationId;
        response.locationName = woli.Location.Name;
        if(changeType == 'New Child'){
            response.assetId = workOrder.Top_Level_Asset__c;
            response.assetName = workOrder.Top_Level_Asset__r.Name;
        }
        
        return response;
    }

    @AuraEnabled 
    public static List<Product2> productLookupFilters(String woliId){
        WorkOrderLineItem woli = [SELECT Id,WorkOrderId,Sales_Order_Item__r.Sales_Order__c,
                                         WorkOrder.CaseId,WorkOrder.Case.AssetId,AssetId,
                                         Sales_Order_Item__c,Sales_Order_Item__r.Asset__c,Change_Type__c 
                                  FROM WorkOrderLineItem WHERE Id = :woliId];
        Sales_Order_Item__c soi = [SELECT Id,Product__c,Asset__c,ERP_Equipment_Number__c,ERP_Reference__c 
                                   FROM Sales_Order_Item__c WHERE Id = :woli.Sales_Order_Item__c];
        Set<Id> childprdpartid = new Set<Id>();
        for(Product_FRU__c productpart : [SELECT Part__c FROM Product_FRU__c WHERE Top_Level__c = :soi.Product__c]){
            childprdpartid.add(productpart.Part__c);
        }
        String soql = 'Select Id,Name,Product_Type__c ,ProductCode,IsActive from Product2 where Id in: childprdpartid AND Product_Type__c = \'Model\'';
        List<Product2> prodList =  (List<Product2>)database.query(soql);
        return database.query(soql); 
    }   
    
    @AuraEnabled
    public static List<Asset> assetLookupFilters(String locationId,String type){
        string locId = locationId;
        String query = 'Select Name,Product_Name__c, Parent_Asset_Text__c, Status, RootAssetId, SAP_Equipment_Description__c, InstallDate From Asset Where LocationId =:locId';
        
        List<Asset> ipList = (List<Asset>) database.query(query); 
        Map<String,List<Asset>> mapInstalledProduct = new Map<String,List<Asset>>();
        Map<String,Asset> mapTopInstalledProduct = new Map<String,Asset>();
        Map<String,List<Asset>> mapInstalledProdDummy = new Map<String,List<Asset>>();
        set<Id> topLevelIds = new set<Id>();
        List<Asset> assetList = new List<Asset>();
      
        for(Asset ip : ipList){
            if(ip.RootAssetId == null){
                mapTopInstalledProduct.put(ip.Id,ip);
            }else{
                topLevelIds.add(ip.RootAssetId);
            }
        }
        if(topLevelIds.size()>0){
            assetList = [SELECT Id,Name,Product_Name__c,Status FROM Asset WHERE Id IN :topLevelIds];
        }
        for(Asset ast: assetList){
            mapTopInstalledProduct.put(ast.Id, ast);
        }
        if(type == 'Top Level'){ 
            return mapTopInstalledProduct.values();
        }else{
            return ipList;
        }
    }
    
    public class PrepopulateWrapperResponse{
        @AuraEnabled public string assetId{get;set;}
        @AuraEnabled public string assetName{get;set;}
        @AuraEnabled public string productId{get;set;}
        @AuraEnabled public string productName{get;set;}
        @AuraEnabled public  string locationId{get;set;}
        @AuraEnabled public string locationName{get;set;}
    }

    public class SelectedSalesOrderWrapper{
        @AuraEnabled public String accountNumber{get;set;}
        @AuraEnabled public String salesOrderNumber {get;set;}
        @AuraEnabled public String salesOrderRecordId {get;set;}
        @AuraEnabled public String salesOrderItemNumber{get;set;}
        @AuraEnabled public String salesOrderItemRecordId{get;set;}
        @AuraEnabled public Date startDate{get;set;}
        @AuraEnabled public Date requested_Delivery_Date {get;set;}
        @AuraEnabled public String changeType{get;set;}
        @AuraEnabled public String erp_Material_Number {get;set;}
        @AuraEnabled public String material_Description {get;set;}
        @AuraEnabled public String erp_Item_Category{get;set;}
        @AuraEnabled public String erp_Sales_Order_Number{get;set;}
        @AuraEnabled public String parentItemNumber{get;set;}
        @AuraEnabled public String parentItemRecordId{get;set;}
        @AuraEnabled public String product{get;set;}
        @AuraEnabled public String productRecordId{get;set;}
    }

    public class WOLIWrapperResponseChangeType{
        @AuraEnabled public String Id {get;set;}
        @AuraEnabled public String woliId {get;set;}
        @AuraEnabled public String workOrderId {get;set;}
        @AuraEnabled public String lineItemNumber {get;set;}
        @AuraEnabled public String select_Change_Type {get;set;}
        @AuraEnabled public String line_Change_Type {get;set;}
        @AuraEnabled public String soi_Change_Type {get;set;}
        @AuraEnabled public String top_LevelId {get;set;}
        @AuraEnabled public String top_Level {get;set;}
        @AuraEnabled public String installed_Product {get;set;}
        @AuraEnabled public String installed_ProductId {get;set;}
        @AuraEnabled public String product_Model {get;set;}
        @AuraEnabled public String workOrderStatus {get;set;}
        @AuraEnabled public String location {get;set;}
        @AuraEnabled public String locationId {get;set;}
        @AuraEnabled public String sapFunctionalLoc {get;set;}
        @AuraEnabled public String erp_WBS_ElementId {get;set;}
        @AuraEnabled public String erp_WBS_Element {get;set;}
        @AuraEnabled public String soi_Product{get;set;}
        @AuraEnabled public String so_Item_Number{get;set;}
        @AuraEnabled public String so_Item_NumberId{get;set;}
        @AuraEnabled public String sales_Order {get;set;}
        @AuraEnabled public String sales_OrderId {get;set;}
        @AuraEnabled public DateTime start_Date {get;set;}
        @AuraEnabled public Date requested_Delivery_Date {get;set;}
        @AuraEnabled public String assetTooltip {get;set;}
        @AuraEnabled public String lineStatus{get;set;} //STRY0449392 - Added LineStatus
        @AuraEnabled public String lineStatusColor{get;set;}
    }

}
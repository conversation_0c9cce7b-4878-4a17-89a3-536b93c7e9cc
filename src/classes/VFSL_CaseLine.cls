/*
* Author : <PERSON><PERSON>
* Description :
*       This class contains logic that are executed when case line is inserted/updated.
Dipali C.         July 25, 2024            Refactor trigger framework 
*/
public with sharing class VFSL_CaseLine { 
    private static final String STATUS_ASSIGNED = 'Assigned';
    private static Id woliRecordTypeVISUsage = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('VIS_Usage');
    private static final Set<String> woRecordTypeSet = new Set<String>{'Helpdesk', 'Installation'};
    private static Map<Id, Case>  mapCaseId_Case = new Map<Id, Case>();
    private static Set<Id> setCaseId_all = new Set<Id>();
    private static list<ContractLineItem> lstCoveredProductData = new list<ContractLineItem>();
    private static set<id> intenalCaselineidlist = new set<id>();
    
    public static void prePoulateFldsOnCaseLine(list<Case_Line__c> lstCaseLine, map<id, Case_Line__c> oldMap){ 
        Set<Id> setCaseId_POno = new Set<Id>();
        Set<Id> setCaseId = new Set<Id>();
        Set<Id> setIPId = new Set<Id>();
        Set<Id> blankWOcaseIds  = new Set<Id>();
        Set<Case_Line__c> set_CaseLine = new set<Case_Line__c>();
        Map<Id, String> mapIPIDBillingType = new Map<Id, String>();
        Map<Id, Id> caseToWorkOrder = new Map<Id, Id>();
        
        for(Case_Line__c varCSL: lstCaseLine) {
            if((varCSL.Billing_Type__c != NULL && varCSL.Billing_Type__c.substring(0,1) == 'P')  && (oldMap .isEmpty() || 
                                                                                                     oldMap.get(varCSL.Id).Billing_Type__c == null ||
                                                                                                     oldMap.get(varCSL.Id).Billing_Type__c.substring(0,1) != 'P')) 
                setCaseId_POno.add(varCSL.Case__c);
            if(varCSL.Asset__c != null && (oldMap.isEmpty() || 
                                           ( varCSL.Asset__c != oldMap.get(varCSL.id).Asset__c))){
                                               set_CaseLine.add(varCSL);
                                               setIPId.add(varCSL.Asset__c);
                                               if(varCSL.Case__c != null) 
                                                   setCaseId.add(varCSL.Case__c);                                                      
                                           } 
            if(varCSL.Case__c !=null)
                setCaseId_all.add(varCSL.Case__c);
            if(oldMap.isEmpty() && varCSL.Work_Order__c == null && varCSL.ERP_NWA__c == null) 
                blankWOcaseIds.add(varCSL.Case__c);
        }
        
        //Getting Case
        if(setCaseId.size() > 0 || setCaseId_POno.size() > 0 || setCaseId_all.size() > 0) {
            mapCaseId_Case = new Map<Id, Case>([SELECT id,Location__c, AssetId, Status, P_O_Number__c,Asset.product_family__c  //STRY0496797 
                                                FROM case 
                                                WHERE id in:setCaseId OR 
                                                Id in :setCaseId_POno OR 
                                                ID IN: setCaseId_all]);
        }
        for(Case_Line__c varCaseLine: lstCaseLine) { 
            if (mapCaseId_Case.get(varCaseLine.Case__c).Status == 'Cancelled' && oldMap.isEmpty()) {
                varCaseLine.addError('Cannot create new Case Line on cancelled Case');
            }
            if(varCaseLine.Case__c != null && (VFSL_Utility.getRecordTypeMap().get('Case_Line__c').get('HelpDesk') == varCaseLine.recordtypeId ||
                                               VFSL_Utility.getRecordTypeMap().get('Case_Line__c').get('Installations') == varCaseLine.recordtypeId)) {
                                                   if(setCaseId.contains(varCaseLine.Case__c) && mapCaseId_Case.containskey(varCaseLine.Case__c)) {
                                                       if(varCaseLine.Asset__c != null) 
                                                           varCaseLine.Asset__c = mapCaseId_Case.get(varCaseLine.Case__c).AssetId;
                                                       
                                                       if(varCaseLine.Location__c != null) 
                                                           varCaseLine.Location__c = mapCaseId_Case.get(varCaseLine.Case__c).Location__c;
                                                   }
                                               }
            
            if(setCaseId_POno.contains(varCaseLine.Case__c) && mapCaseId_Case.get(varCaseLine.Case__c).P_O_Number__c != null) 
                varCaseLine.Purchase_Order__c = mapCaseId_Case.get(varCaseLine.Case__c).P_O_Number__c;
        }
        if(setIPId.size() > 0 && set_CaseLine.size() > 0) 
            methodScCaseLineTrigger(set_CaseLine, setIPId);
        if(!blankWOcaseIds.isEmpty()) {
            List<WorkOrder> pwoList = [SELECT Id, CaseId FROM WorkOrder 
                                       WHERE CaseId = :blankWOcaseIds AND ParentWorkOrderId != null];
            for (WorkOrder pwo : pwoList) 
                caseToWorkOrder.put(pwo.CaseId, pwo.Id);
            if(!caseToWorkOrder.isEmpty()) {
                for(Case_Line__c cLine : lstCaseLine) {
                    if (cLine.Work_Order__c == null && caseToWorkOrder.containsKey(cLine.Case__c)) 
                        cLine.Work_Order__c = caseToWorkOrder.get(cLine.Case__c);
                }
            }
        } 
        validateOverLap(lstCaseLine, oldMap);
    }
    
    //Parameterized Method called through ScCaseLineTrigger Trigger
    public static void methodScCaseLineTrigger(set<Case_Line__c> set_CaseLine,set<id> setIPId) {
        Map<Id, String> mapIPIDBillingType = new Map<Id, String>();
        Set<Id> setTopIPId = new Set<Id>();
        if(setIPId.size()>0) {
            //STRY0449325: Dipali: added logic to consider parent billing type for subs with contract not eligible : Start
            Map<Id,Asset> astMap = new Map<Id,Asset>([Select id,ParentId,Parent.Service_Contract__c,Parent.Service_Contract__r.SAP_Contract_Type__c, 
                                   ContractEligible__c from Asset where Id in:setIPId]);
            for(Asset ip : astMap.values()){
                if(!ip.ContractEligible__c){
                    setTopIPId.add(ip.ParentId);
                }
            }
            lstCoveredProductData = [SELECT id, StartDate, EndDate, ServiceContractId, AssetId, 
                                     Asset.Service_Contract__r.SAP_Contract_Type__c 
                                     FROM ContractLineItem 
                                     WHERE ServiceContractId != null and (AssetId in:setIPId OR AssetId in:setTopIPId)];
            
            //Validate if Covered PRoduct Exists based on filter criteria
            if(lstCoveredProductData.size() > 0) {
                //Iterate Covered Product data
                for(ContractLineItem objCoveredProduct : lstCoveredProductData) {
                    //Validate if Contract is active on current date
                    if(objCoveredProduct.StartDate <= system.today() && objCoveredProduct.EndDate >= system.today()) {
                        if(mapIPIDBillingType.keyset().isEmpty() && !mapIPIDBillingType.containsKey(objCoveredProduct.AssetId)) {
                            if (objCoveredProduct.Asset.Service_Contract__r.SAP_Contract_Type__c == 'ZH3')
                                mapIPIDBillingType.put(objCoveredProduct.AssetId, 'W - Warranty');
                            else
                                mapIPIDBillingType.put(objCoveredProduct.AssetId, 'C - Contract');
                        }
                    }
                }
            }
            //STRY0449325: Dipali: added logic to consider parent billing type for subs with contract not eligible : End
            //Iterate Installed PRoduct Ids
            if(setIPId.size() > 0) {
                for(id idIP : setIPId) {
                    if(mapIPIDBillingType.keyset().isEmpty()  && !mapIPIDBillingType.containsKey(idIP))
                        mapIPIDBillingType.put(idIP,'P – Paid Service');
                }
            }
            
            if(mapIPIDBillingType.keyset().size() > 0) {
                //Iterate the filtered Case lines
                for(Case_Line__c objCaseLine : set_CaseLine) {
                   //STRY0449325: Dipali: added condition to consider parent billing type for subs with contract not eligible
                    if(objCaseLine.Billing_Type__c == null && !astMap.get(objCaseLine.Asset__c).ContractEligible__c 
                       && mapIPIDBillingType.containsKey(astMap.get(objCaseLine.Asset__c).ParentId)){
                           objCaseLine.Billing_Type__c  = mapIPIDBillingType.get(astMap.get(objCaseLine.Asset__c).ParentId);
                    }else if(objCaseLine.Billing_Type__c == null && mapIPIDBillingType.containsKey(objCaseLine.Asset__c))
                           objCaseLine.Billing_Type__c  = mapIPIDBillingType.get(objCaseLine.Asset__c);
                    if(objCaseLine.Employee__c == null)
                        objCaseLine.Employee__c = UserInfo.getUserId();
                }
            }
        }
    }
    
    // Check Overlapping 
    public static void validateOverLap(list<Case_Line__c> caseLineList, map<Id, Case_Line__c> caseLineOldMap) {
        List<Case_Line__c> LstCL = new list<Case_Line__c>();
        for(Case_Line__c cl : caseLineList){
            if(cl.Start_Date_time__c != null && cl.End_date_time__c != null && (caseLineOldMap.isEmpty() || 
                                                                                caseLineOldMap.get(cl.id).Start_Date_time__c == null || 
                                                                                caseLineOldMap.get(cl.id).End_date_time__c == null || 
                                                                                caseLineOldMap.get(cl.id).Start_Date_time__c != cl.Start_Date_time__c || 
                                                                                caseLineOldMap.get(cl.id).End_date_time__c != cl.End_date_time__c)){
                                                                                    LstCL.add(cl); 
                                                                                }
        }
        
        if(LstCL.size() > 0){
            checkForCaseLineOverlap(LstCL, caseLineOldMap);
        }           
    }
    
    public static void checkForCaseLineOverlap(List<Case_Line__c> clList, Map<Id, Case_Line__c> oldMap) { 
        Map<Integer, List<String>> errorMap = new Map<Integer, List<String>>();
        Set<Id> setEmployeeId = new Set<Id>();
        Set<Id> oldCLIdSet = new Set<Id>();
        Set<Id> caseids = new Set<Id>();
        if(!oldMap.isEmpty()) {
            for(Case_Line__c caseline : oldMap.values()){
                oldCLIdSet.add(caseline.Id);
            } 
        }
        Datetime mindate = system.now();
        for(Case_Line__c caseline : clList) {
            if(caseline.Start_Date_time__c < mindate)
                mindate = caseline.Start_Date_time__c;
            
            if(caseline.Employee__c != null)
                setEmployeeId.add(caseline.Employee__c);
            
            if(caseline.Case__c != null) 
                caseids.add(caseline.Case__c);
        }
        
        Map<id,Id> case2recordtype = new map<id,Id>();
        for (case c : [select id, RecordtypeId from Case where id in :caseids]) {
            case2recordtype.put(c.id, c.RecordtypeId);
        }
        
        mindate = mindate - 1;
        List<Case_Line__c> otherCLList = new List<Case_Line__c>();
        if(setEmployeeId.size() > 0) {
            otherCLList = [SELECT Id, Name, Start_Date_time__c, Employee__c, End_date_time__c, 
                           Case__r.RecordtypeId, Case__r.CaseNumber 
                           FROM Case_Line__c 
                           WHERE Employee__c IN : setEmployeeId 
                           AND (Start_Date_time__c >: mindate OR End_date_time__c >: mindate)
                           AND Id NOT IN : oldCLIdSet ORDER BY Start_Date_time__c ASC];
        }
        
        for(Integer i = 0; i < clList.size(); ++i) {
            Case_Line__c caseline = clList.get(i);
            if(!errorMap.containsKey(i)) {
                errorMap.put(i, new List<String>());
            }
            for(Case_Line__c othercaseline : clList) {
                if((case2recordtype.size() > 0 && case2recordtype.containsKey(caseline.Case__c) && 
                    case2recordtype.get(caseline.Case__c) != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')) || 
                   othercaseline.Case__r.RecordtypeId != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')) 
                    continue; 
                
                if(caseline != othercaseline && caseline.Employee__c == othercaseline.Employee__c &&
                   othercaseline.End_date_time__c != null && othercaseline.Start_Date_time__c != null && 
                   caseline.Start_Date_time__c != null && caseline.End_date_time__c != null //&& 
                   /*caseline.Case__r.RecordtypeId != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')*/) {
                       
                       if(caseline.Start_Date_time__c < othercaseline.End_date_time__c && othercaseline.Start_Date_time__c < caseline.End_date_time__c) {
                           if(!errorMap.isempty() && errorMap.containsKey(i) &&  errorMap.get(i) != Null &&  othercaseline.Start_Date_time__c != Null) {
                               errorMap.get(i).add(+ ' (' + caseline.Start_Date_time__c.format('hh:mma') 
                                                   + ' - ' + (caseline.End_date_time__c != null ? caseline.End_date_time__c .format('hh:mma') : '') + ')'
                                                   + ' overlaps with existing caseline' 
                                                   + othercaseline.Name
                                                   + ' (' + othercaseline.Start_Date_time__c.format('hh:mma') 
                                                   + ' - ' + (othercaseline.End_date_time__c != null ? othercaseline.End_date_time__c .format('hh:mma') : '') + ')'
                                                   + ' on case '
                                                   + othercaseline.Case__r.CaseNumber);
                           }
                       }
                   }
            }
            
            for(Case_Line__c  othercaseline : otherCLList) {
                if((case2recordtype.size() > 0 && case2recordtype.containsKey(caseline.Case__c) && 
                    case2recordtype.get(caseline.Case__c) != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')) || 
                   othercaseline.Case__r.RecordtypeId != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')) 
                    continue; 
                
                if((caseline != othercaseline) && (caseline.Employee__c == othercaseline.Employee__c )) {
                    if((othercaseline.End_date_time__c == null || 
                        (caseline.Start_Date_time__c < othercaseline.Start_Date_time__c && caseline.End_date_time__c > othercaseline.Start_Date_time__c) || 
                        (caseline.Start_Date_time__c > othercaseline.Start_Date_time__c && caseline.End_date_time__c < othercaseline.End_date_time__c) ||
                        (caseline.Start_Date_time__c < othercaseline.End_date_time__c && caseline.End_date_time__c > othercaseline.End_date_time__c) || 
                        (caseline.Start_Date_time__c == othercaseline.Start_Date_time__c && caseline.End_date_time__c == othercaseline.End_date_time__c) || 
                        (caseline.Start_Date_time__c > othercaseline.Start_Date_time__c && caseline.Start_Date_time__c < othercaseline.End_date_time__c) ||
                        (caseline.End_date_time__c > othercaseline.Start_Date_time__c && caseline.End_date_time__c < othercaseline.End_date_time__c)  || 
                        (othercaseline.Start_Date_time__c > caseline.Start_Date_time__c && othercaseline.Start_Date_time__c < caseline.End_date_time__c) || 
                        (othercaseline.End_date_time__c > caseline.Start_Date_time__c && othercaseline.End_date_time__c < caseline.End_date_time__c)) //&& 
                       /*othercaseline.Case__r.RecordtypeId != VFSL_Utility.getRecordTypeMap().get('Case').get('Helpdesk')*/) {
                           if(!errorMap.isempty() && errorMap.containsKey(i) &&  errorMap.get(i) != Null  && othercaseline.Start_Date_time__c != Null ) {
                               errorMap.get(i).add(+ ' (' + caseline.Start_Date_time__c.format('hh:mma') 
                                                   + ' - ' + (caseline.End_date_time__c != null ? caseline.End_date_time__c.format('hh:mma') : '') + ')'
                                                   + ' overlaps with existing caseline ' 
                                                   + othercaseline.Name
                                                   + ' (' + othercaseline.Start_Date_time__c.format('hh:mma') 
                                                   + ' - ' + (othercaseline.End_date_time__c != null ? othercaseline.End_date_time__c.format('hh:mma') : '') + ')'
                                                   + ' on case '
                                                   + othercaseline.Case__r.CaseNumber);
                           }
                       }
                }
            }
        }
    }
    // Create work order and woli
    public static void createWorkOrderandWOLI(list<Case_Line__c> caseLineList) {
        set<Id> caseIds = new set<Id>();
        //set<Id> hdCaseNWAIds = new set<Id>(); // HD Case NWAIds
        //set<Id> hdCaseIds = new set<Id>(); // HD Case Ids
        set<Id> caseLineOwner = new set<Id>();
        map<Id, ServiceResource> technicianMap = new map<Id, ServiceResource>();
        list<WorkOrder> workOrderListToInsert = new list<WorkOrder>();
        map<Id,WorkOrder> caseLineWorkOrderMap = new map<Id,WorkOrder>();
        map<Id,WorkOrder> hdcaseLineWorkOrderMap = new map<Id,WorkOrder>();
        map<Id, Case> caseMap;
        map<Id, Case> hdCaseMap;
        set<id> pwoids = new set<id>();
        List<WorkOrderLineItem> wdls2insert = new List<WorkOrderLineItem>();
        //Set<Id> nwaIdSet = new Set<id>(); //Dipali - not used anywhere in code
        
        for(Case_Line__c caseLine : caseLineList) {
            if(caseLine.Case_Line_WO_Type__c == 'Helpdesk' || caseLine.Case_Line_WO_Type__c == 'Installation') {
                if(caseLine.recordtypeid == VFSL_Utility.getRecordTypeMap().get('Case_Line__c').get('HelpDesk') 
                   && caseLine.Case_Line_WO_Type__c == 'Helpdesk' &&  
                   caseLine.Case__c != null && caseLine.End_date_time__c != null && 
                   //caseLine.Start_Date_time__c != null && stDate.daysBetween(edDate) == 0 &&  
                   caseLine.End_date_time__c > caseLine.Start_Date_time__c) { 
                       caseIds.add(caseLine.Case__c);
                       caseLineOwner.add(caseLine.Employee__c);
                   } else if((caseline.ERP_NWA__c != null || caseline.Sales_Order__c != null)  && 
                             caseLine.Case_Line_WO_Type__c == 'Installation') { //do not need these hd sets so probably can remove this.
                                 caseIds.add(caseLine.Case__c);
                                 caseLineOwner.add(caseLine.Employee__c); // populating caseOwner for case_line rec type to be used in technicianMap
                             }
            }
        }
        if(!caseIds.isEmpty() && !caseLineOwner.isEmpty()) {
            Map<Id,ServiceResource> userSRMap = new  Map<Id,ServiceResource>();
            userSRMap = getTechnician(caseLineOwner);
            if(!userSRMap.isEmpty()){
                for(ServiceResource sr : userSRMap.values())
                    technicianMap.put(sr.RelatedRecordId,sr);
                caseMap = getCases(caseIds, userSRMap.keySet());
            }
            //Create Work Order
            for(Case_Line__c caseLine : caseLineList) {
                boolean newSO = false;
                List<WorkOrder> listOpenWO = new List<WorkOrder>();
                Case c = caseMap.get(caseLine.Case__c);
                ServiceResource technician =  technicianMap.get(caseLine.Employee__c);
                if(caseLine.ERP_NWA__c == null && caseLine.Sales_Order__c != null 
                   && caseLine.Sales_Order_Item__c != null){
                       newSO = true;
                   }
                //Need to get rid of Cancelled/Closed/Submitted/etc. Work Orders so that the below logic will work and not create duplicates.  
                for(WorkOrder checkWO : c.WorkOrders){
                    if(checkWO.Status == 'Assigned' || checkWO.Status == 'New'){
                        listOpenWO.add(checkWO);
                    }
                }
                if(c != null && listOpenWO.size() == 0) {
                    //create work order
                    WorkOrder tempWO = createWorkOrder(c,caseLine,technician);
                    // STRY0163562:: Start
                    if(c.Account.Account_Type__c == 'Internal'){
                        tempWO.Interface_Status__c ='Do Not Process';
                        intenalCaselineidlist.add(caseLine.id);
                    }
                    //STRY0163562:End
                    workOrderListToInsert.add(tempWO);
                    caseLineWorkOrderMap.put(caseLine.Id,tempWO);
                } else if(c != null && !c.WorkOrders.isEmpty()) {
                    /* Start - STRY0141548 - To avoid duplicate HD WO creation */
                    Boolean isTopLevelChanged = true;
                    for(WorkOrder wo : listOpenWO) {
                        if(c.AssetId != wo.AssetId && c.Asset_Top_Level__c == wo.Top_Level_Asset__c){                                
                            wo.Top_Level_Asset__c = c.Asset_Top_Level__c;
                            wo.AssetId = c.AssetId;
                            workOrderListToInsert.add(wo);
                            caseLineWorkOrderMap.put(caseLine.Id,wo);
                            isTopLevelChanged = false;
                        } else if(c.Asset_Top_Level__c == wo.Top_Level_Asset__c){
                            caseLineWorkOrderMap.put(caseLine.Id,wo);
                            isTopLevelChanged = false;
                        }
                    }
                    if(isTopLevelChanged){
                        WorkOrder tempWO = createWorkOrder(c,caseLine,technician);
                        if(c.Account.Account_Type__c == 'Internal'){
                            tempWO.Interface_Status__c ='Do Not Process';
                            intenalCaselineidlist.add(caseLine.id);
                        }						
                        workOrderListToInsert.add(tempWO);
                        caseLineWorkOrderMap.put(caseLine.Id,tempWO);
                    }
                    /* End - STRY0141548 - To avoid duplicate HD WO creation */
                }
            }
        }
        
        if(!workOrderListToInsert.isEmpty()) {
            upsert workOrderListToInsert;
        }
        //create work detail.
        if(!caseLineWorkOrderMap.isEmpty())
            createWorkOrderLineItems(caseLineList,technicianMap,caseLineWorkOrderMap,caseMap);
    }
    
    //Create work order instance
    private static WorkOrder createWorkOrder(Case caseObj,  Case_Line__c caseLine, ServiceResource technician){
        WorkOrder ObjWorkOrderTemp = new WorkOrder();
        Set<Id> CLID = new Set<id>();
        // condition to set IWO record type to Installation
        if(!caseline.Billing_Type__c.contains(Label.I_Installation)){
            ObjWorkOrderTemp.recordtypeid = Schema.SObjectType.WorkOrder.getRecordTypeInfosByName().get('Helpdesk').getRecordTypeId();
        }
        //Adjust to add Case Line check so that SaaS will branch to Helpdesk instead.
        if(caseObj.recordtypeid == Schema.SObjectType.Case.getRecordTypeInfosByName().get('INST').getRecordTypeId()){
            if(caseline.Case_Line_WO_Type__c == 'Installation'){
                ObjWorkOrderTemp.recordtypeid = Schema.SObjectType.WorkOrder.getRecordTypeInfosByName().get('Installation').getRecordTypeId();
                ObjWorkOrderTemp.Status = 'Assigned';
                //ObjWorkOrderTemp.Interface_Status__c = 'Process'; // STRY0179378 - Commented
                ObjWorkOrderTemp.Install_Case_Time__c = true;
            }else{
                //fill in other SaaS only features for Work Order
                //ObjWorkOrderTemp.Install_Case_Time__c = true;
                ObjWorkOrderTemp.SaasInstall__c = true;
            }
        } 
        //STRY0163562 --Start
        if(caseObj.Account.Account_Type__c == 'Internal'){
            ObjWorkOrderTemp.Interface_Status__c ='Do Not Process';
            intenalCaselineidlist.add(caseLine.id);
        }
        //STRY0163562--END
        
        //Start:STRY0151759
        if(caseline.ERP_Project_Number__c != null && caseLine.Case_Line_WO_Type__c == 'Installation') { 
            ObjWorkOrderTemp.SAP_Project_Text__c= caseline.ERP_Project_Number__c;
        }
        //End:STRY0151759
        //Link to PWO for roll-up calcs 
        ObjWorkOrderTemp.ParentWorkOrderId = caseObj.Install_PWO__c; // STRY0179378 - Link Parent WO to Child WO
        ObjWorkOrderTemp.Status = STATUS_ASSIGNED;
        ObjWorkOrderTemp.Service_Resource__c = technician.Id;
        ObjWorkOrderTemp.Service_Resource_Email__c = technician.RelatedRecord.Email;
        ObjWorkOrderTemp.ServiceTerritoryId = caseObj.Asset.Service_Territory__c;
        ObjWorkOrderTemp.OwnerId = technician.RelatedRecordId;
        ObjWorkOrderTemp.AccountId = caseObj.AccountId;
        ObjWorkOrderTemp.Top_Level_Asset__c = caseObj.Asset_Top_Level__c;
        ObjWorkOrderTemp.ContactId = caseObj.Contactid;
        ObjWorkOrderTemp.Priority = caseObj.Priority;
        ObjWorkOrderTemp.Description = caseObj.Description;
        ObjWorkOrderTemp.LocationId = caseObj.Location__c;
        ObjWorkOrderTemp.Subject = caseObj.Subject;
        ObjWorkOrderTemp.AssetId = caseObj.AssetId;
        ObjWorkOrderTemp.CaseId = caseObj.id;  
        ObjWorkOrderTemp.Customer_Malfunction_Start__c = caseObj.Customer_Malfunction_Start_Date_time_Str__c;
        ObjWorkOrderTemp.Customer_Requested_Start__c = caseObj.Customer_Requested_Start_Date_Time__c;
        ObjWorkOrderTemp.Purpose_of_Visit__c = caseObj.Reason;
        ObjWorkOrderTemp.Acceptance_Date__c = caseObj.Malfunction_Start__c;
        ObjWorkOrderTemp.Malfunction_Start__c = caseObj.Malfunction_Start__c;
        //STSK0023886 - Fill dates for SaaSInstall if not filled as Install Case doesn't account for Preferred Start/End.
        ObjWorkOrderTemp.StartDate = caseObj.Preferred_Start_Time__c != null ? caseObj.Preferred_Start_Time__c : ObjWorkOrderTemp.Malfunction_Start__c;
        ObjWorkOrderTemp.EndDate = caseObj.Preferred_End_Time__c != null ? caseObj.Preferred_End_Time__c : ObjWorkOrderTemp.StartDate + 7;
        ObjWorkOrderTemp.Sales_Order__c = caseLine.Sales_Order__c; //Q2C: Dipali : Assign SO New SO
        
        if (caseline != null && caseline.Billing_Type__c == 'I – Installation') {
            ObjWorkOrderTemp.ERP_WBS__c = caseline.ERP_WBS__c;
        }
        return ObjWorkOrderTemp;
    }
    
    // Create WOLIs
    public static void  createWorkOrderLineItems(list<Case_Line__c> caseLineList,  map<Id, ServiceResource> technicianMap, map<Id, WorkOrder> caseLineWorkOrderMap, map<Id, Case> caseMap) { 
        map<Id,list<WorkOrderLineItem>> mapWorkOrderID_ProdServWD =  VFSL_CaseLine.getProductServicedWDLS(caseLineWorkOrderMap.values());
        list<WorkOrderLineItem> wdlList = new list<WorkOrderLineItem> ();
        for(Case_Line__c caseLine : caseLineList){
            Case c = caseMap.get(caseLine.Case__c);
            ServiceResource technician =  technicianMap.get(caseLine.Employee__c);
            WorkOrder wo = caseLineWorkOrderMap.get(caseLine.Id);
            if(c != null && technician != null && wo != null ){
                list<WorkOrderLineItem> psWDLList= (mapWorkOrderID_ProdServWD.get(wo.Id)!=null)?mapWorkOrderID_ProdServWD.get(wo.Id):new list<WorkOrderLineItem>();
                WorkOrderLineItem wdlTemp = createWOLI(c, caseLine, technician, psWDLList,wo);                  
                wdlList.add(wdlTemp);
            }
        }
        if(!wdlList.isEmpty()){
            insert wdlList;
        }
    }
    
    // Create single WOLI Instance
    public static WorkOrderLineItem createWOLI(Case caseObj, Case_Line__c caseLine, ServiceResource technician, list<WorkOrderLineItem> psWDLList, WorkOrder wo){
        System.debug('Inside createWOLI=====');
        WorkOrderLineItem objWorkDetailTemp = new WorkOrderLineItem();
        objWorkDetailTemp.WorkOrderId = wo.Id;
        objWorkDetailTemp.Service_Resource__c = technician.Id;
        
        if(!psWDLList.isEmpty()) {
            for(WorkOrderLineItem varWD : psWDLList) {
                if(varWD.AssetId == caseLine.Asset__c)
                    objWorkDetailTemp.ParentWorkOrderLineItemId = varWD.ID;
            }
        }
        //STRY0163562--Start 
        if(wo.Interface_Status__c == 'Do Not Process'){
            objWorkDetailTemp.Interface_Status__c ='Do Not Process';
        }
        //STRY0163562--End
        objWorkDetailTemp.SAP_Service_Order__c = caseLine.SAP_Service_Order__c;
        objWorkDetailTemp.Assistance_Request__c = caseLine.Assistance_Request__c; // STRY0194577 : AR: Provide ability to Track the Labor Activity that is submitted from the AR Case Log  and  rollup time by AR
        objWorkDetailTemp.Billing_Review_Requested__c = caseLine.Billing_Review_Requested__c;
        objWorkDetailTemp.Reason_for_Billing_Review__c = caseLine.Billing_Review_Reason__c;
        objWorkDetailTemp.Type__c = 'Labor';
        objWorkDetailTemp.AssetId = caseLine.Asset__c;
        objWorkDetailTemp.Case_Line__c = caseLine.Id;
        objWorkDetailTemp.Status = caseLine.Status__c;
        objWorkDetailTemp.Part__c = caseLine.Product__c;
        if(mapCaseId_Case.containsKey(caseLine.Case__c) && mapCaseId_Case.get(caseLine.Case__c).Asset.Product_Family__c == 'Proton') //STRY0496797
          objWorkDetailTemp.Billing_Type__c = caseLine.Billing_Type__c; 
        objWorkDetailTemp.Activity_Type__c = 'Helpdesk';
        objWorkDetailTemp.EndDate = caseLine.End_date_time__c;
        objWorkDetailTemp.Method__c = caseLine.method__c; //STRY0162785
        objWorkDetailTemp.StartDate = caseLine.Start_Date_time__c;
        objWorkDetailTemp.SaaSType__c = caseLine.SaaSType__c;
        objWorkDetailTemp.RecordTypeId = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('HD_Usage');
        
        if(caseObj.recordtypeid == Schema.SObjectType.Case.getRecordTypeInfosByName().get('INST').getRecordTypeId()){ 
            //add check for SaaS Install
            if(!caseObj.SaaSInstall__c){
                objWorkDetailTemp.recordtypeid = VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('VIS_Usage');
                objWorkDetailTemp.Activity_Group__c = 'Installation';
                objWorkDetailTemp.Activity_Type__c = 'Installation';
                objWorkDetailTemp.Description = 'Helpdesk Support for Installation Agent';
            }else if(caseObj.SaaSInstall__c){
                objWorkDetailTemp.Activity_Group__c = 'Installation';
                objWorkDetailTemp.Activity_Type__c = 'Installation';
                objWorkDetailTemp.Description = 'SaaS Implementation';
            }
        }
        if(caseline.ERP_NWA__c != null){
            objWorkDetailTemp.SAP_NWA__c = caseline.ERP_NWA__c;
            objWorkDetailTemp.SAP_WBS__c = caseline.ERP_WBS__c;
        }
        if(caseline.Sales_Order__c != null){ //Q2C: Dipali : Assign SO and SOI for New SO
            objWorkDetailTemp.Sales_Order__c = caseline.Sales_Order__c;
            objWorkDetailTemp.Sales_Order_Item__c = caseline.Sales_Order_Item__c;
        }
        if(caseline.Billing_Type__c != null && caseLine.Billing_Type__c.substring(0,1) == 'I') {
            objWorkDetailTemp.RecordTypeId = woliRecordTypeVISUsage;
            objWorkDetailTemp.Method__c = 'By Phone';
            objWorkDetailTemp.Activity_Group__c = 'Installation';
            objWorkDetailTemp.Activity_Type__c = 'Installation';
            objWorkDetailTemp.Description = 'Helpdesk Support for Installation Agent';
        }
        return objWorkDetailTemp;
    }
    
    private static map<Id, ServiceResource> getTechnician(set<Id> userIds){
        Map<Id,ServiceResource> technicianMap;
        If(userIds.size() > 0){
            technicianMap =  new Map<Id,ServiceResource>([Select Id, RelatedRecordId, RelatedRecord.Email, RecordTypeId 
                                                          From ServiceResource 
                                                          Where RelatedRecordId IN :userIds AND 
                                                          RecordTypeId =: VFSL_Utility.getRecordTypeMap().get('ServiceResource').get('Technician') ]);
            
        }
        return technicianMap;   
    }
    
    public static map<Id,list<WorkOrderLineItem>> getProductServicedWDLS(list<WorkOrder> woList){
        map<Id, list<WorkOrderLineItem>> mapWorkOrderID_ProdServWD = new map<Id,list<WorkOrderLineItem>>();
        Map<Id, WorkOrder> woListMap = new Map<Id, WorkOrder>(woList);
		//STRY0563151 - Inside the SOQL: convert "woList" into "woListMap.keySet()"
        if(!woListMap.keySet().isEmpty()){
        for(WorkOrderLineItem varWD: [SELECT ID, WorkOrderId, AssetId, Work_Order_Record_Type__c, 
                                      Contract_Number__c, Malfunction_Start_Date__c 
                                      FROM WorkOrderLineItem 
                                      WHERE WorkOrderId in : woListMap.keySet() and RecordType.Name='Header']){ //STRY0563151
                                          List<WorkOrderLineItem> tempListWD = new List<WorkOrderLineItem>();
                                          if(mapWorkOrderID_ProdServWD.containsKey(varWD.WorkOrderId)){
                                              tempListWD = mapWorkOrderID_ProdServWD.get(varWD.WorkOrderId);
                                          }
                                          tempListWD.add(varWD);
                                          mapWorkOrderID_ProdServWD.put(varWD.WorkOrderId, tempListWD);
                                      } }
        
        return mapWorkOrderID_ProdServWD;
    }
    
    private static map<Id,Case> getCases(set<Id> caseIds, set<Id> techIds) {
        //STRY0163562:Added Account.Account_Type__c in the query
        return new map<Id, Case>([SELECT id, Ownerid, AccountId,Account.Account_Type__c, City__c, Contactid, AssetId, BusinessHoursId, 
                                  Preferred_End_Time__c,Requested_End_Date_Time__c, Preferred_Start_Time__c, Requested_Start_Date_Time__c,
                                  Priority, Description, Malfunction_Start__c,Service_Contract__c,Location__c, State_Province2__c,
                                  Subject,Asset_Top_Level__c, Site_Country__c, Service_Contract__r.Name,P_O_Number__c, 
                                  Customer_Malfunction_Start_Date_time_Str__c,ProductSystem__r.Service_Team__c,
                                  Customer_Requested_Start_Date_Time__c,Customer_Malfunction_Start_Date_time__c,SaaSInstall__c,
                                  RecordTypeId, Install_PWO__c, Preferred_Service_Territory__c,Asset.Service_Territory__c, Reason, 
                                  (SELECT Id, ServiceTerritoryId, Interface_Status__c, Top_Level_Asset__c, AssetId, Status, CaseId, RecordTypeId,
                                   Service_Resource__c,NewSO__c 
                                   FROM WorkOrders 
                                   WHERE RecordType.Name IN :woRecordTypeSet and Service_Resource__c in : techIds) 
                                  FROM case
                                  WHERE id in: caseIds ]);
    }
    
    public static void deleteOrderedInstalledProducts(Map<Id, Case_Line__c> cLineOldMap, Map<Id, Case_Line__c> cLineNewMap){
        if(!cLineOldMap.isEmpty() && !cLineNewMap.isEmpty()) {
            Set<Id> soItemIds = new Set<Id>();
            for (Id clId : cLineNewMap.keySet()) {
                if (cLineOldMap.get(clId).Status__c != cLineNewMap.get(clId).Status__c && 
                    'Cancelled'.equalsIgnoreCase(cLineNewMap.get(clId).Status__c)) 
                    soItemIds.add(cLineNewMap.get(clId).Sales_Order_Item__c);
            }
            if (!soItemIds.isEmpty()) {
                List<Asset> instProdToDelete = [SELECT Id FROM Asset WHERE Sales_Order_Item__c = :soItemIds AND Status = 'Ordered'];
                delete instProdToDelete;
            }
        }
    }
    
    // Delete all Caseline related WO and WOLI.
    // STRY0172437 - Redesigned delete CLs functionality
    public static void caseLineDeleteHandler(list<Case_Line__c> caseLineList) {
        Set<Id> caseWOsIdSet = new Set<Id>();
        Set<string> deleteWOsIdSet = new Set<string>();
        Set<String> caseLineNameSet = new Set<String>();
        //List<Case_Line_History__c> UpdtList = new List<Case_Line_History__c>(); //Dipali- this should be commented as part of STRY0224909
        List<WorkOrder> deleteWorkOrderList = new List<WorkOrder>();
        Map<Id, WorkOrderLineItem> deleteWoliList = new Map<Id, WorkOrderLineItem>();
        User currentUser = VFSL_Utility.getLoggedInUserInfo(); // STRY0186075 - Get Current User Details
        
        if(!caseLineList.isEmpty()) {
            for(WorkOrderLineItem woliRec: [SELECT Id, WorkOrderId 
                                            FROM WorkOrderLineItem 
                                            WHERE Case_Line__c in: caseLineList]) {
                                                caseWOsIdSet.add(woliRec.WorkOrderId);
                                                deleteWoliList.put(woliRec.Id, woliRec);
                                            }
            // Get All Parent WOLI if its exist
            for(WorkOrderLineItem parentWoli: [SELECT Id 
                                               FROM WorkOrderLineItem 
                                               WHERE ParentWorkOrderLineItemId in: deleteWoliList.keyset()]) {
                                                   deleteWoliList.put(parentWoli.Id, parentWoli);
                                               }
            // get deleted WO List
            for(AggregateResult agr: [SELECT WorkOrderId, count(Id) 
                                      FROM WorkOrderLineItem 
                                      WHERE WorkOrderId in: caseWOsIdSet group by WorkOrderId]){
                                          if( Integer.valueOf(agr.get('expr0')) <= 1)  
                                              deleteWOsIdSet.add(string.valueOf(agr.get('WorkOrderId')));
                                      }
            // Get Case Line' Name
            for(Case_Line__c cl : caseLineList) {
                /* STRY0186075 - Start - Allow only case line owner or VMS Allowed Profiles to delete case lines */
                if(currentUser.Id != cl.OwnerId && currentUser.Id != cl.CreatedById &&
                   !Label.VFSL_WO_Allowed_Profiles.contains(currentUser.Profile.Name)) {
                       cl.addError('Cannot Delete Case Line Other than Case Line Creator/Owner or BST Team Member');
                   }
                /* STRY0186075 - End - Allow only case line owner or VMS Allowed Profiles to delete case lines */
                caseLineNameSet.add(cl.Name);
            }
            // Below will remove duplicate WorkOrderLineItem
            if(!deleteWoliList.isEmpty())
                delete deleteWoliList.values();
            
            if(!deleteWOsIdSet.isEmpty())
                delete [select Id From WorkOrder Where Id in: deleteWOsIdSet];
        }
    }
}
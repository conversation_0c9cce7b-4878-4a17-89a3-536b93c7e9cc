/*
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON><PERSON><PERSON>         Feb 21, 2025            STRY0485454 - Remove SR_testdata SVMXC referencing methods.
 */
@isTest
public class Ltng_SPO_FolderDeletionTest {
    @isTest 
    static void deleteSPOFolderTest(){
        Profile testProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        User testUser = new User(
            Alias = 'standt', 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8', 
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US', 
            ProfileId = testProfile.Id, 
            TimezoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>'
        );
        insert testUser;

        System.runAs(testUser){
            Case testCase = new Case();
            testCase.OwnerId = UserInfo.getUserId();                                       
            testCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Helpdesk').getRecordTypeId();                                
            testCase.Local_description__c  ='Local_description';              
            testCase.Local_Subject__c = 'Test';                                  
            testCase.ERP_Reference__c = 'HERP_Reference';                        
            testCase.Is_This_a_Complaint__c = 'No';                              
            testCase.Was_anyone_injured__c = 'No';                              
            testCase.Local_Case_Activity__c  ='Local_Case_Activity';
            testCase.Local_Internal_Notes__c   ='Local_Internal_Notes';
            testCase.Is_escalation_to_the_CLT_required__c = 'No';
            testCase.Incident_Workflow_Location__c = 'Simulation';
            testCase.Error_Code__c = 'Hardware';
            insert testCase;

            PHI_Log__c testPHI = new PHI_Log__c();
            testPHI.Case__c = testCase.Id;
            testPHI.Log_Type__c='Case';
            testPHI.Date_Obtained__c = System.today();
            testPHI.Disposition2__c = 'Regulatory Ownership Assigned and Transferred';
            testPHI.Disposition_Date__c = Date.today();
            insert testPHI;

            Group testGroup = new Group(
                Name = 'ECT_Investigation_Group',
                Type = 'Queue'
            );
            insert testGroup;

            QueueSObject testQueue = new QueueSObject(
                QueueId = testGroup.Id,
                SObjectType = 'PHI_Log__c'
            );
            insert testQueue;

            GroupMember testGroupMember = new GroupMember();
            testGroupMember.UserOrGroupId = testUser.Id;
            testGroupMember.GroupId = testGroup.Id;
            insert testGroupMember;

            testPHI.Case_Folder_Id__c = '11446498';
            testPHI.Folder_Id__c = '11446500';
            // testPHI.Server_Location__c = 'https://varian.app.box.com/folder/11446500';
            testPHI.BoxFolderName__c = 'Testing';
            testPHI.OwnerId = testGroup.Id;
            testPHI.Collab_id__c = '791293';
            testPHI.Complaint__c = '2018-2332-32';
            update testPHI;

            List<PHI_Log__c> newPHIList = new List<PHI_Log__c>{testPHI};

            Test.startTest();
            Ltng_SPO_FolderDeletion.deleteSPO_Folder(newPHIList);
            Test.stopTest();
        }
    }    
}
@isTest
public class MvEOSAcknowledgementControllerTest {

    @isTest
    static void testAcknowledgeNotification() {
        // Set up mock data
        Account Acc = VFSL_TestDataUtility.createAccount('My Varian Account test');
        insert Acc;
        Contact cont =VFSL_TestDataUtility.createContact(Acc.id);
        
		Asset asset = VFSL_TestDataUtility.createAsset('HTEST99'); 
        Product2 prod = new Product2(name='ARIA',productcode='***********',Product_Type__c='Model');
        insert prod;
        asset.Product2Id = prod.Id;
        asset.AccountId = Acc.Id;
        Complaint__c comp = new Complaint__c(Language__c='French',RA_Region__c='North');
        insert comp;
        asset.Customer_Notification__c = comp.Id;
        insert asset;
        
        list<Consignee__c> listCon = new list<Consignee__c>();
        Consignee__c EP2 = new Consignee__c(Complaint_Number__c = comp.id, PCSN__c ='H192807',Customer_Name__c= Acc.id,Contact_Name_look__c =cont.Id);
        listCon.add(EP2);
        
        insert listCon;
        List<PON__c> ListPon = new List<PON__c>();
        PON__c P2 = new PON__c(Customer_Name__c =Acc.Id,Consignee__c=EP2.id,PCSN__c='HTEST99',Contact__c=cont.Id,Product_Notification__c=comp.id,Asset__c=asset.id);
        ListPon.add(P2);
        
        insert ListPon; 


        // Call the method to test
        Test.startTest();
        MvEOSAcknowledgementController.acknowledgeNotification(
            comp.Id, 
            true, 
            'Feedback', 
            'Test Contact'
        );
        Test.stopTest();

        // Verify the changes to PON__c
        PON__c updatedPON = [SELECT Id, Status__c, Mode_of_Acknowledgement__c, 
                             Acknowledgement_Date__c, Proof_Type__c, Comments__c, Acknowledged_by__c 
                             FROM PON__c WHERE Id = :P2.Id];

        System.assertEquals('Completed', updatedPON.Status__c);
        System.assertEquals('MyVarian', updatedPON.Mode_of_Acknowledgement__c);
        System.assertEquals(System.today(), updatedPON.Acknowledgement_Date__c);
        System.assertEquals('Proof Received', updatedPON.Proof_Type__c);
        System.assertEquals('Feedback', updatedPON.Comments__c);
        System.assertEquals('Test Contact', updatedPON.Acknowledged_by__c);

        // Verify the changes to Asset
        Asset updatedAsset = [SELECT Id, Delivery_Status__c FROM Asset WHERE Id = :asset.Id];
        System.assertEquals('Delivered & Acknowledged', updatedAsset.Delivery_Status__c);
    }

 /*    @isTest
   static void testAcknowledgeNotification_NoNotificationId() {
        // Call the method with no Notification ID
        try {
            MvEOSAcknowledgementController.acknowledgeNotification(
                '', 
                true, 
                'Feedback', 
                'Test Contact'
            );
            System.assert(false, 'Expected an AuraHandledException due to missing Notification ID');
        } catch (AuraHandledException e) {
            System.assertEquals('No Notification ID provided.', e.getMessage());
        }
    }

    @isTest
    static void testAcknowledgeNotification_NoContactName() {
        // Call the method with no contact name
         Complaint__c comp = new Complaint__c();
        try {
            MvEOSAcknowledgementController.acknowledgeNotification(
                comp.Id, 
                true, 
                'Feedback', 
                ''
            );
            System.assert(false, 'Expected an AuraHandledException due to missing contact name');
        } catch (AuraHandledException e) {
            System.assertEquals('Please enter your name.', e.getMessage());
        }
    }

    @isTest
    static void testAcknowledgeNotification_NoAcknowledgement() {
        // Call the method with acknowledgement set to false
         Complaint__c comp = new Complaint__c();
        try {
            MvEOSAcknowledgementController.acknowledgeNotification(
                comp.Id, 
                false, 
                'Feedback', 
                'Test Contact'
            );
            System.assert(false, 'Expected an AuraHandledException due to unchecked acknowledgement');
        } catch (AuraHandledException e) {
            System.assertEquals('Please check email acknowledgement checkbox.', e.getMessage());
        }
    } */
}
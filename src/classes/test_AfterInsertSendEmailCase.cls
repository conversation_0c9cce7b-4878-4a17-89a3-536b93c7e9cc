@isTest
Public class test_AfterInsertSendEmailCase
{
    
    public static testmethod void testEmailMessage()   
    {

        Account a = new Account(name='test2',BillingCity='California',BillingCountry='usa',BillingState='Washington',BillingStreet='xyx',Country__c='USA',BillingPostalCode ='07601-6013', Site_Type__c = 'HOSPITAL'  );
        insert a;
        
        Contact con=new Contact(Lastname='ltest',FirstName='ftest',Email='<EMAIL>', AccountId=a.Id,MailingCountry ='Zimbabwe', MailingState='teststate', Phone = '124445');
        insert con;
       
        Id RecType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Case' AND DeveloperName = 'Helpdesk'].Id;
        Case c = new Case(AccountId = a.Id, RecordTypeId = RecType, Priority = 'Medium',contactId = con.id);
        insert c;
        
        test.startTest();
        
        EmailMessage t = new EmailMessage(subject = 'Test last Modification Date', TextBody = 'Test', ParentID = c.Id, Incoming=false,FromAddress='<EMAIL>');
        insert t;
        
        EmailMessage t2 = new EmailMessage(subject = 'Test last Modification Date', TextBody = 'Test', ParentID = c.Id, Incoming=true,FromAddress='<EMAIL>');
        insert t2;
        
         EmailMessage t3 = new EmailMessage(subject = 'Test last Modification Date', TextBody = 'Test', ParentID = c.Id, Incoming=true,FromAddress='<EMAIL>');
        insert t3;
        
        delete t2;
        
        test.sTopTest();
    }
    
}
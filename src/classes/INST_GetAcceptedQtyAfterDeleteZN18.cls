// DIpali Chede: 26/08/2022 : 
// This method will be called from Acceptance Certificate Delete record flow to get Accepated Qty after deletion

global with sharing class INST_GetAcceptedQtyAfterDeleteZN18 {
     
    @invocableMethod(label='Get ZN18 Accpted qty after deletion' iconName='slds:standard:record_lookup')
    global static List<Result> GetSOIwithAcceptedQTy(List<Requests> requestList) {
        /* Prepare Response */
        Result response = new Result();
        List<Result> responseWrapper = new List<Result>();
        List<Sales_Order_Item__c> updatedSOI = new List<Sales_Order_Item__c>();
        
        for(Requests req : requestList){
            List<Sales_Order_Item__c> varSobj_ZN18LineColl = req.varSobj_ZN18LineColl;
            String accCertId = req.accCertId;
            if(varSobj_ZN18LineColl != null && varSobj_ZN18LineColl.size() >  0){
                for(Sales_Order_Item__c soi : varSobj_ZN18LineColl){
                    Map<String,Integer> mapAccCertToQty = new  Map<String,Integer>();
                    String zn18qty = '';
                    if(soi.ZN18AcceptedQtyDetails__c != null && soi.ZN18AcceptedQtyDetails__c.contains(accCertId)){
                        List<String> accCertDetails = soi.ZN18AcceptedQtyDetails__c.split(',');
                        for(String str : accCertDetails){
                            mapAccCertToQty.put(str.split(':')[0],  Integer.valueof(str.split(':')[1]));
                        }
                        for(String str : mapAccCertToQty.keySet()){
                            if(str != accCertId)
                                zn18qty= zn18qty == ''? str+':'+mapAccCertToQty.get(str) : zn18qty+','+str+':'+mapAccCertToQty.get(str);
                        }
                        soi.ZN18AcceptedQtyDetails__c = zn18qty;
                        soi.AcceptedQty__c = soi.AcceptedQty__c - mapAccCertToQty.get(accCertId);
                        soi.Install_Date__c= null;
                        updatedSOI.add(soi);
                    }
                }
            }
        }
        response.updatedSOI = updatedSOI;
        responseWrapper.add(response);
        return responseWrapper;
    }
    
    global class Requests {
        @InvocableVariable(required=true)
        global List<Sales_Order_Item__c> varSobj_ZN18LineColl;
        
        @InvocableVariable(required=true)
        global  String accCertId;
    } 
    
    global class Result {
        @InvocableVariable(required=true)
        global List<Sales_Order_Item__c> updatedSOI;  
    } 
    
}
/*
*  Modification Log
*  -----------------------------------------------------------
*  Developer           Modification Date           Comments
*  -----------------------------------------------------------  
*  <PERSON><PERSON><PERSON>       March 25, 2025          STRY0529521 - SVMXC Package, Debug Logs and Commented Code Removal
*/
@isTest
private class CreateProposalCtrlTest {

    static testMethod void createProposal(){
        
        User user = [SELECT Id, Name, Email, Phone from User WHERE Id =: UserInfo.getUserId()];
        

        
        Account testAccount = new Account();
        testAccount.Name = 'TestAccount';
        testAccount.Country__c = 'USA';
        testAccount.Account_Type__c = 'Customer';
        testAccount.BillingStreet = '1234 test street';
        testAccount.BillingCity = 'testCity';
        testAccount.BillingState = 'CA';
        testAccount.BillingPostalCode = '00000';
        insert testAccount;
	
        AccountTeamMember atm = new AccountTeamMember();
        atm.UserId = user.Id;
        atm.AccountId = testAccount.Id;
        insert atm;
        
        Sales_Order__c so = new Sales_Order__c();
        so.Site_Partner__c = testAccount.Id;
        so.Name = 'TestSoName';
	    insert so;
        
        Product2 prod = new Product2();
        prod.Name = 'TestProduct';
        prod.Material_Group__c = 'H:EDUCTN';
        insert prod;        
	
       	Product2 prod2 = new Product2();
        prod2.Name = 'TestProduct2';
        prod2.Material_Group__c = 'H:TRNING';
        insert prod2;  
		
        Language__c lang = new Language__c();
        lang.BMI_Code__c = 'en';
        insert lang;
        
        Product_Description_Language__c prdDesc = new Product_Description_Language__c();
        prdDesc.Language__c = lang.Id;
        prdDesc.Product__c = prod.Id;
        prdDesc.Long_Desc_RichText__c = 'desc text';
        insert prdDesc;

                    
        Product_Description_Language__c prdDesc2 = new Product_Description_Language__c();
        prdDesc2.Language__c = lang.Id;
        prdDesc2.Product__c = prod2.Id;
        prdDesc2.Long_Desc_RichText__c = 'desc text 2';
        insert prdDesc2;
	        
        Sales_Order_Item__c soi = new Sales_Order_Item__c();
        soi.Site_Partner__c = testAccount.Id;
        soi.Sales_Order__c = so.Id;
        soi.Product__c = prod.Id;
        soi.ERP_Item_Category__c = 'Z014';
        insert soi;
        
        Sales_Order_Item__c soi2 = new Sales_Order_Item__c();
        soi2.Site_Partner__c = testAccount.Id;
        soi2.Sales_Order__c = so.Id;
        soi2.Product__c = prod2.Id;
        soi2.ERP_Item_Category__c = 'Z014';
        insert soi2;
        
        ERP_NWA__c nwa = new ERP_NWA__C();
        nwa.erpnwa_SalesOrderItem__c = soi.Id;
        nwa.Name = 'NWA for soi1';
        nwa.INST_ESTRT__c = Date.today();
        nwa.Training_Forecast_End_Date__c = Date.today();
        nwa.Expiration_Date__c = Date.today();
        nwa.Product__c = prod.Id;
        insert nwa;

        ERP_NWA__c nwa2 = new ERP_NWA__C();
        nwa2.erpnwa_SalesOrderItem__c = soi2.Id;
        nwa2.Name = 'NWA for soi2';
        nwa2.INST_ESTRT__c = Date.today();
        nwa2.Training_Forecast_End_Date__c = Date.today();
        nwa2.Expiration_Date__c = Date.today();
        nwa2.Product__c = prod2.Id;
        insert nwa2;
        
        Contact con1 = new Contact();
        con1.FirstName = 'FN1';
        con1.LastName = 'LN1';
        con1.Email = '<EMAIL>';
        con1.Phone = '*********';
        con1.AccountId = testAccount.Id;
        con1.MailingCountry = 'USA';
        con1.MailingState = 'CA';
        insert con1;
        
        Contact con2 = new Contact();
        con2.FirstName = 'FN2';
        con2.LastName = 'LN2';
        con2.Email = '<EMAIL>';
        con2.Phone = '*********';
        con2.AccountId = testAccount.Id;
        con2.MailingCountry = 'USA';
        con2.MailingState = 'CA';
        insert con2;
        
        
        String contactWrapper = con1.Id + ':' + con1.Name + ':' + con1.Email + ';' + con2.Id + ':' + con2.Name + ':' + con2.Email + ';' ;
        String managerWrapper = UserInfo.getUserId() + ':' + UserInfo.getName() + ':' + UserInfo.getUserEmail() + ';';
        
        Proposal__c proposal = new Proposal__c();
        proposal.Version__c = 0;
        proposal.Account__c = testAccount.Id;
        proposal.Sales_Order__c = so.Id;
        insert proposal;
		
        
        List<Id> userIdList = new List<Id>();
        userIdList.add(UserInfo.getUserId());
        

        PageReference pageRef = Page.ProposalAttachment;
        Test.setCurrentPage(pageRef);
       	ApexPages.currentPage().getParameters().put('Id', proposal.Id);

        Test.startTest();
        
        
        String SoBaseStr = CreateProposalCtrl.createSalesOrderBase(so.Id);
        
        Map<String, Object> soJSON = (Map<String, Object>) JSON.deserializeUntyped(SoBaseStr);
        String soiStrings = JSON.serialize(soJSON.get('SalesOrderItems'));
        
        CreateProposalCtrl.getContactsManagersFromAccount(testAccount.Id);
        CreateProposalCtrl.getAdditionalUserByName(UserInfo.getName());
        CreateProposalCtrl.postProposal2(proposal.Id, soiStrings, managerWrapper, contactWrapper, contactWrapper);
        CreateProposalCtrl.createProposalAttachment(proposal.Id, false);        
        CreateProposalCtrl.getProposalDetails(proposal.Id);
        
        CreateProposalCtrl.getAdditionalProductByName('TestProduct');
        CreateProposalCtrl.getAdditionalProductWrapper(prod.id);
        CreateProposalCtrl.getAdditionalUserById(userIdList);
        CreateProposalCtrl.getLocationsFromSalesOrder(so.Id);
        
        ProposalWrapper.ProductWrapper pw = new ProposalWrapper.ProductWrapper(prod);
        ProposalWrapper.ProductWrapper pw2 = new ProposalWrapper.ProductWrapper(prod2);

		ProposalWrapper.Managers manager = new ProposalWrapper.Managers(user);
       	ProposalWrapper.Managers manager2 = new ProposalWrapper.Managers(atm, false);
		
        ProposalAttachmentCtrl controller = new ProposalAttachmentCtrl();
        
        emailProposalCtrl.getContactWrapperList(proposal.Id);
        emailProposalCtrl.getContactsList(proposal.Id);
		emailProposalCtrl.getTrainingCoordinator(proposal.Id);
        emailProposalCtrl.getContacts(proposal.Id);
        emailProposalCtrl.getAttachments(proposal.Id);
        emailProposalCtrl.getMostRecentAttachment(proposal.Id);
        emailProposalCtrl.sendEmail(new List<String>{'to'}, new List<String>{'cc'}, 'replyToAddress', 'subject', 'body', null, proposal.Id);

		
        ProposalHomeCtrl.queryProposals('','', '','', '', '');
        ProposalHomeCtrl.queryProposals('a', so.Name , '','pending', '', '');


        Test.stopTest();

    }

}
/**
*  Class Name: UserTriggerServices 
*  Description: This is a service class for User. 
*  Company: Varian FSL
*  CreatedDate: April 05, 2024
*  
*  Modification Log
*  -----------------------------------------------------------
*  Developer           Modification Date         Comments
*  -----------------------------------------------------------  
*  <PERSON> Lin          Apr 05, 2024           STRY0177673 - New trigger framework
*/

public without sharing class UserTriggerServices {
    private static Set<Id> stModelAnalyticsId = new Set<Id>();
    private static Set<Id> stModelAnalyticsInactivate = new Set<Id>();
    
    set<Id> stContactId = new set<Id>();
    set<Id> stContactIddeactivate = new set<Id>();
    private static Map<Id,Contact> RelatedContacts = new Map<Id,Contact>();
    private static Set<Id> acctIds = new Set<Id>();
    
    public void updateUsernameBefore(List<User> newUserList){
        Profile p  = Check_UpdateUsername.runOnce();       
        
        for(user u: newUserList){
            System.debug('con1: '+(p <> null) );
            System.debug('con2: '+(u.contactid != null) );
            System.debug('con3: '+(u.IsActive == true) );
            System.debug('con4: '+(u.profileid == p.Id) );
            System.debug('con5: '+(!(u.Username.contains(Label.MyVarianUserNameText))) );
            if(p <> null && u.contactid != null && u.IsActive == true 
                && u.profileid == p.Id && !(u.Username.contains(Label.MyVarianUserNameText))){
                u.Username = u.Username + Label.MyVarianUserNameText;
            }
         }
    }
    
    //STRY0232198 - Added by Mukul to update NickName if myvarian user is deactivating start
    public void setCommunityUserNickname(List<User> newUserList, Map<Id, User> oldUserMap){
        for(User user: newUserList){ 
           if(oldUserMap.get(user.Id).isActive && oldUserMap.get(user.Id).isActive != user.isActive && !user.isActive){      
             user.CommunityNickname = user.CommunityNickname+'_'+math.round((math.random()*10000));
           }
        }
    }
    //STRY0232198 - Added by Mukul to update NickName if myvarian user is deactivating start
    
    //update nick name of old community user on new user enabling.
    public void updateCommunityUserNickname(List<User> newUserList){
        set<String> nickNames = new set<String>();
        set<String> setEmail = new set<String>();
        for(User user: newUserList){  
            nickNames.add(user.CommunityNickname);
            setEmail.add(user.email);
        }
        if(!nickNames.isEmpty()){
            List<user> users = [SELECT Id,CommunityNickname 
                                FROM User 
                                WHERE CommunityNickname in: nickNames AND Email NOT IN : setEmail];
            for(user user: users){
                user.CommunityNickname = user.CommunityNickname+'_'+math.round((math.random()*10000));
            }
            if(!users.isEmpty()){
                update users;
            }
        }        
    }
    
    //STRY0505124 - LMS Account is not used any more in system START
    // Function Removed for cleanup
    //STRY0505124 - LMS Account is not used any more in system END
    
    public void updateTrueContext(List<User> newUserList, Map<Id, User> oldUserMap){
        List<user> users = new List<user>();
        for(User user: newUserList){     
            //STRY0226357 - Include managerId change to the if condition
            //STRY0230299 - Include Prontoforms_User__c
            if(user.Prontoforms_User__c  != oldUserMap.get(user.id).Prontoforms_User__c  ||
               user.Title != oldUserMap.get(user.id).Title  ||
               user.department != oldUserMap.get(user.id).Department  ||
               user.CompanyName != oldUserMap.get(user.id).CompanyName ||
               user.ManagerId != oldUserMap.get(user.id).ManagerId ||
               user.Prontoforms_Group__c != oldUserMap.get(user.id).Prontoforms_Group__c ){
                   user.Prontoforms_API_Call__c = true;         
            }
        }
    }
    // ANNIE
    public void setFieldsOnInsert(List<User> newUserList, Map<Id, User> oldUserMap){
        //Ranjan 12/01/2015
        list<id> contlist = new list<id>();
        for(User u : newUserList){
            if(u.contactid!=NULL){
                contlist.add(u.contactid);
            }
        }
        if(!contlist.isEmpty()){
            RelatedContacts = new Map<Id,Contact>([SELECT Id, Name, Need_LMS_Access__c, AccountId, MyVarian_Member__c 
                                                   FROM Contact
                                                   WHERE Id IN :contlist]);
        }
            
        for (User u : newUserList){
            if (RelatedContacts.containsKey(u.contactid) 
                && RelatedContacts.get(u.contactid).Need_LMS_Access__c && u.LMS_Status__c == null){   
                u.LMS_Status__c = 'Request';   
                u.LMS_Registration_Date__c = Datetime.now();
            }
        }// until here
        
        if(!RelatedContacts.isEmpty()){
            for(Contact c : RelatedContacts.values()){
                if(c.AccountId != NULL){
                    acctIds.add(c.AccountId);
                }
            }
        }
        
        //get list of Accounts with specific timezone info
        List<Account> accnts = new List<Account>();
        if(!acctIds.isEmpty()){
            accnts = [SELECT Id, Timezone__c, Timezone__r.Salesforce_timezone__c
                      FROM Account
                      WHERE Id IN :acctIds];
        }

        for(User us : newUserList){
            List<String> timezones =new List<String>();
            if(us.ContactId <> null && us.Subscribed_Products__c != true){
                us.Subscribed_Products__c = true;
            }
            Profile prf = CurrentLoginUserInformation.getCurrentLoginUSerProfile();
            String profileName = prf.Name;
            for(PicklistEntry  p:User.fields.TimeZoneSidKey.getDescribe().getpicklistvalues()){
                timezones.add(p.getValue());
            }
            if(us.ContactId <> null && profileName =='VMS MyVarian - Customer User'){
                for(String p : timezones ){
                    if(!accnts.isEmpty() && accnts[0].Timezone__c != null 
                        && string.valueof(accnts[0].Timezone__r.Salesforce_timezone__c).contains(p)){
                        us.TimeZoneSidKey = p;
                    }
                }
            }
        }
    }
    
    public void setKCSCoach(List<User> newUserList, Map<Id, User> oldUserMap){
        //KCS Coach update story#STRY0127670
        map<id,User> usrsmap = new map<Id,User>();        
        String oldKcs, newKcs;
        Set<Id> setId = new Set<Id>();

        for(User us :  newUserList){
            if(us.KCS_Coach__c <> null){
                setId.add(us.KCS_Coach__c);
            }
        }
        if (!setId.isEmpty()){
            usrsmap = new map<Id, USer>([SELECT Id, Name 
                                         FROM User 
                                         WHERE id in: setId]);
            for(User us : newUserList) {  
                if(oldUserMap != null && oldUserMap.containsKey(us.Id) 
                    && oldUserMap.get(us.Id).KCS_Coach__c != us.KCS_Coach__c && us.KCS_Coach__c <> null){
                    if(us.Kcs__c <> null && usrsmap.containsKey(us.KCS_Coach__c)){
                        us.Kcs__c = usrsmap.get(us.KCS_Coach__c).Name+','+us.Kcs__c;
                    }else if(usrsmap.containsKey(us.KCS_Coach__c)){
                        us.Kcs__c = usrsmap.get(us.KCS_Coach__c).Name;
                    }
                }else if(oldUserMap == null && us.KCS_Coach__c <> null) {
                    us.Kcs__c = usrsmap.get(us.KCS_Coach__c).Name;
                }
            }
        }
    }
    
    public void modelUserCreation(List<User> newUserList, Map<Id, User> oldUserMap){
        //for creating user
        for(user u: newUserList){
            if(u.contactid != null){
                if(u.IsActive == True && u.is_Model_Analytic_Member__c){
                    stModelAnalyticsId.add(u.contactId);
                }
            }     
        }      
    }
    public void modelUserUpdate(List<User> newUserList, Map<Id, User> oldUserMap){
         //User update and user is made active          
        for(user u: newUserList){ 
            if(u.contactid !=null){
                if(u.IsActive && u.is_Model_Analytic_Member__c 
                    && (oldUserMap.get(u.id).IsActive != u.IsActive 
                        || oldUserMap.get(u.id).is_Model_Analytic_Member__c != u.is_Model_Analytic_Member__c)){
                    stModelAnalyticsId.add(u.contactId);
                }
                if((!u.IsActive && oldUserMap.get(u.id).IsActive != u.IsActive) 
                    || (!u.is_Model_Analytic_Member__c && oldUserMap.get(u.id).is_Model_Analytic_Member__c != u.is_Model_Analytic_Member__c )){
                    stModelAnalyticsInactivate.add(u.contactId);
                }
            }
        }
    }
    
    public void modelQueueableInterface(){
        if(stModelAnalyticsId.size() > 0){
            ID jobID = System.enqueueJob(new ModelAnalytics(stModelAnalyticsId,'PUT','CpOktaModelAnalytics'));
        }
        if(stModelAnalyticsInactivate.size() > 0){
            ID jobID = System.enqueueJob(new ModelAnalytics(stModelAnalyticsInactivate,'DELETE','CpOktaModelAnalytics'));
        }    
    }
    
    public void BigMachineChatterGroup(Map<Id, User> oldUserMap, Map<Id, User> newUserMap){
        //Rakesh.2/25/2016.Start
        //Add User to chatter group when Synchronize with BigMachines=TRUE
        if(VIC_TriggerSupport.ignoreContactTrigger == false){
            UserBigMachine.AddRemoveUserToGroup(newUserMap, oldUserMap,'Pricebook Notes','VMS Marketing - User');              
        }
    }
    public void CpUserCreation(List<User> newUserList){
        //STRY0569414 START
        Set<Id> activeCustomerUser = new Set<Id>();
        Set<Id> stContactId = new Set<Id>();
        for(user u: newUserList){
            if(u.contactid != null){
                stContactId.add(u.contactId);
                if(String.isNotBlank(u.email) && !u.Email.contains('@varian.com') && !u.Email.contains('@siemens-healthineers.com')){
                    activeCustomerUser.add(u.Id);
                }
            }
        }
        //CpUserActivationClass.useractivationmethod(stContactId);
        if(!activeCustomerUser.isEmpty()){
            System.enqueueJob(new MvHomeBodyControllerHelper(activeCustomerUser));
        }
        //STRY0569414 END
        System.enqueueJob(new vMC_SetupUser(stContactId));// Added by Krishna. User story STRY0115103
    }
    
    //STRY0569414 Removed CpOktaUserUpdate method as no longer needed.
	
    public void deactivateUserSessionIds(List<User> newUserList){
        // Puneet Mishra, STSK0012350 : deleting usersessionIds rec is user if deactivated
        Set<Id> deactivatedUser = new Set<Id>();
        if(trigger.isUpdate && trigger.isAfter) {
            for(User u : newUserList ){
                deactivatedUser.add(u.Id);
            }
            if(!deactivatedUser.isEmpty()){
                DeactivateUserTriggerHandler.deleteDeactivatedUser(deactivatedUser);
            }
        }
    }
    
    public void addVMSInternalUserInsert(List<User> newUserList, Map<Id, User> oldUserMap, Map<Id, User> newUserMap){
        set<id> stNewInstalledProduId=new set<id>();
        set<string> stProfile=new set<string>();
        set<id> accountId = new set<id>(); //set for adding new user in the account's marketing kit.
        set<id> marketingkitids = new set<id>();
        set<Id> userIdSet = new set<Id>();
        Set<Id> contactIds = new Set<Id>();
        set<Id> profileIds = new Set<Id>();  //DE7861
        Map <Id,Profile> profileMap = new Map <Id,Profile>(); //DE7861
        String profName;
        
        for (User u: newUserList){ //DE7861
            if (u.profileId != null){
                profileIds.add(u.profileId);
            }
        }
        if (profileIds.size()>0){ //DE7861
            profileMap = new Map <Id,Profile>([SELECT id,name 
                                               FROM profile 
                                               WHERE id in :profileIds]);
        }

        for (User usr1 : newUserList){
            profName = profileMap.get(usr1.ProfileId).name; //DE7861
            
            if ((usr1.ContactId != null) && (profName).toLowerCase().contains('myvarian')){
                contactIds.add(usr1.ContactId);
            }
        }

        // Nilesh - Start - Validation Rule- duplicate contact email
        List<Contact> conList = new List<Contact>();
        if(!contactIds.isEmpty()){
            conList = [SELECT Id, DupContact__c 
                       FROM Contact 
                       WHERE Id in: contactIds];
        }
                
        Map<Id, Contact> duplicateContactMap = new Map<Id, Contact>();
        for(Contact cn : conList){
            if(cn.DupContact__c){
                duplicateContactMap.put(cn.Id, cn);
            }
        }
        for(User usr : newUserList){
            if(duplicateContactMap.containsKey(usr.ContactId)){
                usr.addError('There are multiple contacts with the same email address. Please reconcile before enabling community user.');
            }
        }
        // Nilesh - Stop - Validation Rule- duplicate contact email
        
        if (contactIds.size() > 0) {
            ProductUtils.updateContactPartnerFlag(contactIds, true);
        }
        
        // Get the set of Account Id's to create a group member in the "VMS MyVarian - Partners" group.

        for(user u: newUserList){
            if(u.contactid !=null){
                contact con=new contact(id=u.contactid,Registered_Portal_User__c=true);
                stProfile.add(u.ProfileId);
                accountId.add(con.accountid);
                userIdSet.add(u.Id);
            }
        }
        if(accountId.size() > 0){
            List<MarketingKitRole__c> mktrList = [SELECT Id 
                                                  FROM MarketingKitRole__c 
                                                  WHERE Account__c in: accountId];
            for(MarketingKitRole__c mktr: mktrList){
                if(mktr.id != NULL){ 
                    marketingkitids.add(mktr.id); 
                }
            }
        }
        
        Map<String, Profile> mpProfile = new Map<String, Profile>();
        if(!stProfile.isEmpty()){
            mpProfile=new Map<string,Profile>([SELECT Id, Name 
                                               FROM profile 
                                               WHERE id IN: stProfile AND Name !='Customer Portal User']);
        }
        
        if(!Test.isRunningTest()){
            List<Asset> ipList = new List<Asset>();
            if(!acctIds.isEmpty()){
                ipList = [SELECT Id,Product2Id,AccountId 
                          FROM Asset 
                          WHERE AccountId =: acctIds];
            }
            for(Asset ip :ipList){
                if(ip.Id != NULL){
                    stNewInstalledProduId.add(ip.id);
                }
            }  
        }
                                
        if(stNewInstalledProduId.size()>0){
            AddVMSInternalUserToGroupHandler.addMvUserIntoAssetGroup(stNewInstalledProduId);
            
        }
        if(newUserMap.values().get(0).ContactId != Null){
            List<Contact> lstContacts = [SELECT AccountId 
                                         FROM Contact 
                                         WHERE Id =: newUserMap.values().get(0).ContactId];
            if(!lstContacts.isEmpty()){
                Id accId = lstContacts[0].AccountId;
                if(accId != null){
                    BatchAddUserToSelectedPG temp = new BatchAddUserToSelectedPG(userIdSet,accId);
                    database.executeBatch(temp,1);
                }
            }
        }
                
        if(marketingkitids.size() > 0){
            CpProductPermissions.AddPortalUserInGroup(marketingkitids);
        }
        if(!newUserList.isEmpty()){
            CpProductPermissions.AddVMSInternalUserToGroup(newUserList);
        }
        
        Set<Id> portalUserAccountIds = new Set<Id>();
        for(user u: newUserList){
            if(u.accountid!=null){
                portalUserAccountIds.add(u.accountid);
            }
        }
        if(portalUserAccountIds.size() > 0){
            // Get the Accounts which are related to user whoost account type is distributor.
            Map<Id,Account> accountsMap = new Map<Id,Account>([SELECT Id,Distributor__c, Account_Type__c 
                                                               FROM Account 
                                                               WHERE Id in : portalUserAccountIds and Distributor__c = true and IsPartner = true]);
            Set<Id> distributorUserIds = new Set<Id>();
            for(User newUser : newUserList){
                if(newUser.AccountId != null){
                    Account acc = accountsMap.get(newUser.AccountId);
                    if(acc != null){
                        distributorUserIds.add(newUser.Id);
                    }
                }
            }
            if(distributorUserIds.size() > 0 && !System.isFuture() && !System.isBatch()){
                FuturePartnerGroupMember.insertMember(distributorUserIds);
            }
        }  
    }
    
    public void addVMSInternalUserUpdate(List<User> newUserList, Map<Id, User> oldUserMap){
        Set<Id> stContactIddeactivate = new set<Id>();
        /* Added additional logic to call AddVMSInternalUserToGroup() method only when the contentPermission value is changed */
        List<String> UserIds = new List<String>();
        
        for(User u: newUserList){
            if(u.UserPermissionsSFContentUser != oldUserMap.get(u.id).UserPermissionsSFContentUser && u.UserPermissionsSFContentUser == True){
                UserIds.add(u.id);
            }
        }
        if(UserIds.size() > 0){
            CpProductPermissions.AddVMSInternalUserToGroup(newUserList);
        }
        
        Set<id> Setuserids=new Set<id>();
        ///*  DE7155: deactivating Technician records when User record gets activated */

        for(user u: newUserList){
            if(u.contactid !=null){
                if((oldUserMap.get(u.id).IsActive != u.IsActive && u.IsActive == false) || u.IsPortalEnabled == false){
                    if(u.accountid!=null){
                        Setuserids.add(u.id);
                    }
                    stContactIddeactivate.add(u.contactid); // set for deactivating the user
                }
            }
        }
        
        if (stContactIddeactivate.size() > 0){
            if(!system.isFuture() && !System.isBatch()){
                ProductUtils.updateContactPartnerFlag(stContactIddeactivate, false);
            }
        }
        if(Setuserids.size() > 0){
            List<GroupMember> DelPartnerGroupMembers = new List<GroupMember>();
            DelPartnerGroupMembers = [SELECT Id,GroupId 
                                      FROM GroupMember 
                                      WHERE  UserOrGroupId in: Setuserids];
            if(DelPartnerGroupMembers.size() > 0){
                delete DelPartnerGroupMembers;
            } 
        }  
    }
    
    public void addUserToChatterGroup(List<User> newUserList){
        // Adding new user to chatter group
        Map<Id, String> userMap = new Map<Id, String>();
        for (User u:newUserList) {
            userMap.put(u.Id, u.Country);
        }
        UserGroupTriggerHandler.addUserToChatterGroup(userMap);
    }    
      
}
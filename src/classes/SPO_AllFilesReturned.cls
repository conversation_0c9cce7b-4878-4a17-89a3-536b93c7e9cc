/*
* Author: <PERSON><PERSON>
* Created Date: 2-June-2019
* Project/Story/Inc/Task : Unity/Sharepoint Integration for Lightning
* Description: 
* Contains method 
* Retrieves all Files from the Folder  
*/
public class SPO_AllFilesReturned {    
    SPO_SF_API_Integration  sp = new SPO_SF_API_Integration ();
    public String site_url = Label.SP_Site_URL;
    
    public Map<String,Object> retrieveAllFilesFromFolder(String folderName) {   
        String accessToken = sp.getAccessToken();
        http http=new http();
        HTTPResponse res=new HttpResponse();
        HttpRequest req = new HttpRequest();
        req.setMethod('GET');
        req.setHeader('Authorization', 'Bearer '+accessToken);
        req.setHeader('Accept', 'application/json;odata=verbose');
        
        String endpoint = site_url+'/_api/web/GetFolderByServerRelativeUrl(\'Shared Documents/'+ folderName +'\')/Files';
        endpoint = endpoint.replace(' ', '%20');
        System.debug('endpoint======='+endpoint);
        req.setEndpoint(endpoint);
        
        if(Test.isRunningTest()){
            res=MyResponse.checkRequestType(req.getEndpoint());
        }else{
            res = http.send(req);
        }
        
        
        String jasonresp = res.getBody();
        
        Map<String, Object> jsonObj = (Map<String, Object>)JSON.deserializeUntyped(jasonresp);
        
        
        Integer statusCode = (Integer) res.getstatuscode();
        System.debug('Get All Files From Folder Path---------'+jasonresp);
        System.debug('retrieveAllFilesFromFolder statusCode---------'+statusCode);
        return jsonObj;
    }   
}
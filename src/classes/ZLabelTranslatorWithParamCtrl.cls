/*
 * @author: am<PERSON><PERSON>
 * @description: custom label taransaltion controller
 */

public class ZLabelTranslatorWithParamCtrl {
    public String label_lang {get;set;}
    public String label {get;set;}
    public list<String> customLabels {get;set;}
    
    public  ZLabelTranslatorWithParamCtrl(){
       Map<String, String> reqParams = ApexPages.currentPage().getParameters(); 
        if(reqParams.get('label_lang') != null){
             label_lang = reqParams.get('label_lang');
       		 label = reqParams.get('label');
        }else{
             label = 'en';
        }
        if(reqParams.get('labels') != null){
            String labels = reqParams.get('labels');
         	customLabels =  labels.split(',');
        }else{
			customLabels =  new list<String>();
        }
      
    }
}
/*
Author         : Shital Bhujbal
Functionality  : Test class written to test functinality of VFSL_WorkOrderContentCount aprx class.
Date           : 04 June 2019
*/
@isTest
public class VFSL_WorkOrderContentCountTest {
      
    public static testMethod void insertContent_Test() {
        Set<Id> workOrderIds = new Set<Id>();
        Integer decrement = 0;
        
        RecursiveQuoteTriggerController.isSuccessEmailSent = true;
        PreventBMQ.runOnce();
        LostFiscalYearRecursive.runOnce();
        
        Account accountObj = New Account(Name='Test Acc',Account_Type__c='Customer',SAP_Account_Type__c='Z001',State__c='California',
                                       BillingStreet='Test Street',BillingCity='Milpitas',BillingState='CA',State_Province_Is_Not_Applicable__c=true,
                                       Country__c='USA',BillingPostalCode='12345');

        accountObj.recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Sold_to').getRecordTypeId();
        insert accountObj;
       
        SObjectType entity = workOrder.getSObjectType();
        Id trainingRecTypeId = VFSL_TestDataUtility.getRecordTypeId(entity,'Planning_TE');
        WorkOrder workOrd = new Workorder(AccountId = accountObj.Id,Description='test',Status='Assigned', Dispatch_Status__c='Accepted',
                                Service_Super_Region__c='006',Street='Pune',State='CA',PostalCode='95035',
                                Country='USA',City='Pune',Internal_Comments__c='Test',
                                Direct_Assignment__c=false, FSR_Language_Code__c='ENG');
        workOrd.recordtypeId =trainingRecTypeId;
       
        insert workOrd;
        workOrderIds.add(workOrd.Id);
        
        test.startTest();
        ContentVersion contentVersion_1 = new ContentVersion(Title = 'Test File',PathOnClient = 'TestFile.jpg',VersionData = Blob.valueOf('Test Content'));
        ContentVersion contentVersion_11 = new ContentVersion(Title = 'Test new file',PathOnClient = 'TestNewFile.jpg',VersionData = Blob.valueOf('Test New Content'));
        insert contentVersion_1;
        insert contentVersion_11;
        Set<Id> contentDocId = new Set<Id>();
        contentDocId.add(contentVersion_1.ContentDocumentId);
        ContentVersion contentVersion_2 = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion_1.Id LIMIT 1];
        ContentVersion contentVersion_22 = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion_11.Id LIMIT 1];

        //List<ContentDocument> documents = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument];
        
        List<ContentDocumentLink> contentLinkList = new List<ContentDocumentLink>();
        ContentDocumentLink contentlink = new ContentDocumentLink();
        contentlink.LinkedEntityId = workOrd.id;
        contentlink.contentdocumentid = contentVersion_2.ContentDocumentId;
        contentlink.ShareType = 'V';
        contentLinkList.add(contentlink);
        
        ContentDocumentLink contentlink1 = new ContentDocumentLink();
        contentlink1.LinkedEntityId = workOrd.id;
        contentlink1.contentdocumentid = contentVersion_22.ContentDocumentId;
        contentlink1.ShareType = 'V';
        contentLinkList.add(contentlink1);
        
        insert contentLinkList;
        
        VFSL_WorkOrderContentCount.countContentOnWO(workOrderIds, decrement);
        test.stopTest();
    }
}
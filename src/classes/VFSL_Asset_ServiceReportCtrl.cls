public class VFSL_Asset_ServiceReportCtrl {
    @AuraEnabled
    public static Map<String, Map<String, String>> getReportData() {
        Map<String, Map<String, String>> reportData = new Map<String, Map<String, String>>();

        reportData.put('ConfigReport', new Map<String, String>{'Name' => '00O0h000007N5AjEAK'}); 
        reportData.put('ConfigReportZOR', new Map<String, String>{'Name' => '00O0XXXX0000000AAB'});
        reportData.put('PlannedConfigReport', new Map<String, String>{'Name' => '00OJx000001iH6DMAU'});
        reportData.put('DeliverableReport', new Map<String, String>{'Name' => '00OJx000004lVR3MAM'});
        //reportData.put('PlannedDeliverableReport', new Map<String, String>{'Name' => '00O0XXXX0000000AAE'});
        reportData.put('LicensingReport', new Map<String, String>{'Name' => '00O0h000007N8pbEAC'});
        reportData.put('HITReport', new Map<String, String>{'Name' => '00O0h000004ZpWREA0'});
        reportData.put('EquPartsReport', new Map<String, String>{'Name' => '00O8a000007NOZGEA4'});
        
        return reportData;
    }
}
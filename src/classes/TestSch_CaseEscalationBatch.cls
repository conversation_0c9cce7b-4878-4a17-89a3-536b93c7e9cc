@isTest
public class TestSch_CaseEscalationBatch {
    
    public static testMethod void method1() {
        
        Id mirCaseRecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('MIR').getRecordTypeId();
        
        Account account = new Account(Name='Test Account',State_Province_Is_Not_Applicable__c=true, Country__c='Albania', BillingCity='Test',Site_Type__c='HOSPITAL');
        insert account;

        Contact contact = new Contact(Lastname='Test', FirstName='Test',Phone='*********',AccountId=account.id, Email='<EMAIL>');
        insert contact;
        
        List<Case> caseList = new List<Case>{
            new Case(AccountId = account.Id, ContactId = contact.Id, RecordTypeId = mirCaseRecordTypeId, Priority = 'Urgent - Patient Treatment Pending', Status = 'New',Tools_Used__c = 'Custom Response;Reprints'),
            new Case(AccountId = account.Id, ContactId = contact.Id, RecordTypeId = mirCaseRecordTypeId, Priority = 'Within 2 Business Days', Status = 'New'),
            new Case(AccountId = account.Id, ContactId = contact.Id, RecordTypeId = mirCaseRecordTypeId, Priority = 'Within 5 Business Days', Status = 'New'),
            new Case(AccountId = account.Id, ContactId = contact.Id, RecordTypeId = mirCaseRecordTypeId, Priority = 'Greater than 5 business days', Status = 'New')
        };
        insert caseList;
        
        Test.startTest();
        Sch_CaseEscalationBatch sh1 = new Sch_CaseEscalationBatch();
        String sch = '0 0 2 * * ?'; 
        system.schedule('Test Territory Check', sch, sh1); 
        Test.stopTest();
        
    }
}
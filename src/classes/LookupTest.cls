@isTest
public class LookupTest {
@isTest
    public static void testSearchDB(){
        Account accnt= OCSUGC_TestUtility.createAccount('test account', true);
        contact con = new contact();
        con.FirstName= 'Test';
        con.lastname= 'User';
        con.Accountid= accnt.id;
        con.Email='<EMAIL>';
        con.Inactive_Contact__c = False;
        insert con;
        
        String objectName = 'Contact';
        String fld_API_Text ='Name';
        String fld_API_Account= 'AccountId';
        String fld_API_AccntName= 'Account_Name__c';
        String fld_API_Val='Id';
        Integer lim= 10;
        String fld_API_Search='Name';
        String searchText='Test';
        Lookup.ResultWrapper rslt =new Lookup.ResultWrapper();
        
        
        Id [] fixedSearchResults= new Id[1];
        fixedSearchResults[0] = con.Id;
        Test.setFixedSearchResults(fixedSearchResults);
     
        
        String result = Lookup.searchDB(objectName,fld_API_Text,fld_API_Account,fld_API_AccntName,fld_API_Val,lim,fld_API_Search,searchText);
    }
}
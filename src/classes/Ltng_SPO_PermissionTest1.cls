/*
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON><PERSON><PERSON>         Feb 24, 2025            STRY0485454 - Remove SR_testdata SVMXC referencing methods.
 */
@isTest
public class Ltng_SPO_PermissionTest1 {
    @isTest 
    static void unitTest1(){
        Profile testProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        User testUser = new User(
            Alias = 'standt', 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8', 
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US', 
            ProfileId = testProfile.Id, 
            TimezoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>'
        );
        insert testUser;
        
        Case testCase = new Case();
        testCase.OwnerId = UserInfo.getUserId();                                       
        testCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Helpdesk').getRecordTypeId();                                
        testCase.Local_description__c  ='Local_description';              
        testCase.Local_Subject__c = 'Test';                                  
        testCase.ERP_Reference__c = 'HERP_Reference';                        
        testCase.Is_This_a_Complaint__c = 'No';                              
        testCase.Was_anyone_injured__c = 'No';                              
        testCase.Local_Case_Activity__c  ='Local_Case_Activity';
        testCase.Local_Internal_Notes__c   ='Local_Internal_Notes';
        testCase.Is_escalation_to_the_CLT_required__c = 'No';
        testCase.Incident_Workflow_Location__c = 'Simulation';
        testCase.Error_Code__c = 'Hardware';
        insert testCase;
        
        Test.startTest();
        PHI_Log__c testPHI = new PHI_Log__c();
        testPHI.Log_Type__c = 'Case';
        testPHI.Case__c = testCase.Id;
        testPHI.Date_Obtained__c = System.today();
        testPHI.Disposition2__c = 'Regulatory Ownership Assigned and Transferred';
        testPHI.Disposition_Date__c = Date.today();
        testPHI.Director_Dept_Head__c = testUser.Id;
        testPHI.OwnerId = Userinfo.getUserId();
        testPHI.Storage_Location2__c = 'Varian Secure Drop';
        testPHI.Data_Security_Details__c = 'Varian Data Center (CRM, Varian Secure Drop)';
        insert testPHI;
        
        testPHI.SP_Encrypted_Folder_URL__c = '/Shared%20Documents/PHI%20Data/CASE-0000000000/PHI-Log-4013';
        update testPHI;
        
        System.runAs(testUser){
            Ltng_SPO_Permission.addUserToSPO(testPHI.Id, false, 'Customers',testUser.Id, '<EMAIL>') ;
        }
        Test.stopTest();
    }
    
    @isTest 
    static void unitTest2(){
        User currentUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        Group testGroup = [SELECT Id FROM Group WHERE NAME = 'ECT Investigation Group' LIMIT 1];
        GroupMember testGroupMember;

        System.runAs(currentUser){
            /* 
            Group testGroup = new Group(
                Name = 'ECT_Investigation_Group',
                Type = 'Queue'
            );
            insert testGroup;
             */
            testGroupMember = new GroupMember(
                UserOrGroupId = currentUser.Id,
                GroupId = testGroup.Id
            );
            insert testGroupMember;
        }

        Profile testProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        User testUser = new User(
            Alias = 'standt', 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8', 
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US', 
            ProfileId = testProfile.Id, 
            TimezoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>'
        );
        insert testUser;

        Case testCase = new Case();
        testCase.OwnerId = UserInfo.getUserId();                                       
        testCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Helpdesk').getRecordTypeId();                                
        testCase.Local_description__c  ='Local_description';              
        testCase.Local_Subject__c = 'Test';                                  
        testCase.ERP_Reference__c = 'HERP_Reference';                        
        testCase.Is_This_a_Complaint__c = 'No';                              
        testCase.Was_anyone_injured__c = 'No';                              
        testCase.Local_Case_Activity__c  ='Local_Case_Activity';
        testCase.Local_Internal_Notes__c   ='Local_Internal_Notes';
        testCase.Is_escalation_to_the_CLT_required__c = 'No';
        testCase.Incident_Workflow_Location__c = 'Simulation';
        testCase.Error_Code__c = 'Hardware';
        insert testCase;

        Test.startTest();
        PHI_Log__c testPHI = new PHI_Log__c();
        testPHI.Log_Type__c = 'Case';
        testPHI.Case__c = testCase.Id;
        testPHI.Date_Obtained__c = System.today();
        testPHI.Disposition2__c = 'Regulatory Ownership Assigned and Transferred';
        testPHI.Disposition_Date__c = Date.today();
        testPHI.Director_Dept_Head__c = testUser.Id;
        testPHI.OwnerId = testUser.Id;
        testPHI.Storage_Location2__c = 'Varian Secure Drop';
        testPHI.Data_Security_Details__c = 'Varian Data Center (CRM, Varian Secure Drop)';
        insert testPHI;

        testPHI.SP_Encrypted_Folder_URL__c = '/Shared%20Documents/PHI%20Data/CASE-0000000000/PHI-Log-4013';
        update testPHI;

        System.runAs(testUser){
            Ltng_SPO_Permission.addUserToSPO(testPHI.Id, true, 'Customers',testUser.Id, '<EMAIL>') ;
        }
        Test.stopTest();
    }

    @isTest 
    static void unitTest3(){
        User currentUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        Group testGroup = [SELECT Id FROM Group WHERE NAME = 'ECT Investigation Group' LIMIT 1];
        GroupMember testGroupMember;

        System.runAs(currentUser){
            /* 
            Group testGroup = new Group(
                Name = 'ECT_Investigation_Group',
                Type = 'Queue'
            );
            insert testGroup;
             */
            testGroupMember = new GroupMember(
                UserOrGroupId = currentUser.Id,
                GroupId = testGroup.Id
            );
            insert testGroupMember;
        }

        Profile testProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        User testUser = new User(
            Alias = 'standt', 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8', 
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US', 
            ProfileId = testProfile.Id, 
            TimezoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>'
        );
        insert testUser;

        Case testCase = new Case();
        testCase.OwnerId = UserInfo.getUserId();                                       
        testCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Helpdesk').getRecordTypeId();                                
        testCase.Local_description__c  ='Local_description';              
        testCase.Local_Subject__c = 'Test';                                  
        testCase.ERP_Reference__c = 'HERP_Reference';                        
        testCase.Is_This_a_Complaint__c = 'No';                              
        testCase.Was_anyone_injured__c = 'No';                              
        testCase.Local_Case_Activity__c  ='Local_Case_Activity';
        testCase.Local_Internal_Notes__c   ='Local_Internal_Notes';
        testCase.Is_escalation_to_the_CLT_required__c = 'No';
        testCase.Incident_Workflow_Location__c = 'Simulation';
        testCase.Error_Code__c = 'Hardware';
        insert testCase;

        Test.startTest();
        PHI_Log__c testPHI = new PHI_Log__c();
        testPHI.Log_Type__c = 'Case';
        testPHI.Case__c = testCase.Id;
        testPHI.Date_Obtained__c = System.today();
        testPHI.Disposition2__c = 'Regulatory Ownership Assigned and Transferred';
        testPHI.Disposition_Date__c = Date.today();
        testPHI.Director_Dept_Head__c = testUser.Id;
        testPHI.OwnerId = UserInfo.getUserId();
        testPHI.Storage_Location2__c = 'Varian Secure Drop';
        testPHI.Data_Security_Details__c = 'Varian Data Center (CRM, Varian Secure Drop)';
        insert testPHI;

        testPHI.SP_Encrypted_Folder_URL__c = '/Shared%20Documents/PHI%20Data/CASE-0000000000/PHI-Log-4013';
        update testPHI;

        System.runAs(testUser){
            Ltng_SPO_Permission.addUserToSPO(testPHI.Id, false, 'Customers',UserInfo.getUserId(), '<EMAIL>') ;
        }
        Test.stopTest();
    }

    @isTest 
    static void unitTest4(){
        User currentUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        Group testGroup = [SELECT Id FROM Group WHERE NAME = 'ECT Investigation Group' LIMIT 1];
        GroupMember testGroupMember;

        System.runAs(currentUser){
            /* 
            Group testGroup = new Group(
                Name = 'ECT_Investigation_Group',
                Type = 'Queue'
            );
            insert testGroup;
             */
            testGroupMember = new GroupMember(
                UserOrGroupId = currentUser.Id,
                GroupId = testGroup.Id
            );
            insert testGroupMember;
        }

        Profile testProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        User testUser = new User(
            Alias = 'standt', 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8', 
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US', 
            ProfileId = testProfile.Id, 
            TimezoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>'
        );
        insert testUser;

        Case testCase = new Case();
        testCase.OwnerId = UserInfo.getUserId();                                       
        testCase.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('Helpdesk').getRecordTypeId();                                
        testCase.Local_description__c  ='Local_description';              
        testCase.Local_Subject__c = 'Test';                                  
        testCase.ERP_Reference__c = 'HERP_Reference';                        
        testCase.Is_This_a_Complaint__c = 'No';                              
        testCase.Was_anyone_injured__c = 'No';                              
        testCase.Local_Case_Activity__c  ='Local_Case_Activity';
        testCase.Local_Internal_Notes__c   ='Local_Internal_Notes';
        testCase.Is_escalation_to_the_CLT_required__c = 'No';
        testCase.Incident_Workflow_Location__c = 'Simulation';
        testCase.Error_Code__c = 'Hardware';
        insert testCase;

        Test.startTest();
        PHI_Log__c testPHI = new PHI_Log__c();
        testPHI.Log_Type__c = 'Case';
        testPHI.Case__c = testCase.Id;
        testPHI.Date_Obtained__c = System.today();
        testPHI.Disposition2__c = 'Regulatory Ownership Assigned and Transferred';
        testPHI.Disposition_Date__c = Date.today();
        testPHI.Director_Dept_Head__c = testUser.Id;
        testPHI.OwnerId = testGroup.Id;
        testPHI.Storage_Location2__c = 'Varian Secure Drop';
        testPHI.Data_Security_Details__c = 'Varian Data Center (CRM, Varian Secure Drop)';
        insert testPHI;

        testPHI.SP_Encrypted_Folder_URL__c = '/Shared%20Documents/PHI%20Data/CASE-0000000000/PHI-Log-4013';
        update testPHI;

        System.runAs(testUser){
            Ltng_SPO_Permission.addUserToSPO(testPHI.Id, false, 'Customers',testUser.Id, '<EMAIL>') ;
        }
        Test.stopTest();
    }
}
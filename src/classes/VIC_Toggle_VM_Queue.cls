public class VIC_Toggle_VM_Queue implements Queueable, Database.AllowsCallouts {
    
    List<VIC_Order__c> vcList;
    string status;
    public VIC_Toggle_VM_Queue(List<VIC_Order__c> vcList,string status) {
        this.vcList = vcList;
        this.status= status;         
    }
    
    public void execute(QueueableContext context) {
        //List<VIC_Order__c> vicOrderLst = [Select Id, resourceGroupName__c, Azure_Username__c, Current_User_IP_Address__c, 
        //                                  Region__c, VM_Environment_Type__c, VM_Name__c From VIC_Order__c Where Id = 'aEZ0h000000H5Zb'];
        String body = '';
        for(VIC_Order__c vc: this.vcList) {
            Http http = new Http();
            HttpRequest request = new HttpRequest();
            if(this.status == 'On') {
                request.setEndpoint(Label.VIC_Start_Logic_App_Trigger_URL);
            } else if(this.status == 'Off') {
                request.setEndpoint(Label.VIC_Stop_Logic_App_Trigger_URL);
            }
            request.setMethod('POST');
            //request.setMethod('GET');
            request.setHeader('Content-Type', 'application/xml');
            //request.setHeader('Content-Type', 'application/json');
            
            body = '<?xml version="1.0" encoding="UTF-8"?>'+
                    '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">'+
                     '<soapenv:Body>'+
                      '<notifications xmlns="http://soap.sforce.com/2005/09/outbound">'+
                       '<Notification>'+
                        '<sObject xsi:type="sf:VIC_Order__c" xmlns:sf="urn:sobject.enterprise.soap.sforce.com">'+
                         '<sf:resourceGroupName__c>'+vc.resourceGroupName__c+'</sf:resourceGroupName__c>'+
                         '<sf:Id>'+vc.Id+'</sf:Id>'+
                         '<sf:Azure_Username__c>'+vc.Azure_Username__c+'</sf:Azure_Username__c>'+
                         '<sf:Current_User_IP_Address__c>'+vc.Current_User_IP_Address__c+'</sf:Current_User_IP_Address__c>'+
                         '<sf:Region__c>'+vc.Region__c+'</sf:Region__c>'+
                         '<sf:VM_Environment_Type__c>'+vc.VM_Environment_Type__c+'</sf:VM_Environment_Type__c>'+
                         '<sf:VM_Name__c>'+vc.VM_Name__c+'</sf:VM_Name__c>'+
                        '</sObject>'+
                       '</Notification>'+
                      '</notifications>'+
                     '</soapenv:Body>'+
                    '</soapenv:Envelope>';
            
            
            request.setBody(body);
            try {
                HttpResponse response = http.send(request);
                System.debug(response);
            } catch(Exception e) {
                System.debug('Calling Rest API Exception===='+String.valueOf(e));
            }
        }
    }
}
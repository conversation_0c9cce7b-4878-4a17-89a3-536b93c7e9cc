/**
 *  Class Name: TaskRayCustomScheduledEmailBatchTest
 *  Description: This is a test class for TaskRayCustomScheduledEmailBatch
 *  Company: Varian
 *  CreatedDate: Mar 23, 2021
 *  Test class for : TaskRayCustomScheduledEmailBatch
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON> Lin        Mar 23, 2021            Original Version
 */
@isTest
public class TaskRayCustomScheduledEmailBatchTest {
	@testSetup static void methodName() {
        TASKRAY__Project__c template;
        TASKRAY__trTaskGroup__c TG1;
        TASKRAY__Project_Task__c task1;
        TASKRAY__trContributor__c trUser;
        
        template = new TASKRAY__Project__c();
        template.Name = 'Primary Template';
        template.TASKRAY__trTemplate__c = true;
        insert template;
        
        TG1 = new TASKRAY__trTaskGroup__c();
        TG1.Name = 'TG1';
        TG1.TASKRAY__Project__c = template.Id;
        TG1.TASKRAY__trStartDate__c = Date.newInstance(2020, 2, 17);
        TG1.TASKRAY__trEndDate__c = Date.newInstance(2020, 5 , 17);
        TG1.Task_Group_Linked_To_Start__c = true;
        TG1.TASKRAY__trSortOrder__c = 0;
        insert TG1;
        
        Account a = VFSL_TestDataUtility.createAccount('Test Account');
        insert a;
        Contact c = createContact(a.id);
        insert c;
        User usr;
        usr = createUser('Test User1',c);
        insert usr;
       
        task1 = new TASKRAY__Project_Task__c();
        task1.Name = 'Task 1';
        task1.TASKRAY__trTaskGroup__c = TG1.Id;
        task1.TASKRAY__Project__c = template.Id;
        task1.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        task1.TASKRAY__trProjectSortOrder__c = 1;
        task1.Key_Milestone_Task__c = true;
        task1.OwnerId = usr.Id;
        insert task1;
        
        trUser = new TASKRAY__trContributor__c();
        trUser.TASKRAY__User__c = usr.Id;
        trUser.TASKRAY__Project__c = template.Id;
        insert trUser;
        
    }
    
    public static User createUser(String name,Contact c) {
        User user = New User();
        user.ProfileId = [Select Id from Profile where name = 'VMS MyVarian - Customer User'].Id;
        String orgId = UserInfo.getOrganizationId();
        Integer randomInt = Integer.valueOf(math.rint(math.random()*1000000));
        String uniqueName = orgId + randomInt;
        user.LastName = name+'Bond'+ uniqueName;
        user.Email = uniqueName+'<EMAIL>';
        user.Username = 'jamesBond'+randomInt+'@gmail.com';
        user.CompanyName = 'test';
        user.Title = 'title';
        user.Alias = 'Agent007';
        user.TimeZoneSidKey = 'America/Los_Angeles';
        user.EmailEncodingKey = 'UTF-8';
        user.LanguageLocaleKey = 'en_US';
        user.LocaleSidKey = 'en_Us';
        user.contactId = c.id;
        return user;
    }
    
     public static Contact createContact(Id accountId) {
        Contact contactObj = New Contact();
        contactObj.LastName = 'Bond';
        contactObj.FirstName = 'James';
        contactObj.Email = '<EMAIL>';
        contactObj.Functional_Role__c = 'Consultant';
        contactObj.AccountId = accountId;
        contactObj.Inactive_Contact__c = false; 
        return contactObj;
    }
    
    private static testMethod void batchTest(){
        Test.StartTest();
        
        TaskRayCustomScheduledEmailBatch b = new TaskRayCustomScheduledEmailBatch();
        database.executeBatch(b, 10);
        
        Test.stopTest();
    }
}
@isTest
public class APTS_OpportunityTriggerHandlerTest{

@isTest
public static void testClearAgreementContainer() {
       User user1 = APTS_TestDataUtility.createUser('James Bond');
       user1.Department = 'US CORP Legal Operations';
       insert user1;
          
        Account acc1 = APTS_TestDataUtility.createAccount('Test Account');
        insert acc1;
        
        Contact cont1 = APTS_TestDataUtility.createContact(acc1.id);
        insert cont1;
        
                 
        //Inserting Agreement Records
        Apttus__APTS_Agreement__c agreementObj1 = new Apttus__APTS_Agreement__c();
        agreementObj1.Name = 'Test MSA1';
        Id agrRecordTypeId = Schema.SObjectType.Apttus__APTS_Agreement__c.getRecordTypeInfosByName().get('Umbrella Agreement').getRecordTypeId();
        agreementObj1.RecordTypeId = agrRecordTypeId;
        agreementObj1.Apttus__Contract_Start_Date__c = System.Today();
        //agreementObj1.Apttus__Contract_End_Date__c = System.Today()+365;
        agreementObj1.Data_Rights__c = 'No';
        agreementObj1.Electronic_or_Physical_File__c = 'Physical';
        insert agreementObj1;
        System.assert(agreementObj1.Id != NULL);
        
        List<Opportunity> opportunityList = new List<Opportunity>();
        Opportunity oppty1 = APTS_TestDataUtility.createOpportunity(acc1.id);
        oppty1.Umbrella_Agreement__c = agreementObj1.id;
        oppty1.Tender__c = 'Yes';
        insert oppty1 ;
        
        Opportunity clonedoppty = oppty1.clone(false, false, false, false); 
        clonedoppty.Id = oppty1.Id; 
        Update clonedoppty; 
        
        opportunityList.add(oppty1);
        delete oppty1;
        
        }
}
/**
 *  Class Name: TaskRayTGConstructor2Test
 *  Description: This is a test class for TaskRayTGConstructor2
 *  Company: Varian FSL
 *  CreatedDate: Dec 3, 2019
 *  Test class for : TaskRayTGConstructor2
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON> Lin        Dec 09, 2020            Original Version
 */
@isTest
public class TaskRayTGConstructor2Test {
    
    @testSetup static void methodName() {
        User u = [Select ID, TaskRay_Template_Designer__c  From User Where ID=:USerInfo.getUserId()];
        u.TaskRay_Template_Designer__c = true;
        update u;
        
 		TASKRAY__Project__c template;
        TASKRAY__trTaskGroup__c TG1;
        TASKRAY__trTaskGroup__c TG2;
    	TASKRAY__trTaskGroup__c TG3;
        TASKRAY__trTaskGroup__c TG4;
        TASKRAY__trTaskGroup__c TG5;
        TASKRAY__trTaskGroup__c TG6;
        TASKRAY__trTaskGroup__c TG7;
    	TASKRAY__Project__c stitchTemplate;
    	TASKRAY__Project_Task__c task1;
        TASKRAY__Project_Task__c task2;
    	TASKRAY__Project_Task__c predTask;
    	TASKRAY__Project_Task__c succTask;
    	TASKRAY__trDependency__c dep;
        
        template = new TASKRAY__Project__c();
        template.Name = 'Primary Template';
        template.TASKRAY__trTemplate__c = true;
        insert template;
        
        TG1 = new TASKRAY__trTaskGroup__c();
        TG1.Name = 'TG1';
        TG1.TASKRAY__Project__c = template.Id;
        TG1.TASKRAY__trStartDate__c = Date.newInstance(2020, 2, 17);
        TG1.TASKRAY__trEndDate__c = Date.newInstance(2020, 5 , 17);
        TG1.Task_Group_Linked_To_Start__c = true;
        TG1.TASKRAY__trSortOrder__c = 0;
        insert TG1;
        
        TG6 = new TASKRAY__trTaskGroup__c();
        TG6.Name = 'TG6';
        TG6.TASKRAY__Project__c = template.Id;
        TG6.TASKRAY__trStartDate__c = Date.newInstance(2020, 8, 19);
        TG6.TASKRAY__trEndDate__c = Date.newInstance(2020, 9 , 18);
        TG6.Task_Group_Linked_To_Start__c = true;
        TG6.TASKRAY__trSortOrder__c = 1;
        TG6.Required_TaskGroupOption__c = true;
        TG6.Task_Group_Category__c = 'Choice2';
        insert TG6;
        
        TG7 = new TASKRAY__trTaskGroup__c();
        TG7.Name = 'TG7';
        TG7.TASKRAY__Project__c = template.Id;
        TG7.TASKRAY__trStartDate__c = Date.newInstance(2020, 10, 19);
        TG7.TASKRAY__trEndDate__c = Date.newInstance(2020, 11 , 18);
        TG7.Task_Group_Linked_To_Start__c = true;
        TG7.TASKRAY__trSortOrder__c = 2;
        TG7.Required_TaskGroupOption__c = true;
        TG7.Task_Group_Category__c = 'Choice2';
        insert TG7;
        
        
        
        task1 = new TASKRAY__Project_Task__c();
        task1.Name = 'Task 1';
        task1.TASKRAY__trTaskGroup__c = TG1.Id;
        task1.TASKRAY__Project__c = template.Id;
        task1.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        task1.TASKRAY__trProjectSortOrder__c = 1;
        task1.Key_Milestone_Task__c = true;
        insert task1;
        
        
        stitchTemplate = new TASKRAY__Project__c();
        stitchTemplate.Name = 'Stitch Template';
        stitchTemplate.TASKRAY__trTemplate__c = true;
        insert stitchTemplate;
        
        
        TG2 = new TASKRAY__trTaskGroup__c();
        TG2.Name = 'TG2';
        TG2.TASKRAY__Project__c = stitchTemplate.Id;
        TG2.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        TG2.TASKRAY__trEndDate__c = Date.newInstance(2020, 6 , 18);
        TG2.Task_Group_Linked_To_Start__c = true;
        TG2.TASKRAY__trSortOrder__c = 0;
        TG2.Required_TaskGroupOption__c = true;
        TG2.Task_Group_Category__c = 'Choice1';
        insert TG2;
        
        task2 = new TASKRAY__Project_Task__c();
        task2.Name = 'Task 2';
        task2.TASKRAY__trTaskGroup__c = TG2.Id;
        task2.TASKRAY__Project__c = stitchTemplate.Id;
        task2.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        task2.TASKRAY__trProjectSortOrder__c = 1;
        task2.Key_Milestone_Task__c = true;
        insert task2;
        
        TG3 = new TASKRAY__trTaskGroup__c();
        TG3.Name = 'TG3';
        TG3.TASKRAY__Project__c = stitchTemplate.Id;
        TG3.TASKRAY__trStartDate__c = Date.newInstance(2020, 6, 19);
        TG3.TASKRAY__trEndDate__c = Date.newInstance(2020, 7 , 18);
        TG3.Task_Group_Linked_To_Start__c = true;
        TG3.TASKRAY__trSortOrder__c = 1;
        TG3.Required_TaskGroupOption__c = true;
        TG3.Task_Group_Category__c = 'Choice2';
        insert TG3;
        
        TG4 = new TASKRAY__trTaskGroup__c();
        TG4.Name = 'TG4';
        TG4.TASKRAY__Project__c = stitchTemplate.Id;
        TG4.TASKRAY__trStartDate__c = Date.newInstance(2020, 7, 19);
        TG4.TASKRAY__trEndDate__c = Date.newInstance(2020, 8 , 18);
        TG4.Task_Group_Linked_To_Start__c = true;
        TG4.TASKRAY__trSortOrder__c = 2;
        TG4.Required_TaskGroupOption__c = true;
        TG4.Task_Group_Category__c = 'Choice1';
        insert TG4;
        
        TG5 = new TASKRAY__trTaskGroup__c();
        TG5.Name = 'TG5';
        TG5.TASKRAY__Project__c = stitchTemplate.Id;
        TG5.TASKRAY__trStartDate__c = Date.newInstance(2020, 8, 19);
        TG5.TASKRAY__trEndDate__c = Date.newInstance(2020, 9 , 18);
        TG5.Task_Group_Linked_To_Start__c = true;
        TG5.TASKRAY__trSortOrder__c = 3;
        TG5.Required_TaskGroupOption__c = true;
        TG5.Task_Group_Category__c = 'Choice2';
        insert TG5;

        predTask = new TASKRAY__Project_Task__c();
        predTask.Name = 'Task 1';
        predTask.TASKRAY__trTaskGroup__c = TG2.Id;
        predTask.TASKRAY__Project__c = stitchTemplate.Id;
        predTask.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        insert predTask;
        
        succTask = new TASKRAY__Project_Task__c();
        succTask.Name = 'Task 2';
        succTask.TASKRAY__trTaskGroup__c = TG3.Id;
        succTask.TASKRAY__Project__c = stitchTemplate.Id; // tg2 has to be in TGIDs
        succTask.TASKRAY__trStartDate__c = Date.newInstance(2020, 6, 19);
        succTask.TASKRAY__Deadline__c = Date.newInstance(2020, 6, 20);
		insert succTask;
        
        dep = new TASKRAY__trDependency__c();
        dep.TASKRAY__trPredecessorTask__c = predTask.Id;
        dep.TASKRAY__trSuccessorTask__c = succTask.Id;
        dep.TASKRAY__trLag__c = 2;
        dep.TASKRAY__trDependencyType__c = 'Start-To-Start';
        insert dep;
	}
    
    @isTest static void sortTGTest() {
        
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
       
        Test.startTest();
        
		List<TaskRayTGConstructor2.FlowInputs> FlowInputsList = new List<TaskRayTGConstructor2.FlowInputs>();
        TaskRayTGConstructor2.FlowInputs FlowInputs = new TaskRayTGConstructor2.FlowInputs();
        FlowInputs.templateId = template.Id;
        FlowInputs.sortTG = true;
        FlowInputsList.add(FlowInputs);
        TaskRayTGConstructor2.constructTG(FlowInputsList); // test the sortTG function
        
        Test.stopTest();
      
    }
    
    /*@isTest static void getPrimaryTGListTest() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        TASKRAY__trTaskGroup__c TG6 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG6' LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG1.Id);
        TGIDS.add(TG6.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG1.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        Test.startTest();
        
        //TaskRayTGConstructor2.getPrimaryTGList(TGIDS, projectStart, ClonedIDs, ClonedQty, template.Id);
        
        Test.stopTest();
    }*/
    @isTest static void getPrimaryTGListTest2() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG2.Id);
        TGIDS.add(TG3.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG2.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        Test.startTest();
        
        TaskRayTGConstructor2.createPrimaryTGList2(TGIDS, projectStart, milestoneDate, false, ClonedIDs, ClonedQty, stitchTemplate.Id);
        
        Test.stopTest();
    }
    @isTest static void getPrimaryTGListTest3() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        TASKRAY__trDependency__c dep = [SELECT Id,TASKRAY__trDependencyType__c FROM TASKRAY__trDependency__c LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG2.Id);
        TGIDS.add(TG3.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG2.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        dep.TASKRAY__trDependencyType__c = 'Finish-To-Start';
        update dep;
        
        Test.startTest();
        
        TaskRayTGConstructor2.createPrimaryTGList2(TGIDS, projectStart, milestoneDate, false, ClonedIDs, ClonedQty, stitchTemplate.Id);
        
        Test.stopTest();
    }
    @isTest static void getPrimaryTGListTest4() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        TASKRAY__trDependency__c dep = [SELECT Id,TASKRAY__trDependencyType__c FROM TASKRAY__trDependency__c LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG2.Id);
        TGIDS.add(TG3.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG2.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        dep.TASKRAY__trDependencyType__c = 'Start-To-Finish';
        update dep;
        
        Test.startTest();
        
        TaskRayTGConstructor2.createPrimaryTGList2(TGIDS, projectStart, milestoneDate, false, ClonedIDs, ClonedQty, stitchTemplate.Id);
        
        Test.stopTest();
    }
    @isTest static void getPrimaryTGListTest5() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        TASKRAY__trDependency__c dep = [SELECT Id,TASKRAY__trDependencyType__c FROM TASKRAY__trDependency__c LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG2.Id);
        TGIDS.add(TG3.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG2.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        dep.TASKRAY__trDependencyType__c = 'Finish-To-Finish';
        update dep;
        
        Test.startTest();
        
        TaskRayTGConstructor2.createPrimaryTGList2(TGIDS, projectStart, milestoneDate, false, ClonedIDs, ClonedQty, stitchTemplate.Id);
        
        Test.stopTest();
    }
    @isTest static void getPrimaryTGListTest6() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        TASKRAY__trDependency__c dep = [SELECT Id,TASKRAY__trDependencyType__c FROM TASKRAY__trDependency__c LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG2.Id);
        TGIDS.add(TG3.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG2.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        dep.TASKRAY__trDependencyType__c = 'Finish-To-Finish';
        update dep;
        
        Test.startTest();
        
        TaskRayTGConstructor2.createPrimaryTGList2(TGIDS, projectStart, milestoneDate, true, ClonedIDs, ClonedQty, stitchTemplate.Id);
        
        Test.stopTest();
    }
    @isTest static void getStitchTGListTest() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        TASKRAY__trTaskGroup__c TG2 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG2' LIMIT 1];
        TASKRAY__trTaskGroup__c TG3 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG3' LIMIT 1];
        
        List<Id> TGIDS2 = new List<Id>();
        TGIDS2.add(TG2.Id);
        TGIDS2.add(TG3.Id);
        Date projectStart = Date.newInstance(2020, 2, 17);
        
        List<String>ClonedTGIds = new List<String>();
        ClonedTGIds.add(TG2.Id);
        ClonedTGIds.add(TG3.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        ClonedQty.add(2);
        
        Test.startTest();
        
        TaskRayTGConstructor2.getStitchTGList3(TGIDS2, projectStart, ClonedTGIds, ClonedQty, template.Id, stitchTemplate.Id);
        
        Test.stopTest();
    }/*
    @isTest static void getPrimaryTGListWithMilestoneTest1(){
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG1.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG1.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        Test.startTest();
        
        TaskRayTGConstructor2.getPrimaryTGListWithMilestone(TGIDs, projectStart, milestoneDate, ClonedIDs, ClonedQty, template.Id);
        
        Test.stopTest();
        
    }*/
	/*
    @isTest static void getPrimaryTGListWithMilestoneTest2(){
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        
        TG1.Linked_To_Key_Milestone__c = true;
        TG1.Task_Group_Linked_To_Start__c = false;
        update TG1;
        
        List<Id> TGIDS = new List<Id>();
        TGIDS.add(TG1.Id);
        
        List<String> ClonedIDs = new List<String>();
        ClonedIDs.add(TG1.Id);
        
        List<Decimal> ClonedQty = new List<Decimal>();
        ClonedQty.add(2);
        Date projectStart = Date.newInstance(2020, 2, 17);
        Date milestoneDate = Date.newInstance(2021, 7, 20);
        
        Test.startTest();
        
        TaskRayTGConstructor2.getPrimaryTGListWithMilestone(TGIDs, projectStart, milestoneDate, ClonedIDs, ClonedQty, template.Id);
        
        Test.stopTest();
        
        TG1.Linked_To_Key_Milestone__c = false;
        TG1.Task_Group_Linked_To_Start__c = true;
        update TG1;
        
    }*/
    @isTest static void createTGArraysTest() {
        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
		 List<TASKRAY__trTaskGroup__c> TGCategories = [SELECT Name, Task_Group_Category__c FROM TASKRAY__trTaskGroup__c
                                                   WHERE TASKRAY__Project__c =: stitchTemplate.Id AND Task_Group_Category__c !=: null AND Required_TaskGroupOption__c =: true ORDER BY Task_Group_Category__c];        

       	System.debug('TGCategories: '+TGCategories);
        
        List<TaskRayTGConstructor2.FlowInputs> FlowInputsList = new List<TaskRayTGConstructor2.FlowInputs>();
        TaskRayTGConstructor2.FlowInputs FlowInputs = new TaskRayTGConstructor2.FlowInputs();
        FlowInputs.templateId = stitchTemplate.Id;
        FlowInputs.sortTG = false;
        FlowInputs.checkMultiTG = false;
        FlowInputs.StitchCategories = true;
        FlowInputsList.add(FlowInputs);
        
        
        Test.startTest();
        TaskRayTGConstructor2.constructTG(FlowInputsList);
        
        Test.stopTest();
    }
    @isTest static void checkMultiInstanceTGSTest1() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        TG1.Multi_Instance_Task_Group__c = false;
        update TG1;
        
        List<TaskRayTGConstructor2.FlowInputs> FlowInputsList = new List<TaskRayTGConstructor2.FlowInputs>();
        TaskRayTGConstructor2.FlowInputs FlowInputs = new TaskRayTGConstructor2.FlowInputs();
        FlowInputs.templateId = template.Id;
        FlowInputs.sortTG = false;
        FlowInputs.checkMultiTG = true;
        FlowInputsList.add(FlowInputs);
        
        
        Test.startTest();
        TaskRayTGConstructor2.constructTG(FlowInputsList);
        
        Test.stopTest();
    }
    @isTest static void checkMultiInstanceTGSTest2() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        TG1.Multi_Instance_Task_Group__c = true;
        update TG1;
        
        Test.startTest();
        
        TaskRayTGConstructor2.checkMultiInstanceTG(template.Id);
        
        Test.stopTest();
    }
    
    @isTest static void populateTemplateTaskName() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        TASKRAY__trTaskGroup__c TG1 = [SELECT Id,TASKRAY__trSortOrder__c FROM TASKRAY__trTaskGroup__c WHERE Name ='TG1' LIMIT 1];
        TG1.Multi_Instance_Task_Group__c = true;
        update TG1;
        

        TASKRAY__Project__c stitchTemplate = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Stitch Template' LIMIT 1];
        
        Test.startTest();
        List<TaskrayTemplateTaskName.FlowInputs> FlowInputsList = new List<TaskrayTemplateTaskName.FlowInputs>();
        TaskrayTemplateTaskName.FlowInputs FlowInputs = new TaskrayTemplateTaskName.FlowInputs();
        FlowInputs.currentProjectRecordId = template.Id;
        FlowInputs.sourceTemplateIdList = new List<Id>{template.Id, stitchTemplate.Id};
        FlowInputsList.add(FlowInputs);
        
        
        TaskrayTemplateTaskName.populateTemplateTaskName(FlowInputsList);
        
        Test.stopTest();
    }
    
}
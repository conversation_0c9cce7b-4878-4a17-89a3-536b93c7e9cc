public  class DisplayThumbnailImagesController {  
    private final Event_Webinar__c event;
    //Parent Contact  
    private String Product2Id ;  
    //Image selected from UI  
    public String selectedImage {get; set;}  
      
    public DisplayThumbnailImagesController(ApexPages.StandardController stdController)  
    {         
        this.event = (Event_Webinar__c)stdController.getRecord();
        //Fetching contact Id  
        Product2Id = ApexPages.CurrentPage().getParameters().get('Id') ; 
        System.debug('Product2Id' + Product2Id);
        selectedImage = '' ;  
    }  
      
    public boolean validateImage(String image)  
    {  
        String Regex = '([^\\s]+(\\.(?i)(jpg|jpeg|png|gif|bmp))$)';  
        Pattern MyPattern = Pattern.compile(Regex);  
        Matcher MyMatcher = MyPattern.matcher(image);  
        return MyMatcher.matches() ;  
    }  
      
    public List<SelectOption> getItems()  
    {  
        List<SelectOption> options = new List<SelectOption>();   
          
        //All attachments related to contact  
        List<Attachment> attachLst = [SELECT Id , Name FROM Attachment WHERE ParentId =: Product2Id] ; 
        System.debug('attachment List ===>'+attachLst);
          
        //Creating option list  
        for(Attachment att : attachLst)  
        {  
            //String imageName = att.name.replaceAll('\\s+','|') ;  
            //if(validateImage(imageName))  
            //{  
                options.add(new SelectOption(att.Id , att.Name));  
            //}  
        }  
        System.debug('options ===>'+options);
        return options ;  
    }  
      
    public PageReference SaveImage()  
    {  
        //Contact to update  
        List<Event_Webinar__c> conToUpdate = new List<Event_Webinar__c>() ;  
        conToUpdate = [select id,Store_Thumbnail_Image_Url__c from Event_Webinar__c where id =: Product2Id] ;  
  
        //Inserting image parth  
        if(conToUpdate.size() > 0)  
        {  
            conToUpdate[0].Store_Thumbnail_Image_Url__c = '/servlet/servlet.FileDownload?file=' + selectedImage ;  
            update conToUpdate[0] ;  
        }  
          string recID = ApexPages.CurrentPage().getParameters().get('id');
        Pagereference pr = new PageReference('/'+recID);
        return pr ;
    }  
}
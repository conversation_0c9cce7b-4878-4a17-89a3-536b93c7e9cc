@isTest
public class QuoteTriggerHandlerTest {
    
    static testMethod void testUpdateNationalDistributorName() {
        Test.startTest();
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
        
        Contact con = [SELECT Id FROM Contact WHERE AccountId =:acct.Id];
        
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Tender__c = 'Yes';
        insert opp;
        
        BigMachines__Quote__c quote = TestUtils.getQuote();
        quote.BigMachines__Account__c = acct.Id;
        quote.BigMachines__Opportunity__c = opp.Id;
        quote.National_Distributor__c = '121212';


        
        QuoteTriggerHandler.updateNationalDistributorName(new List<BigMachines__Quote__c>{quote});
        
        System.assertEquals(quote.National_Distributor__c,'Test Account');
        Test.stopTest();
    }
    
    static testMethod void testUpdateNationalDistributorNameNegative() {
        Test.startTest();
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
        
        Contact con = [SELECT Id FROM Contact WHERE AccountId =:acct.Id];
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Tender__c = 'Yes';
        insert opp;
        
        BigMachines__Quote__c bmQuote = TestUtils.getQuote();
        bmQuote.BigMachines__Account__c = acct.Id;
        bmQuote.BigMachines__Opportunity__c = opp.Id;
        bmQuote.National_Distributor__c = '';
        
        BigMachines__Quote__c bmQuote1 = TestUtils.getQuote();
        bmQuote1.BigMachines__Account__c = acct.Id;
        bmQuote1.BigMachines__Opportunity__c = opp.Id;
        bmQuote1.National_Distributor__c = 'Random Test';
        bmQuote1.Current_Approvers__c = 'vmsaraut';

        
        List<BigMachines__Quote__c> sfQuotes = new List<BigMachines__Quote__c>{bmQuote,bmQuote1};
            
            QuoteTriggerHandler.updateNationalDistributorName(sfQuotes);
        
        System.assertEquals(sfQuotes[0].National_Distributor__c,'');
        System.assertEquals(sfQuotes[1].National_Distributor__c,'Random Test');
        
        
        insert sfQuotes;
        
       	bmQuote1.Interface_Status__c = 'Process';
        update bmQuote1;
        Test.stopTest();
    }
    
    static testMethod void testSetQuoteFields(){
        Test.startTest();
        Lookup_Values__c lookupValue = new Lookup_Values__c();
        lookupValue.Name = 'Letter of Credit(Brachy only-Handle this)';
        insert lookupValue;
        
        
        List<BigMachines__Quote__c> bigMachinesQuotes = [
            SELECT Id, BigMachines__Status__c,BigMachines__Is_Primary__c,BigMachines__Opportunity__c,Net_Booking_Value__c,First_Epot_Submitted_Date__c,OMNI_Quote_Number__c,Is_Replaced__c,Quote_Reference__c,Interface_Status__c,Message_Service__c,Booking_Message__c,BigMachines__Account__c,Price_Group__c,Payment_Terms__c,
            Order_Type__c,SAP_Prebooked_Service__c,SAP_Prebooked_Sales__c,SalesPaymentTerms__c,Letter_of_Credit_Required__c, Subscription_Status__c, Quote_Type__c,
            Initial_Order_Status__c, eCommerce_Quote__c, BigMachines__Total_Amount__c, CurrencyISOCode
            FROM BigMachines__Quote__c
        ];	
        
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
       // acct.Sub_Region__c = 'China Admin';
       // update acct;
        
      
        Contact con = TestUtils.getContact();
        
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Unified_Funding_Status__c = '100%';
        //opp.Account_Country__c = 'USA';
        opp.Tender__c = 'Yes';
        opp.Quote_Replaced__c = bigMachinesQuotes[0].Id;
        insert opp;
        
        
        
        List<BigMachines__Quote__c> bigMachinesQuotes1 = new List<BigMachines__Quote__c>();
        Map<Id,BigMachines__Quote__c> oldQuoteMap1 = new Map<Id,BigMachines__Quote__c>();


        for (Integer i = 0; i < 3; i++) {
    bigMachinesQuotes1.add(new BigMachines__Quote__c(
        Name = 'Test Quote ' + i,
        BigMachines__Opportunity__c = opp.Id,
        BigMachines__Account__c = acct.Id,
        OMNI_Quote_Number__c = '123456',
        Order_Type__c = 'Services',
        SAP_Prebooked_Service__c = 'TestValue',
        Quote_Status__c = 'DRAFT',
        Ship_To_Country__c = 'USA',
        eCommerce_Quote__c = true,
        Total_Amount__c	 = 10000,
        Is_Replaced__c = false,
        BigMachines__Is_Primary__c = true,
        Booking_Message__c = 'SUCCESS'
       
    ));
}
insert bigMachinesQuotes1;
        oldQuoteMap1.put(bigMachinesQuotes1[0].Id,bigMachinesQuotes1[0]);

        
        Id standardPricebookId = Test.getStandardPricebookId();
        
        	Product2 varianProduct1 = new Product2();
            varianProduct1.Name = 'Sample Service Product1';
            varianProduct1.IsActive = true;
            varianProduct1.ProductCode = 'Test Service Code1';
            
            Product2 varianProduct2 = new Product2();
            varianProduct2.Name = 'Sample Service Product2';
            varianProduct2.IsActive = true;
            varianProduct2.ProductCode = 'Test Service Code2';
            insert new List<Product2>{varianProduct1, varianProduct2};
            
            List<Product2> products = [SELECT Id,Relevant_Non_Relevant__c FROM Product2 WHERE Id=:varianProduct1.Id Or Id=:varianProduct2.Id];
            products[0].Relevant_Non_Relevant__c = 'Relevant';
            products[1].Relevant_Non_Relevant__c = 'Relevant';
            update products;
        
        PricebookEntry pbEntryService = new PricebookEntry();
        pbEntryService.Product2Id = varianProduct1.Id;
        pbEntryService.Pricebook2Id = standardPricebookId;
        pbEntryService.UnitPrice = 111;
        pbEntryService.CurrencyISOCode = 'USD';
        pbEntryService.IsActive = true;
        
        PricebookEntry pbEntryService1 = new PricebookEntry();
        pbEntryService1.Product2Id = varianProduct2.Id;
        pbEntryService1.Pricebook2Id = standardPricebookId;
        pbEntryService1.UnitPrice = 111;
        pbEntryService1.CurrencyISOCode = 'USD';
        pbEntryService1.IsActive = true;
        insert new List<PricebookEntry>{pbEntryService,pbEntryService1};

        
        BigMachines__Configuration_Record__c  objSite = TestUtils.createBMISite();

        
        
        BigMachines__Quote_Product__c quoteProduct = new BigMachines__Quote_Product__c();
        quoteProduct.BigMachines__Quote__c = bigMachinesQuotes1[0].Id;
        quoteProduct.BigMachines__Product__c = varianProduct1.Id;
        quoteProduct.Header__c = true;
        quoteProduct.Name = 'Test QProduct1';
        quoteProduct.QP_Product_Model__c = 'ABDC';
        quoteProduct.QP_Product_Family__c= 'System Solutions';
        quoteProduct.BigMachines__Quantity__c = 1;
        quoteProduct.BigMachines__Sales_Price__c =5;
        quoteProduct.Forecast_Net_Booking_Value__c = 20000;

        //insert quoteProduct;
        
        BigMachines__Quote_Product__c quoteProduct2 = new BigMachines__Quote_Product__c();
        quoteProduct2.BigMachines__Quote__c = bigMachinesQuotes1[0].Id;
        quoteProduct2.BigMachines__Quote__c = varianProduct2.Id;
        quoteProduct2.Name = 'Test QProduct2';
        quoteProduct2.QP_Product_Model__c = 'ABDC';
        quoteProduct2.QP_Product_Family__c= 'System Solutions';
        quoteProduct2.BigMachines__Quantity__c = 1;
        quoteProduct2.BigMachines__Sales_Price__c =5;
        quoteProduct2.Forecast_Net_Booking_Value__c = 20000;
        //insert quoteProduct2;
        //insert new List<BigMachines__Quote_Product__c>{quoteProduct, quoteProduct2};
       
        
        Map<Id,BigMachines__Quote__c> oldQuoteMap = new Map<Id,BigMachines__Quote__c>();
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            bmQuote.BigMachines__Opportunity__c = opp.Id;
            bmQuote.OMNI_Quote_Number__c = '123456';
            bmQuote.Order_Type__c = 'Services';
            bmQuote.Quote__c = bigMachinesQuotes[0].Id;

			bmquote.Booking_Message__c = 'SUCCESS';
            
            
            

            //dQuoteMap.put(bmQuote.Id,bmQuote);
        }
        update bigMachinesQuotes1; 
        
        BigMachines__Quote__c bmquoteNew = bigMachinesQuotes[0];
        
        for (BigMachines__Quote__c bm : bigMachinesQuotes1) {
    	oldQuoteMap.put(bm.Id, bm.clone(false, true, false, false));
		}
        
        for (BigMachines__Quote__c q : bigMachinesQuotes1) {
   
            q.Quote__c = bigMachinesQuotes[0].Id;
            q.Total_Amount__c = 15000;
            q.Quote_Status__c = 'DRAFT';
            q.BigMachines__Is_Primary__c = false;
	}
        update bigMachinesQuotes1; 
       //QuoteTriggerHandler.addOnQuoteToContract(oldQuoteMap);

        
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Process';
                bmQuote.OMNI_Quote_Number__c = 'UPDATED-OMNI-789';
                bmQuote.SAP_Prebooked_Sales__c = '12345';
                bmquote.Quote_Status__c = 'SUCCESS';
                
            }else if(bmQuote.Order_Type__c == 'Services'){
                bmQuote.Interface_Status__c = 'Processing';
                //bmQuote.Message_Service__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Service__c = '12345';
                bmQuote.OMNI_Quote_Number__c = 'UPDATED-OMNI-789';

            }else{
                bmQuote.Interface_Status__c = 'Processed';
                //bmQuote.Message_Service__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Service__c = '12345';
                //bmQuote.Booking_Message__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Sales__c = '1234533';
                bmQuote.OMNI_Quote_Number__c = 'UPDATED-OMNI-789';

            }
            
            bmQuote.BigMachines__Status__c = 'Approved';
        }
        
		
       update bigMachinesQuotes1;
        /*oldQuoteMap1.put(bigMachinesQuotes1[0].Id, bigMachinesQuotes1[0]);
         bigMachinesQuotes1[0].Interface_Status__c = 'Process';
        update bigMachinesQuotes1[0];*/
        
        
        QuoteTriggerHandler.updateQuoteFields(bigMachinesQuotes1,oldQuoteMap);
        QuoteTriggerHandler.updateOpportunityFields(oldQuoteMap1,oldQuoteMap);
        QuoteTriggerHandler.updateOMNIQuoteConversion(bigMachinesQuotes1,oldQuoteMap);
        QuoteTriggerHandler.updateReplacedQuote(bigMachinesQuotes1,oldQuoteMap);
        
        QuoteTriggerHandler handler = new QuoteTriggerHandler();
        handler.updateQuote(bigMachinesQuotes1,oldQuoteMap,true);
        
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Process';
                bmQuote.Quote_Status__c = 'SUCCESS';
                
            }else if(bmQuote.Order_Type__c == 'Services'&& bmQuote.SAP_Prebooked_Service__c != null){
                //System.assertEquals(bmQuote.Quote_Status__c,'SUCCESS');
                System.assertEquals(bmQuote.Quote_Status__c,'ERROR');
            }else{
                System.assertNotEquals(bmQuote.Quote_Status__c,'SUCCESS');
            }
        }
        
        List<BigMachines__Quote__c> bigMachinesQuotes2 = [
            SELECT Id, BigMachines__Status__c,Parent_Quote__c,Amendment__c,Interface_Status__c,Message_Service__c,Booking_Message__c,BigMachines__Account__c,Price_Group__c,Payment_Terms__c,
            Order_Type__c,SAP_Prebooked_Service__c,SAP_Prebooked_Sales__c,SalesPaymentTerms__c,Letter_of_Credit_Required__c, Subscription_Status__c, Quote_Type__c,
            Initial_Order_Status__c, eCommerce_Quote__c, BigMachines__Total_Amount__c, CurrencyISOCode
            FROM BigMachines__Quote__c
        ];
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes2){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Error';
                bmQuote.Booking_Message__c = 'Error';
                
            }else if(bmQuote.Order_Type__c == 'Services'){
                bmQuote.Interface_Status__c = 'Processing';
                bmQuote.Message_Service__c = 'SUCCESS123';
            }else{
                bmQuote.Interface_Status__c = 'Processed';
                bmQuote.Message_Service__c = 'SUCCESS 12345';
                bmQuote.Booking_Message__c = 'SUCCESS';
                //      
                bmQuote.eCommerce_Quote__c = True;       
                bmQuote.SAP_Prebooked_Sales__c = 'Test';
                bmQuote.SAP_Prebooked_Service__c = 'Test';
                bmQuote.SAP_Booked_Sales__c =  'Test';                
                bmQuote.Quote_Status__c = 'SUCCESS';
                
            }
        }
        
        bigMachinesQuotes2[2].Quote_Type__c = 'Amendments';
        bigMachinesQuotes2[2].SAP_Prebooked_Service__c = 'DRAFT';
        bigMachinesQuotes2[2].Amendment__c = true;
        bigMachinesQuotes2[2].Parent_Quote__c  = bigMachinesQuotes2[1].Id;
        bigMachinesQuotes2[2].BigMachines__Opportunity__c = opp.Id;
        bigMachinesQuotes2[2].Order_Type__c ='Services';
        bigMachinesQuotes2[2].Ship_To_Country__c = 'USA';
        update bigMachinesQuotes2[2];
        
        //bigMachinesQuotes2[2].SAP_Prebooked_Sales__c = 'Test';
        //bigMachinesQuotes2[2].SAP_Booked_Sales__c = 'Test';
        
        //QuoteTriggerHandler.updateQuoteFields(bigMachinesQuotes2,oldQuoteMap);
        //QuoteTriggerHandler.getCurrencyConversions();
        List<BigMachines__Quote__c> newQuoteList = new List<BigMachines__Quote__c>{ bigMachinesQuotes2[2] };

        QuoteTriggerHandler.updateOldContractDetails(newQuoteList);
        
        /*
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes2){
            if(bmQuote.Order_Type__c == 'Sales'){
                System.assertEquals(bmQuote.Quote_Status__c,'ERROR');
                System.assertEquals(bmQuote.SalesPaymentTerms__c,lookupValue.Id);
            }else if(bmQuote.Order_Type__c == 'Services'){
                if(bigMachinesQuotes2[1].Id == bmQuote.Id){
                    System.assertnotEquals(bmQuote.Quote_Status__c,'SUCCESS Action Needed');
                    System.assertEquals(bmQuote.Price_Group__c,'Z1');
                }
            }else{
                System.assertnotEquals(bmQuote.Quote_Status__c,'SUCCESS Action Needed');
            }
        }
        
        System.assertEquals(bigMachinesQuotes2[2].Quote_Status__c,'Amended');*/
        Test.stopTest();
    }
    
    static testMethod void testSetQuoteFields2(){
        Test.startTest();
        Lookup_Values__c lookupValue = new Lookup_Values__c();
        lookupValue.Name = 'Letter of Credit(Brachy only-Handle this)';
        insert lookupValue;
        
        
        List<BigMachines__Quote__c> bigMachinesQuotes1 = [
            SELECT Id, BigMachines__Status__c,BigMachines__Is_Primary__c,BigMachines__Opportunity__c,Net_Booking_Value__c,First_Epot_Submitted_Date__c,OMNI_Quote_Number__c,Is_Replaced__c,Quote_Reference__c,Interface_Status__c,Message_Service__c,Booking_Message__c,BigMachines__Account__c,Price_Group__c,Payment_Terms__c,
            Order_Type__c,SAP_Prebooked_Service__c,SAP_Prebooked_Sales__c,SalesPaymentTerms__c,Letter_of_Credit_Required__c, Subscription_Status__c, Quote_Type__c,
            Initial_Order_Status__c, eCommerce_Quote__c, BigMachines__Total_Amount__c, CurrencyISOCode
            FROM BigMachines__Quote__c
        ];	
        
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
      
        Contact con = TestUtils.getContact();
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Unified_Funding_Status__c = '100%';
        //opp.Account_Country__c = 'USA';
        opp.Tender__c = 'Yes';
        insert opp;
        
        Map<Id,BigMachines__Quote__c> oldQuoteMap = new Map<Id,BigMachines__Quote__c>();
        Map<Id,BigMachines__Quote__c> oldQuoteMap1 = new Map<Id,BigMachines__Quote__c>();
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            bmQuote.BigMachines__Opportunity__c = opp.Id;
            oldQuoteMap.put(bmQuote.Id,bmQuote);
        }
        
        
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Process';
                
            }else if(bmQuote.Order_Type__c == 'Services'){
                bmQuote.Interface_Status__c = 'Processing';
                //bmQuote.Message_Service__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Service__c = '12345';
            }else{
                bmQuote.Interface_Status__c = 'Processed';
                //bmQuote.Message_Service__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Service__c = '12345';
                //bmQuote.Booking_Message__c = 'SUCCESS';
                bmQuote.SAP_Prebooked_Sales__c = '1234533';
            }
            
            bmQuote.BigMachines__Status__c = 'Approved';
        }
        
		
       
        /*oldQuoteMap1.put(bigMachinesQuotes1[0].Id, bigMachinesQuotes1[0]);
         bigMachinesQuotes1[0].Interface_Status__c = 'Process';
        update bigMachinesQuotes1[0];*/
        
        
        QuoteTriggerHandler.updateQuoteFields(bigMachinesQuotes1,oldQuoteMap);
        QuoteTriggerHandler.updateOpportunityFields(oldQuoteMap,oldQuoteMap);
        QuoteTriggerHandler.updateOMNIQuoteConversion(bigMachinesQuotes1,oldQuoteMap);
        QuoteTriggerHandler.updateReplacedQuote(bigMachinesQuotes1,oldQuoteMap);
        //QuoteTriggerHandler.addOnQuoteToContract(oldQuoteMap1);
        
        QuoteTriggerHandler handler = new QuoteTriggerHandler();
        handler.updateQuote(bigMachinesQuotes1,oldQuoteMap,true);
        
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes1){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Process';
                
            }else if(bmQuote.Order_Type__c == 'Services'&& bmQuote.SAP_Prebooked_Service__c != null){
                //System.assertEquals(bmQuote.Quote_Status__c,'SUCCESS');
                System.assertEquals(bmQuote.Quote_Status__c,'Interface Processing');
            }else{
                System.assertNotEquals(bmQuote.Quote_Status__c,'SUCCESS');
            }
        }
        
        List<BigMachines__Quote__c> bigMachinesQuotes2 = [
            SELECT Id, BigMachines__Status__c,Parent_Quote__c,Amendment__c,Interface_Status__c,Message_Service__c,Booking_Message__c,BigMachines__Account__c,Price_Group__c,Payment_Terms__c,
            Order_Type__c,SAP_Prebooked_Service__c,SAP_Prebooked_Sales__c,SalesPaymentTerms__c,Letter_of_Credit_Required__c, Subscription_Status__c, Quote_Type__c,
            Initial_Order_Status__c, eCommerce_Quote__c, BigMachines__Total_Amount__c, CurrencyISOCode
            FROM BigMachines__Quote__c
        ];
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes2){
            if(bmQuote.Order_Type__c == 'Sales'){
                bmQuote.Interface_Status__c = 'Error';
                bmQuote.Booking_Message__c = 'Error';
                
            }else if(bmQuote.Order_Type__c == 'Services'){
                bmQuote.Interface_Status__c = 'Processing';
                bmQuote.Message_Service__c = 'SUCCESS123';
            }else{
                bmQuote.Interface_Status__c = 'Processed';
                bmQuote.Message_Service__c = 'SUCCESS 12345';
                bmQuote.Booking_Message__c = 'SUCCESS';
                //      
                bmQuote.eCommerce_Quote__c = True;       
                bmQuote.SAP_Prebooked_Sales__c = 'Test';
                bmQuote.SAP_Prebooked_Service__c = 'Test';
                bmQuote.SAP_Booked_Sales__c =  'Test';                
                bmQuote.Quote_Status__c = 'SUCCESS Action Needed';
                
            }
        }
        
        bigMachinesQuotes2[2].Quote_Type__c = 'Amendments';
        bigMachinesQuotes2[2].SAP_Prebooked_Service__c = 'SUCCESS';
        bigMachinesQuotes2[2].Amendment__c = true;
        bigMachinesQuotes2[2].Parent_Quote__c  = bigMachinesQuotes2[1].Id;
        bigMachinesQuotes2[2].BigMachines__Opportunity__c = opp.Id;
        bigMachinesQuotes2[2].Order_Type__c ='Services';
        bigMachinesQuotes2[2].Ship_To_Country__c = 'USA';
        update bigMachinesQuotes2[2];
        
        //bigMachinesQuotes2[2].SAP_Prebooked_Sales__c = 'Test';
        //bigMachinesQuotes2[2].SAP_Booked_Sales__c = 'Test';
        
        QuoteTriggerHandler.updateQuoteFields(bigMachinesQuotes2,oldQuoteMap);
        //QuoteTriggerHandler.getCurrencyConversions();
        List<BigMachines__Quote__c> newQuoteList = new List<BigMachines__Quote__c>{ bigMachinesQuotes2[2] };

        QuoteTriggerHandler.updateOldContractDetails(newQuoteList);
        
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes2){
            if(bmQuote.Order_Type__c == 'Sales'){
                System.assertEquals(bmQuote.Quote_Status__c,'ERROR');
                System.assertEquals(bmQuote.SalesPaymentTerms__c,lookupValue.Id);
            }else if(bmQuote.Order_Type__c == 'Services'){
                if(bigMachinesQuotes2[1].Id == bmQuote.Id){
                    System.assertnotEquals(bmQuote.Quote_Status__c,'SUCCESS Action Needed');
                    System.assertEquals(bmQuote.Price_Group__c,'Z1');
                }
            }else{
                System.assertnotEquals(bmQuote.Quote_Status__c,'SUCCESS Action Needed');
            }
        }
        
        System.assertEquals(bigMachinesQuotes2[2].Quote_Status__c,'Amended');
        Test.stopTest();
    }
    
static testMethod void testUpdateReplacedQuote() {
    Test.startTest();
    Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
      
    Contact con = TestUtils.getContact();
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Unified_Funding_Status__c = '100%';
        //opp.Account_Country__c = 'USA';
        opp.Tender__c = 'Yes';
        insert opp;
    
     BigMachines__Configuration_Record__c oracleCPQ = new BigMachines__Configuration_Record__c();
        oracleCPQ.BigMachines__Is_Active__c = true;
        oracleCPQ.BigMachines__action_id_copy__c = '4654400';
        oracleCPQ.BigMachines__action_id_open__c = '4654396';
        oracleCPQ.BigMachines__bm_site__c = 'devvarian';
        oracleCPQ.BigMachines__document_id__c = '4653823';
        oracleCPQ.BigMachines__process__c = 'quickstart_commerce_process';
        oracleCPQ.BigMachines__process_id__c = '4653759';
        oracleCPQ.BigMachines__version_id__c = '********';
        insert oracleCPQ;

    BigMachines__Quote__c bmQuote = TestUtils.getQuote();
        bmQuote.Name = 'TEST-QUOTE';
        bmQuote.BigMachines__Account__c = acct.Id;
        bmQuote.BigMachines__Opportunity__c = opp.Id;
        bmQuote.BigMachines__Status__c = 'Approved';
        bmQuote.Order_Type__c = 'Sales';
        bmQuote.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        //bmQuote.BigMachines__Is_Primary__c = true;
        //bmQuote.Parent_Quote__c = bigMachinesQuotes[0].Name;   
        //bmQuote.eCommerce_Quote__c = True;
        bmQuote.eCommerce_Quote__c = False;
        bmQuote.Booking_Message__c = 'SUCCESS';
        bmQuote.SAP_Prebooked_Sales__c = 'Test';
        bmQuote.SAP_Prebooked_Service__c = 'Test';
        bmQuote.SAP_Booked_Sales__c =  'Test';
        bmQuote.Message_Service__c = 'SUCCESS';
        bmQuote.Quote_Status__c = 'SUCCESS Action Needed';
        bmQuote.Is_Replaced__c = false;
        bmQuote.SAP_Prebooked_Sales__c = 'Test';
    	bmQuote.BigMachines__Site__c = oracleCPQ.Id;
    	bmQuote.BigMachines__Is_Primary__c = false;
        insert bmQuote;

    	opp.Quote_Replaced__c =  bmQuote.Id;
        update opp;
		//bmQuote.Is_Replaced__c = true;
    	//update bmQuote;

    //Map<Id, BigMachines__Quote__c> oldQuoteMap = new Map<Id, BigMachines__Quote__c>();
    BigMachines__Quote__c clonedOldQuote = bmQuote.clone(false, true, false, false);
    

  BigMachines__Quote__c bmQuote2 = TestUtils.getQuote();
        bmQuote2.Name = 'TEST-QUOTE2';
        bmQuote2.BigMachines__Account__c = acct.Id;
        bmQuote2.BigMachines__Opportunity__c = opp.Id;
        bmQuote2.BigMachines__Status__c = 'Approved';
        bmQuote2.Order_Type__c = 'Sales';
        bmQuote2.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        //bmQuote.BigMachines__Is_Primary__c = true;
        //bmQuote2.Parent_Quote__c = bmQuote.Id;   
        //bmQuote.eCommerce_Quote__c = True;
        bmQuote2.eCommerce_Quote__c = False;
        bmQuote2.Booking_Message__c = 'SUCCESS';
        bmQuote2.SAP_Prebooked_Sales__c = 'Test';
        bmQuote2.SAP_Prebooked_Service__c = 'Test';
        bmQuote2.SAP_Booked_Sales__c =  'Test';
        bmQuote2.Message_Service__c = 'SUCCESS';
        bmQuote2.Quote_Status__c = 'SUCCESS Action Needed';
        bmQuote2.Is_Replaced__c = false;
        bmQuote2.SAP_Prebooked_Sales__c = 'Test';
    	bmQuote2.Quote__c = bmQuote.Id;
        bmQuote2.BigMachines__Is_Primary__c = true;
        bmQuote2.BigMachines__Site__c = oracleCPQ.Id;
        insert bmQuote2;
    
    System.debug('Id:'+ bmQuote2.Id);
    
    // qlist == bmquote2
    // quoteoldmap = oldQuoteMap
    // 
    
//BigMachines__Quote__c oldBmQuote2 = bmQuote2.clone(false, true, false, false);    
//oldBmQuote2.BigMachines__Is_Primary__c = false;
//update oldBmQuote2;    

// System.debug('oldBmQuote2:'+ oldBmQuote2.Id);
    
Map<Id, BigMachines__Quote__c> oldQuoteMap = new Map<Id, BigMachines__Quote__c>();
oldQuoteMap.put(bmQuote2.Id, bmQuote);
    

//bmQuote2.BigMachines__Is_Primary__c = true;
//update bmQuote2;




List<BigMachines__Quote__c> newQuotes = new List<BigMachines__Quote__c>{ bmQuote2 };
    
    QuoteTriggerHandler.updateReplacedQuote(newQuotes,oldQuoteMap);
    
    BigMachines__Quote__c updatedOldQuote = [
        SELECT Id, Is_Replaced__c FROM BigMachines__Quote__c WHERE Id = :bmQuote.Id
    ];

    //System.assertEquals(true, updatedOldQuote.Is_Replaced__c, 'The original quote should be marked as replaced');
     Test.stopTest();
}

    
    /**
* Test create omni quote conversion if quote is omni quote has been created in SF
*/
    static testMethod void testUdateOMNIQuoteConversion(){
        Test.startTest();
        Test.setMock(HTTPCalloutMock.class,new OracleQuoteGetTransactionTest.BigMachineTransactionMock());
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
      
        Contact con = TestUtils.getContact();
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Unified_Funding_Status__c = '100%';
        //opp.Account_Country__c = 'USA';
        opp.Tender__c = 'Yes';
        insert opp;
        
        List<BigMachines__Quote__c> bigMachinesQuotes2 = new List<BigMachines__Quote__c>();
        bigMachinesQuotes2 = [SELECT Id,Name,GPO__c,OwnerId
                                                          FROM BigMachines__Quote__c];
       
        
        Integer counter = 0;                                                    
        for(BigMachines__Quote__c bmQuote : bigMachinesQuotes2){
            bmQuote.OMNI_Quote_Number__c = 'TEST OMNI QUOTE '+counter;
            bmQuote.BigMachines__Opportunity__c = opp.Id;
            bmQuote.Order_Type__c ='Services';
        	bmQuote.Ship_To_Country__c = 'USA';
            bmQuote.GPO__c = 'NA';
        }
        if(bigMachinesQuotes2.size() > 0){
        	update bigMachinesQuotes2;
        }
        
        OMNI_Quote_Conversion__c oqc = new OMNI_Quote_Conversion__c();
        oqc.Name = bigMachinesQuotes2[0].Name;
        oqc.OMNI_Quote_Number__c = bigMachinesQuotes2[0].OMNI_Quote_Number__c; 
        oqc.BMI_Quote_Number__c  = bigMachinesQuotes2[0].Name;
        oqc.Geo__c = bigMachinesQuotes2[0].GPO__c;
    	oqc.Converted_By__c  = bigMachinesQuotes2[0].OwnerId;
        oqc.Date_Converted__c = system.today();
        oqc.Opportunity__c = bigMachinesQuotes2[0].BigMachines__Opportunity__c;
        insert oqc;
        
        System.debug('Quote Name: ' + bigMachinesQuotes2[0].Name);
System.debug('OMNI Quote Number: ' + bigMachinesQuotes2[0].OMNI_Quote_Number__c);
System.debug('Parent Quote: ' + bigMachinesQuotes2[0].Parent_Quote__c);
System.debug('Converted record: ' + oqc);
        
        List<OMNI_Quote_Conversion__c> omniQuotes = [SELECT Id FROM OMNI_Quote_Conversion__c LIMIT 1];
        System.assertEquals(1, omniQuotes.size());
        Test.stopTest();
    }
    
    static testMethod void  testRevisedQuoteInfo(){
        
        Account acct = [Select Id FROM Account LIMIT 1];
        Opportunity opp = [Select Id FROM Opportunity LIMIT 1];
        
        List<BigMachines__Quote__c> bigMachinesQuotes = [SELECT Id, Name,OMNI_Quote_Number__c
                                                         FROM BigMachines__Quote__c];
        
        System.debug('----bigMachinesQuotes[0].Name'+bigMachinesQuotes[0].Name);
        
        Quote_Product_Partner__c quotePartner = new Quote_Product_Partner__c();
        quotePartner.Quote__c = bigMachinesQuotes[0].Id;
        quotePartner.ERP_Partner_Number__c = 'TEST1212';
        quotePartner.ERP_Partner_Function__c = 'Z1';
        insert quotePartner;
        
        BigMachines__Quote__c bmQuote = TestUtils.getQuote();
        bmQuote.Name = 'TEST-QUOTE';
        bmQuote.BigMachines__Account__c = acct.Id;
        bmQuote.BigMachines__Opportunity__c = opp.Id;
        bmQuote.BigMachines__Status__c = 'Approved';
        bmQuote.Order_Type__c = 'Sales';
        bmQuote.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        //bmQuote.BigMachines__Is_Primary__c = true;
        bmQuote.Parent_Quote__c = bigMachinesQuotes[0].Name;
        bmQuote.BigMachines__Site__c = 'a31E0000000GL68IAG';
        //     
        //bmQuote.eCommerce_Quote__c = True;
        bmQuote.eCommerce_Quote__c = False;
        bmQuote.Booking_Message__c = 'SUCCESS';
        bmQuote.SAP_Prebooked_Sales__c = 'Test';
        bmQuote.SAP_Prebooked_Service__c = 'Test';
        bmQuote.SAP_Booked_Sales__c =  'Test';
        bmQuote.Message_Service__c = 'SUCCESS';
        bmQuote.Quote_Status__c = 'SUCCESS Action Needed';
        
        bmQuote.SAP_Prebooked_Sales__c = 'Test';
        insert bmQuote;
        
        //QuoteTriggerHandler.getPrebookDate(bmQuote, bmQuote);
    }
    
    /**
* Set up test data can be used for all the quotes.
*/
    @testSetup static void setUpQuotedata() {
        
        Regulatory_Country__c regulatory = new Regulatory_Country__c();
        regulatory.Name = 'USA';
        insert regulatory;
        
        Country__c country = new Country__c();
        country.Name = 'USA';
        country.Region__c = 'NA';
        country.ISO_Code__c = 'United States of America - US';
        country.Pricing_Group__c = 'Z1';
        country.Clearance__c=true;
        insert country;
        
        Account acct = TestUtils.getAccount();
        acct.AccountNumber = '121212';
        acct.Country1__c = country.Id;
        //acct.Account_Status__c='Blocked';
        //acct.Special_Care_Instruction_New__c='Choose another Account; this account is marked for deletion in SAP.;<font color="Red"><b></b></font><p><b>6/4/2021 M Feinblum: Customer has no A/R activity or active sites. </b></p><b></b>';
        
        insert acct;
        
        Contact con = TestUtils.getContact();
        con.AccountId = acct.Id;
        insert con;
        
        Opportunity opp1 = TestUtils.getOpportunity();
        opp1.AccountId = acct.Id;
        opp1.Primary_Contact_Name__c = con.Id;
        opp1.Tender__c = 'Yes';
        
        Opportunity opp2 = TestUtils.getOpportunity();
        opp2.AccountId = acct.Id;
        opp2.Primary_Contact_Name__c = con.Id;
        opp2.Tender__c = 'Yes';
        
        Opportunity opp3 = TestUtils.getOpportunity();
        opp3.AccountId = acct.Id;
        opp3.Primary_Contact_Name__c = con.Id;
        opp3.Tender__c = 'Yes';
        
        Opportunity opp4 = TestUtils.getOpportunity();
        opp4.AccountId = acct.Id;
        opp4.Primary_Contact_Name__c = con.Id;
        opp4.Tender__c = 'Yes';
        
        List<Opportunity> opps = new List<Opportunity>{opp1,opp2,opp3,opp4};
            insert opps;
        
        BigMachines__Quote__c bmQuote1 = TestUtils.getQuote();
        bmQuote1.Name = 'QUOTE-TEST';
        bmQuote1.BigMachines__Account__c = acct.Id;
        bmQuote1.BigMachines__Opportunity__c = opp1.Id;
        bmQuote1.BigMachines__Status__c = 'Approved';
        bmQuote1.Order_Type__c = 'Sales';
        bmQuote1.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote1.BigMachines__Is_Primary__c = true;
        
        BigMachines__Quote__c bmQuote2 = TestUtils.getQuote();
        bmQuote2.BigMachines__Account__c = acct.Id;
        bmQuote2.BigMachines__Opportunity__c = opp2.Id;
        bmQuote2.BigMachines__Status__c = 'Approved Revision';
        bmQuote2.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote2.Order_Type__c = 'Services';
        bmQuote2.BigMachines__Is_Primary__c = true;
        
        BigMachines__Quote__c bmQuote3 = TestUtils.getQuote();
        bmQuote3.BigMachines__Account__c = acct.Id;
        bmQuote3.BigMachines__Opportunity__c = opp3.Id;
        bmQuote3.BigMachines__Status__c = 'Approved Revision';
        bmQuote3.Order_Type__c = 'Services';
        bmQuote3.BigMachines__Is_Primary__c = true;
        
        BigMachines__Quote__c bmQuote4 = TestUtils.getQuote();
        bmQuote4.BigMachines__Account__c = acct.Id;
        bmQuote4.BigMachines__Opportunity__c = opp4.Id;
        bmQuote4.BigMachines__Status__c = 'Approved Revision';
        bmQuote4.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote4.Order_Type__c = 'Combined';
        bmQuote4.BigMachines__Is_Primary__c = true;
        
        BigMachines__Quote__c bmQuote5 = TestUtils.getQuote();
        bmQuote5.BigMachines__Account__c = acct.Id;
        bmQuote5.BigMachines__Opportunity__c = opp4.Id;
        bmQuote5.BigMachines__Status__c = 'Approved Amendment';
        bmQuote5.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote5.Order_Type__c = 'Combined';
        //bmQuote5.Booking_Message__c = 'SUCCESS';/////vinod
        //bmQuote5.Message_Service__c ='SUCCESS';
        bmQuote5.Quote_Type__c = 'Amendments';
        bmQuote5.BigMachines__Is_Primary__c = true;
        bmQuote5.SAP_Prebooked_Sales__c = 'Test';
        
        BigMachines__Quote__c bmQuote6 = TestUtils.getQuote();
        bmQuote6.BigMachines__Account__c = acct.Id;
        bmQuote6.BigMachines__Opportunity__c = opp4.Id;
        bmQuote6.BigMachines__Status__c = 'Approved Amendment';
        bmQuote6.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote6.Order_Type__c = 'Combined';
        bmQuote6.Quote_Type__c = 'Amendments';
        bmQuote6.BigMachines__Is_Primary__c = true;
        bmQuote6.SAP_Prebooked_Sales__c = 'Test';
        
        List<BigMachines__Quote__c> quotes = new List<BigMachines__Quote__c>{bmQuote1,bmQuote2,bmQuote3,bmQuote4};//,bmQuote5};
            insert quotes;
        
        test.starttest();
        opp2.Quote_Replaced__c= bmQuote1.id;
        update opp2;
        test.stoptest();
        
    }
    
    
    static testMethod void updateClearanceFieldTest() {
        Test.startTest();
        Account acct = [SELECT Id FROM Account WHERE AccountNumber = '121212'];
        
        Contact con = [SELECT Id FROM Contact WHERE AccountId =:acct.Id];
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Deliver_to_Country__c = 'USA';
        opp.Primary_Contact_Name__c = con.Id;
        opp.Tender__c = 'Yes';
        insert opp;
        
        BigMachines__Quote__c quote = TestUtils.getQuote();
        quote.BigMachines__Account__c = acct.Id;
        quote.BigMachines__Opportunity__c = opp.Id;
        quote.National_Distributor__c = '121212';
        quote.BigMachines__Status__c = 'Approved';
        quote.BigMachines__Site__c = 'a31E0000000GL68IAG';
        quote.Ship_To_Country__c='USA';
        
        //
        //quote.BigMachines__Status__c = 'Approved Revision' ;
        
        insert quote;
        
        
        QuoteTriggerHandler.updateClearanceField(new List<BigMachines__Quote__c>{quote});
        QuoteTriggerHandler objQT =  new QuoteTriggerHandler();
        objQT.preventDelete(new List<BigMachines__Quote__c>{quote}); 
        test.stopTest();      
        
    }
    
    @isTest
    public static void updateOpptyStage()
    {
        Account acct = [select id,name,Account_Status__c from Account where Account_Status__c != 'Pending Removal' limit 1];
        contact con = [select id,Name from Contact where AccountId =: acct.Id];
        
        Set<String> oppID = new Set<String>();
        Opportunity opp = new Opportunity(
            Name='New Opp', AccountId = acct.Id, Primary_Contact_Name__c = con.Id,Libra_Opportunity_Product__c='', StageName='1 - QUALIFICATION (N/A)', CloseDate=date.valueof('2019-01-01'), Type='LIBRA II',Tender__c = 'Yes');
        
        System.debug('oppID-->'+oppID);
        insert opp;
        oppID.add(opp.Id);
        Test.startTest();
        
        QuoteTriggerHandler.updateOpptyStage(oppID);
        
        BigMachines__Configuration_Record__c oracleCPQ = new BigMachines__Configuration_Record__c();
        oracleCPQ.BigMachines__Is_Active__c = true;
        oracleCPQ.BigMachines__action_id_copy__c = '4654400';
        oracleCPQ.BigMachines__action_id_open__c = '4654396';
        oracleCPQ.BigMachines__bm_site__c = 'devvarian';
        oracleCPQ.BigMachines__document_id__c = '4653823';
        oracleCPQ.BigMachines__process__c = 'quickstart_commerce_process';
        oracleCPQ.BigMachines__process_id__c = '4653759';
        oracleCPQ.BigMachines__version_id__c = '********';
        insert oracleCPQ;
        
        //Subscription__c sub = [SELECT Id, Name FROM Subscription__c limit 1];
        map<Id, BigMachines__Quote__c> mapContNoQtId = new map<Id, BigMachines__Quote__c>();
        BigMachines__Quote__c quote = TestUtils.getQuote();
        quote.BigMachines__Status__c = 'Pending';
        quote.BigMachines__Account__c = acct.Id;
        quote.BigMachines__Opportunity__c=opp.Id;
        quote.National_Distributor__c = '121212'; 
        quote.BigMachines__Site__c = oracleCPQ.Id;
       // quote.SAP_Contract_Number__c = sub.Id;
        insert quote;
        mapContNoQtId.put(quote.Id,quote);
        System.debug('mapContNoQtId-->'+mapContNoQtId);
       
        
        quote.National_Distributor__c = '121212';
        update quote;
        Test.stopTest();
        //QuoteTriggerHandler.addOnQuoteToContract(mapContNoQtId);
    }
        /**
        * Set up test data with account status.
        */
    static testMethod void testAccountStats() {
        // Account acct = new Account();
        // acct.Name = 'Test Account';
        // acct.Country__c = 'USA';
        // acct.Account_Type__c = 'Customer';
        // acct.Account_Duplicate_Rule_Bypass__c=true;
        // acct.AccountNumber = '121212';
        // acct.Account_Status__c='Blocked';
        // acct.Special_Care_Instruction_New__c='Choose another Account; this account is marked for deletion in SAP.;<font color="Red"><b></b></font><p><b>6/4/2021 M Feinblum: Customer has no A/R activity or active sites. </b></p><b></b>';
        // insert acct;
        Test.startTest();
        Account acct = [SELECT Id,Account_Status__c,Special_Care_Instruction_New__c FROM Account WHERE AccountNumber = '121212'];
        acct.Account_Status__c='Blocked';
        acct.Special_Care_Instruction_New__c='Choose another Account; this account is marked for deletion in SAP.;<font color="Red"><b></b></font><p><b>6/4/2021 M Feinblum: Customer has no A/R activity or active sites. </b></p><b></b>';
        update acct;
        
        Contact con = [SELECT Id FROM Contact WHERE AccountId =:acct.Id];
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Tender__c = 'Yes';
        insert opp;
        
        User user = new User();
        user.ProfileId = [Select Id From Profile Where Name LIKE '%System Admin%' limit 1].Id;
        user.LastName = 'test lastname';
        user.Email = '<EMAIL>';
        Integer randomInt = Integer.valueOf(math.rint(math.random()*1000000));
        user.Username = 'testdemo'+randomInt+'@gmail.com';
        user.CompanyName = 'test';
        user.LanguageLocaleKey = 'en_US';
        user.LocaleSidKey = 'en_Us';
        user.Title = 'title';
        user.Alias = 'Test0087';
        user.TimeZoneSidKey = 'America/Los_Angeles';
        user.EmailEncodingKey = 'UTF-8';
        insert user;
        
        BigMachines__Quote__c bmQuote = new BigMachines__Quote__c();
        bmQuote.Name = 'Quote Test';
        bmQuote.Price_Group__c = 'Z1';
        bmQuote.BigMachines__Account__c = acct.Id;
        bmQuote.BigMachines__Opportunity__c = opp.Id;
        bmQuote.National_Distributor__c = '';
        bmQuote.BigMachines__Status__c='Approved';
        bmQuote.Amendment__c=true;
        bmQuote.Current_Amendment__c=true;
        bmQuote.SAP_Booked_Service__c='123456';
        bmQuote.Current_Approvers__c='test1,test2,test3';
        bmQuote.Submitted_To_SAP_By__c = user.Id;
        
        BigMachines__Quote__c bmQuote2 = TestUtils.getQuote();
        bmQuote.Name = 'Quote Test2';
        bmQuote2.BigMachines__Account__c = acct.Id;
        bmQuote2.BigMachines__Opportunity__c = opp.Id;
        bmQuote2.BigMachines__Status__c = 'Approved Revision';
        bmQuote2.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote2.Order_Type__c = 'Services';
        bmQuote2.Amendment__c=true;
        bmQuote2.Current_Amendment__c=true;
        bmQuote2.Precheck_Interface_Status__c='Ready for PreCheck';
        
        BigMachines__Quote__c bmQuote3 = TestUtils.getQuote();
        bmQuote3.BigMachines__Account__c = acct.Id;
        bmQuote3.BigMachines__Opportunity__c = opp.Id;
        bmQuote3.BigMachines__Status__c = 'Approved Amendment';
        bmQuote3.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote3.Order_Type__c = 'Combined';
        bmQuote3.Quote_Type__c = 'Amendments';
        bmQuote3.BigMachines__Is_Primary__c = true;
        bmQuote3.Quote_Status__c='SUCCESS';
        bmQuote3.SAP_Prebooked_Sales__c = 'Test';
        bmQuote3.Subscription_Only_Quote__c=true;
        bmQuote3.Subscription_Status__c='Success';
        bmQuote3.Amendment__c=true;
        bmQuote3.Current_Amendment__c=true;
        try{
            //insert bmQuote2;
            List<BigMachines__Quote__c> quotes = new List<BigMachines__Quote__c>{bmQuote,bmQuote2,bmQuote3};
            insert quotes;
            BigMachines__Quote__c q = [SELECT Id FROM BigMachines__Quote__c WHERE Name =:'Quote Test' LIMIT 1];
            q.Precheck_Interface_Status__c='Ready for PreCheck';
            q.SAP_Booked_Service__c='123456';
            q.Current_Approvers__c='test1,test2,test3';
            q.Submitted_To_SAP_By__c = UserInfo.getUserId();
            update q;
        }catch(Exception e){}
        Test.stopTest();
    }
    
    static testMethod void testApprovalFields() {
        
        Account acct = [SELECT Id,Account_Status__c,Special_Care_Instruction_New__c FROM Account WHERE AccountNumber = '121212'];
        
        Contact con = [SELECT Id FROM Contact WHERE AccountId =:acct.Id];
        
        Opportunity opp = TestUtils.getOpportunity();
        opp.AccountId = acct.Id;
        opp.Primary_Contact_Name__c = con.Id;
        opp.Tender__c = 'Yes';
        insert opp;
        Test.startTest();
        
        Test.setMock(HTTPCalloutMock.class,new OracleQuoteGetTransactionTest.BigMachineTransactionMock());
        
         BigMachines__Configuration_Record__c siteRecord = new BigMachines__Configuration_Record__c(
                                                            BigMachines__bm_site__c = 'Test Site', 
                                                            BigMachines__process_id__c = '************',
                                                            BigMachines__action_id_copy__c = '************', 
                                                            BigMachines__action_id_open__c = '************',
                                                            BigMachines__document_id__c = '************',
                                                            BigMachines__version_id__c = '************', 
                                                            BigMachines__process__c = 'Test Process Variable',
                                                            BigMachines__Is_Active__c = true);
        insert siteRecord; 
        
        BigMachines__Quote__c bmQuote = new BigMachines__Quote__c();
        bmQuote.Name = 'Quote Test';
        bmQuote.Price_Group__c = 'Z1';
        bmQuote.BigMachines__Account__c = acct.Id;
        bmQuote.BigMachines__Opportunity__c = opp.Id;
        bmQuote.National_Distributor__c = '';
        bmQuote.BigMachines__Status__c='Pending';
        bmQuote.BigMachines__Site__c = siteRecord.Id;
        
        
        BigMachines__Quote__c bmQuote2 = TestUtils.getQuote();
        bmQuote.Name = 'Quote Test2';
        bmQuote2.BigMachines__Account__c = acct.Id;
        bmQuote2.BigMachines__Opportunity__c = opp.Id;
        bmQuote2.BigMachines__Status__c = 'Pending';
        bmQuote2.Payment_Terms__c = 'Letter of Credit(Brachy only-Handle this)';
        bmQuote2.Order_Type__c = 'Services';
        bmQuote.BigMachines__Site__c = siteRecord.Id;
        
        List<BigMachines__Quote__c> listOfQuotes = new List<BigMachines__Quote__c>();
        
        insert bmQuote;
        insert bmQuote2;
        //insert new List<BigMachines__Quote__c>{bmQuote ,bmQuote2};
        
        bmQuote.BigMachines__Status__c = 'Pending Approval';
        bmQuote2.BigMachines__Status__c  = 'Approved';
        update bmQuote;
        update bmQuote2;
        //update new List<BigMachines__Quote__c>{bmQuote, bmQuote2};
        
        List<BigMachines__Quote__c> bmQuotes = [SELECT Id FROM BigMachines__Quote__c WHERE DAY_ONLY(SubmitForApprovalsDate__c) = TODAY OR DAY_ONLY(QuoteApprovedDate__c) = TODAY];
        //System.assertEquals(2, bmQuotes.size());
        
        Test.stopTest();
    }
    
    
     private static BigMachines__Quote_Product__c getQuoteProduct(Id quoteId,Id subscriptionProductId, String numberOfYears, String billingFrequency, String salesType){
        BigMachines__Quote_Product__c quoteProduct = new BigMachines__Quote_Product__c();
        quoteProduct.BigMachines__Quote__c = quoteId;
        quoteProduct.BigMachines__Product__c = subscriptionProductId;
        quoteProduct.Header__c = true;
        quoteProduct.Product_Type__c = 'Sales';
        quoteProduct.Standard_Price__c = 40000;
        quoteProduct.BigMachines__Sales_Price__c = 40000;
        quoteProduct.BigMachines__Quantity__c = 1;
        quoteProduct.Line_Number__c= 12;
        quoteProduct.EPOT_Section_Id__c = 'TEST';
        quoteProduct.Subscription_Start_Date__c = Date.today();
        quoteProduct.Add_On_Start_Date__c = Date.today().addMonths(15).toStartOfMonth();
        quoteProduct.Number_Of_Years__c = numberOfYears;
        quoteProduct.Subscription_End_Date__c = Date.today().addYears(Integer.valueOf(numberOfYears));
        quoteProduct.Billing_Frequency__c = billingFrequency;
        quoteProduct.Subscription_Sales_Type__c = salesType;
        quoteProduct.Installation_Price__c = 0.0;
        quoteProduct.Subscription_Unit_Price__c = 2900;
        if(salesType != 'Renewal'){
            quoteProduct.SAP_Contract_Number__c = 'TEST1234';
        }
        return quoteProduct;
    }    

}
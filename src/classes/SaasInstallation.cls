/**
*@description Creates subscription installation plan
*<AUTHOR>
*/
public with sharing class SaasInstallation{
    
    
    static List<Product2> fullScaleList = new List<Product2>();
    static Id caseRecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('INST').getRecordTypeId();
    static Id planningWORecordTypeId = Schema.SObjectType.WorkOrder.getRecordTypeInfosByName().get('Planning VIS').getRecordTypeId();
    static Id headerWOLIRecordTypeId = Schema.SObjectType.WorkOrderLineItem.getRecordTypeInfosByName().get('Header').getRecordTypeId();
    /**
* Creates records of Installation case, Case lines and Installed product for new subscription
*/
    public static void createInstallationPlan(List<Subscription__c> subscriptions, Map<Id, Subscription__c> oldSubscriptions){
        Set<Id> quoteIds = new Set<Id>();
        Set<String> functionalLocations = new Set<String>();
        List<Subscription__c> subscriptionsToProcess = new List<Subscription__c>();
        Map<String,Id> primaryContacts = new Map<String,Id>();
        Map<String,String> poNumbers = new Map<String, String>();
        Map<String,Id> erpPcodeIds = new Map<String,Id>();
        Map<String,String> parentPCSN = new Map<String,String>();
        Set<String> equipmentCodes = new Set<String>();
        Set<String> projectManagerNumbers = new Set<String>();
        system.debug('subscriptions=='+subscriptions);
        for(Subscription__c saasSubscription : subscriptions){
            //Changed by Harshal for STRY0055313 
            //Added back ERP Project condition after it was removed as part of above story - Krishna
            if(!String.isBlank(saasSubscription.PCSN__c) && saasSubscription.ERP_Project__c
               && ((oldSubscriptions!=null && (oldSubscriptions.get(saasSubscription.Id).PCSN__c!=saasSubscription.PCSN__c || oldSubscriptions.get(saasSubscription.Id).ERP_Project__c == false)) 
                   || oldSubscriptions==null)){
                       
                       subscriptionsToProcess.add(saasSubscription);
                       quoteIds.add(saasSubscription.Quote__c);
                       
                       if(String.isNotBlank(saasSubscription.Functional_Location__c)){
                           functionalLocations.add(saasSubscription.Functional_Location__c);
                       }
                       system.debug('saasSubscription pcsn==='+(saasSubscription.PCSN__c.split('-')[1]));
                       equipmentCodes.add(saasSubscription.PCSN__c.split('-')[1]);
                       system.debug('equipmentCodes=='+equipmentCodes);
                       projectManagerNumbers.add(saasSubscription.Site_Project_Manager__c);
                   }
        }
        system.debug('equipmentCodes 11=='+equipmentCodes);
        List<Asset> installedProducts = new List<Asset>();
        List<Case> installationCases = new List<Case>();
        List<WorkOrder> planningWorkOrders = new List<WorkOrder>();
        List<WorkOrderLineItem> planningWorkOrderLines = new List<WorkOrderLineItem>();
        
        List<Subscription_Product__c> subscriptionProducts = new List<Subscription_Product__c>();
        
        Map<String,Id> locationIds = getLocationRecords(functionalLocations);
        system.debug('^^locationIds=='+locationIds);
        
        Map<String,Id> subscriptionProductIds = getHeaderProductsDetails(quoteIds, primaryContacts, poNumbers, erpPcodeIds, parentPCSN);
        
        Map<String, Product2> saasModels = getSubscriptionModels(equipmentCodes);
        system.debug('saasModels =='+saasModels);
        Map<String, User> projectManagers = getProjectManagers(projectManagerNumbers);
        
        //Create installation case/installed product/case line for every subscription
        for(Subscription__c saasSubscription : subscriptionsToProcess){
            Id locationId = (locationIds!=null && locationIds.size()>0)?locationIds.get(saasSubscription.Functional_Location__c):null;
            Id productId = subscriptionProductIds.get(saasSubscription.Name);
            system.debug('saasSubscription.PCSN__c==='+saasSubscription.PCSN__c);
            String productCode = saasSubscription.PCSN__c.split('-')[1];
            
            Asset installedProduct = getInstalledProduct(saasSubscription, locationId, saasModels.get(productCode), null);
            Id saasProductId;
            
            if((saasModels!=null && saasModels.size()>0) && (productCode!=null && productCode.length()>0)){
                system.debug('=saasModels.chk=='+saasModels.get(productCode).Id);
                saasProductId=saasModels.get(productCode).Id;
            }
            Case installationCase = getInstallationCase(saasSubscription, locationId,(primaryContacts!=null? primaryContacts.get(saasSubscription.Name):null), installedProduct,saasProductId);
            
            if(projectManagers.containsKey(saasSubscription.Site_Project_Manager__c)){ 
                installationCase.OwnerId = projectManagers.get(saasSubscription.Site_Project_Manager__c).Id;
            }
            
            installedProducts.add(installedProduct);
            installationCases.add(installationCase);
            subscriptionProducts.add(getSubscriptionProduct(installedProduct, locationId, saasSubscription));
        }
        
        System.debug('----installedProducts'+installedProducts);
        if(!installedProducts.isEmpty()) insert installedProducts;
        
        for(Subscription_Product__c subProduct : subscriptionProducts){
            subProduct.Asset__c = subProduct.Asset__r.Id;
        }
        
        if(!subscriptionProducts.isEmpty()) insert subscriptionProducts;
        

    }
    
    /**
* Get location records related to subscriptions. Used for creating installed products
*/
    private static Map<String, Id> getLocationRecords(Set<String> functionalLocations){
        Map<String,Id> locationIds = new Map<String, Id>();
        for(Schema.Location loc : [SELECT Id,SAP_Functional_Location__c FROM Location WHERE SAP_Functional_Location__c IN:functionalLocations]){
            locationIds.put(loc.SAP_Functional_Location__c, loc.Id);
        }
        return locationIds;
    }
    
    private static Asset getInstalledProduct(Subscription__c saasSubscription, Id locationId, Product2 product, Id parentId){
        
        Product2 localProd = product;
        
        if((saasSubscription !=null && localProd!=null)&&(saasSubscription.Product_Type__c.equalsIgnoreCase('FS') && localProd.productCode.substring(0,3) == 'HMS'))
        {
            List<Product2> tempList = new List<Product2>();
            System.debug('fullScaleList+++++++'+fullScaleList);
            localProd = fullScaleList [0];
            System.debug('localProd+++++++'+localProd);
        }
        System.debug('Outside if');
        //System.debug('localProd.Id+++++'+localProd.Id);
        
        Asset objInstalledProduct = new Asset();
        objInstalledProduct.Name = saasSubscription.Name;
        //STSK0011740 - Start -- Changed Status to 'Ordered' From 'Pending Installation',
        objInstalledProduct.Status = 'Ordered';
        objInstalledProduct.Interface_Status__c = 'Process';
        objInstalledProduct.Billing_Type__c = 'P – Paid Service';
        objInstalledProduct.SAP_Functional_Location__c = saasSubscription.Functional_Location__c;
        objInstalledProduct.AccountId = saasSubscription.Account__c;
        objInstalledProduct.LocationId = locationId;
        objInstalledProduct.SAP_EQUIPMENT__c = saasSubscription.PCSN__c;
        objInstalledProduct.Subscription_Contract__c = saasSubscription.Id;
        objInstalledProduct.ParentId = parentId;
        
        if(localProd!=null){
            objInstalledProduct.Interface_Change_Type__c = localProd.Assign_PCSN_Type__c;
            objInstalledProduct.Product2Id = localProd.Id;
            objInstalledProduct.ERP_Pcode__c = localProd.ERP_PCode__c;
            objInstalledProduct.SerialNumber = saasSubscription.PCSN__c.remove(localProd.ERP_PCode__r.Name).remove('-'+localProd.Service_Product_Group__c).trim();
        }
        else{
            objInstalledProduct.Interface_Message__c ='Missing ERP Pcode';
        }
        return objInstalledProduct;
    }
    
    
    /**
* Create installation case for Saas subscription
*/
    private static Case getInstallationCase(Subscription__c saasSubscription, Id locationId, Id primaryContactId, Asset insProd, Id saasProductId){
        Case objCase=new Case();
        
        objCase.AccountId = saasSubscription.Account__c;
        objCase.Location__c = locationId;
        objCase.RecordTypeId = caseRecordTypeId;
        objCase.Subject = 'Subscription Installation';
        objCase.ERP_Project_Number__c = saasSubscription.ERP_Project_Number__c;
        objCase.Origin = 'Subscription';
        objCase.ContactId = primaryContactId;
        objCase.Asset = insProd;
        if(saasProductId!=null)
            objCase.ProductId = saasProductId;
        objCase.Subscription__c = saasSubscription.Id;
        
        return objCase;      
    }
    
    /*private static WorkOrder getPlanningWorkOrder(Case installationCase, Subscription__c saasSubscription, Id locationId, Asset insProd, Id projectManagerId){
        WorkOrder pwo = new WorkOrder();
        pwo.Case = installationCase;
        pwo.AccountId = saasSubscription.Account__c;
        pwo.Asset = insProd;
        pwo.LocationId = locationId;
        pwo.RecordTypeId = planningWORecordTypeId;
        pwo.Status = 'New';
        pwo.Subject = 'Subscription Installation - Contract:'+saasSubscription.Name;
        pwo.SAP_Project_Text__c = saasSubscription.ERP_Project_Number__c;
        pwo.Project_Manager__c = projectManagerId;
        pwo.OwnerId = projectManagerId;
        return pwo;
    }
    
    public static WorkOrderLineItem getWOLI(WorkOrder wo, Asset insProd, Id locationId, ERP_NWA__c nwa){
        
        WorkOrderLineItem woli = new WorkOrderLineItem();
        woli.RecordTypeId = headerWOLIRecordTypeId;
        woli.WorkOrder = wo;
        woli.LocationId = locationId;
        woli.Asset = insProd;
        woli.Billing_Type__c =  'I – Installation';
        woli.SAP_NWA__c = nwa.Id;
        woli.Status = 'New';
        woli.SAP_WBS__c = nwa.WBS_Element__c;
        return woli;
    }*/
    
    /**
* Create instance of subscriptiot using installed product and location.
*/
    private static Subscription_Product__c getSubscriptionProduct(Asset installedProduct, Id locationId, Subscription__c saasSubscription){
        return new Subscription_Product__c(
            ERP_Equipment_Nbr__c = installedProduct.SAP_EQUIPMENT__c,
            ERP_Partner_Number__c = saasSubscription.ERP_Site_Partner__c,
            Asset__r = installedProduct,
            Subscription__c = saasSubscription.Id,
            Asset_Location__c = locationId
        );
    }
    
    /**
* Get header products related to each subscription to create installed product
*/
    private static Map<String,Id> getHeaderProductsDetails(Set<Id> quoteIds, Map<String, Id> primaryContacts, 
                                                           Map<String,String> poNumbers, Map<String,Id> erpPcodeIds, Map<String,String> parentPCSN){
                                                               List<BigMachines__Quote_Product__c> quoteProducts = [
                                                                   SELECT Id,Name,BigMachines__Product__c,Subscription_Product_Type__c,BigMachines__Quote__r.Principal_Contact__c,
                                                                   BigMachines__Quote__r.Purchase_Order_Number__c,SAP_Contract_Number__c,BigMachines__Product__r.ERP_Pcode__c,Parent_PCSN__c
                                                                   FROM BigMachines__Quote_Product__c
                                                                   WHERE Subscription_Product_Type__c!=''
                                                                   AND BigMachines__Quote__c IN: quoteIds
                                                                   AND Header__c = true
                                                               ];
                                                               
                                                               Map<String,Id> subscriptionProductIds = new Map<String,Id>();
                                                               
                                                               for(BigMachines__Quote_Product__c quoteProduct : quoteProducts){
                                                                   subscriptionProductIds.put(quoteProduct.SAP_Contract_Number__c, quoteProduct.BigMachines__Product__c);
                                                                   primaryContacts.put(quoteProduct.SAP_Contract_Number__c, quoteProduct.BigMachines__Quote__r.Principal_Contact__c);
                                                                   poNumbers.put(quoteProduct.SAP_Contract_Number__c, quoteProduct.BigMachines__Quote__r.Purchase_Order_Number__c);
                                                                   erpPcodeIds.put(quoteProduct.SAP_Contract_Number__c, quoteProduct.BigMachines__Product__r.ERP_Pcode__c);
                                                                   parentPCSN.put(quoteProduct.SAP_Contract_Number__c, quoteProduct.Parent_PCSN__c);
                                                               }
                                                               return subscriptionProductIds;
                                                           }
    
    /**
* Get project managers to assign installation cases
*/
    private static Map<String, User> getProjectManagers(Set<String> pmERPNumbers){
        Map<String,String> emailWithERPNumbers = new Map<String,String>();
        Map<String, User> projectManagers = new Map<String,User>();
        
        for(Contact internalContact :[
            SELECT Id, Email, ERP_Reference__c 
            FROM Contact
            WHERE ERP_Reference__c IN:pmERPNumbers
            AND ERP_Reference__c!=null
        ]){
            emailWithERPNumbers.put(internalContact.Email, internalContact.ERP_Reference__c);
        }
        
        for(User pmUser : [SELECT Id, Email FROM USER WHERE Email IN:emailWithERPNumbers.keySet()]){
            if(emailWithERPNumbers.containsKey(pmUser.Email)){
                projectManagers.put(emailWithERPNumbers.get(pmUser.Email), pmUser);
            }
        }
        return projectManagers;
    }
    /**
* Get subscription models
*/
    private static Map<String,Product2> getSubscriptionModels(Set<String> saasProductCodes){
        Map<String,Product2> saasModels = new Map<String,Product2>();
        system.debug('saasProductCodes=='+saasProductCodes);
        List<Product2> saasServiceProducts = [
            SELECT Id, ProductCode, ERP_Pcode__c,ERP_PCode__r.Name, Service_Product_Group__c,Assign_PCSN_Type__c
            FROM Product2
            WHERE Service_Product_Group__c IN:saasProductCodes
        ];
        system.debug('==saasServiceProducts =='+saasServiceProducts);
        
        for(Product2 product : saasServiceProducts){
            saasModels.put(product.Service_Product_Group__c,product);
            
            if(product.productCode == 'HMSC')
                fullScaleList.add(product);
        }
        system.debug('==saasModels=='+saasModels);
        return saasModels;
    }
    
    /**
* ERP NWA ids to update on case lines
*/
    private static Map<Id,ERP_NWA__c> getSubscriptionNWAMap(List<Subscription__c> subscriptions){
        System.debug(LoggingLevel.INFO+'-----subscriptions'+subscriptions);
        
        Map<Id,ERP_NWA__c> erpNWAIds = new Map<Id,ERP_NWA__c>();
        for(ERP_NWA__c erpnwa :[
            SELECT Id, Name,ERP_Project__r.Subscription__c, WBS_Element__c
            FROM ERP_NWA__c
            WHERE ERP_Project__r.Subscription__c IN :subscriptions
            AND ERP_Std_Text_Key__c = 'INST001'
        ]){
            System.debug(LoggingLevel.INFO+'-----erpnwa'+erpnwa);
            erpNWAIds.put(erpnwa.ERP_Project__r.Subscription__c, erpnwa);                                    
        }
        return erpNWAIds;
    } 
}
public class fakeresponse {
public static string typeofrequest = 'negative';
public static HTTPResponse fakeresponsemethod(String typeofresponse)
{
 
 HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        if(typeofresponse == 'getuseremail')
        {
         res.setBody('{"id": "00ub0oNGTSWTBKOLGLNR","status": "DEPROVISIONED","credentials":{"recovery_question":{"question":"What is your mother name"}}}');
        }
        if(typeofresponse == 'getuseremailpossitive')
        {
         //res.setBody('{"id": "00ub0oNGTSWTBKOLGLNR","status": "DEPROVISIONED","credentials":{"recovery_question":{"question":"What is your mother name"}}}');
        }
        if(typeofresponse != 'getuseremail' && typeofresponse != 'activation'){
        res.setBody('{"id": "00ub0oNGTSWTBKOLGLNR","status": "LOCKED_OUT","credentials":{"recovery_question":{"question":"What is your mother name"}}}');
        }
        if( typeofresponse == 'activation' || typeofresponse == 'updateuser'){
        
         res.setBody('{"errorCauses": [{"errorSummary":"this is an error."}]}');
        }
        if(typeofresponse == 'applinks')
        {
          String body = '[{"appInstanceId":"0oaigfpoxjUHAFMHUVMP"}]';
         res.setBody(body);
         system.debug('#######'+res.getbody());
        }
        if(typeofresponse == 'getuserbyid')
        {
         res.setBody('{"id": "00ub0oNGTSWTBKOLGLNR","status": "ACTIVE","credentials":{"recovery_question":{"question":"What is your mother name"}}}');
         system.debug('@@@@@'+res.getbody());
        }
        res.setStatusCode(200);
        return res;
}
public static HTTPResponse fakeresponseloginmethod(Boolean Flag)
{
    
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
    if(Flag)
        res.setBody('{"cookieToken":"jkjhkyuhkgjgjgjgjj","userId" : "00ub0oNGTSWTBKOLGLNR", "id" : "hkjhkjkjjhhgjhgj" }');
        else
        res.setBody('{"cookieToken":null,"userId" : "00ub0oNGTSWTBKOLGLNR", "id" : "hkjhkjkjjhhgjhgj" }');
        res.setStatusCode(200);
        return res;
}
public static HTTPResponse fakeresponseloginmethod()
{
    
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setBody('{"cookieToken":"jkjhkyuhkgjgjgjgjj","userId" : "00ub0oNGTSWTBKOLGLNR", "id" : "hkjhkjkjjhhgjhgj" }');
        res.setStatusCode(200);
        return res;
}
}
@isTest
public class MVTPassPackageControllerTest {
    public static Id recTypeIDTechnician = Schema.SObjectType.SVMXC__Service_Group_Members__c.getRecordTypeInfosByName().get('Technician').getRecordTypeId();
    @isTest public static void tPaasSubscriptionTest(){
        
        
        account acc = new account();
        acc.Name = 'Test conName';
        acc.Site_Type__c = 'HOSPITAL';
        acc.CurrencyIsoCode = 'USD';
        acc.Country__c = 'India';
        acc.BillingCity = 'Jaipur';
        acc.BillingState = 'Rajasthan';
        acc.ERP_Site_Partner_Code__c = 'Test';
        acc.BillingStreet = 'Test Billing Street';
        insert acc;
        
        contact oCon = new contact();
        oCon.AccountId = acc.Id;
        oCon.FirstName = 'Test fName1';
        oCon.LastName = 'Test lName1';
        oCon.Functional_Role__c = 'Educator';
        oCon.Email = '<EMAIL>';
        oCon.MobilePhone = '**********';
        oCon.Phone = '**********';
        oCon.Account_Admin__c = true;
        insert oCon;
        
        
        Apttus__APTS_Agreement__c agr = new Apttus__APTS_Agreement__c();
        agr.Name = 'Test Umbrella Agreement';
        agr.Apttus__Account__c = acc.Id;
        agr.Apttus__Contract_Start_Date__c = date.parse('12/27/2010');
        agr.Apttus__Contract_End_Date__c = date.parse('12/27/2011');
        insert agr;
          
        Opportunity salesOpp1 = new Opportunity();
        salesOpp1.Name = 'Test Opportunity';
        salesOpp1.AccountId = acc.Id;
        salesOpp1.StageName = '0 - NEW LEAD';
        salesOpp1.Primary_Contact_Name__c = ocon.Id;
        salesOpp1.MGR_Forecast_Percentage__c = '';
        salesOpp1.Unified_Funding_Status__c = '20%';
        salesOpp1.Unified_Probability__c = '20%';
        salesOpp1.Net_Booking_Value__c = 200;
        salesOpp1.CloseDate = System.today();
        salesOpp1.Opportunity_Type__c = 'Sales';
        salesOpp1.Type ='Replacement';
        salesOpp1.Tender__c ='No';
        salesOpp1.Umbrella_Agreement__c = agr.id;
        insert salesOpp1;
        
        Sales_Order__c so = new Sales_Order__c();
        so.Name='Test So';
        so.Site_Partner__c=acc.id;       
        insert so;
        
        BigMachines__Quote__c quote = new BigMachines__Quote__c();
        quote.Name = 'Quote Test';
        quote.Sales_Order__c=so.id;
        quote.BigMachines__Opportunity__c = salesOpp1.id;
        quote.Price_Group__c = 'Z1';
        quote.Quote_Type__c = 'Amendments';
        quote.BigMachines__Site__c =label.BigMachines_Site_Id;
        quote.BigMachines__Status__c='Approved';
        insert Quote;
        
        test.startTest();
        Subscription__c subscription1 = new Subscription__c();
        subscription1.Name = 'TEST1234';
        subscription1.Quote__c = quote.Id;
        subscription1.Status__c = 'Processed';
        subscription1.Ext_Quote_Number__c = 'QUOTE-TEST1';
        subscription1.ERP_Site_Partner__c = 'Sales11111';
        insert subscription1;
        
        SVMXC__Site__c varLoc = new SVMXC__Site__c(SVMXC__Account__c = acc.id, Plant__c = 'testPlant', SVMXC__Service_Engineer__c = userInfo.getUserID());
        insert varLoc;
        
        //insert Service Team
        SVMXC__Service_Group__c objServTeam = new SVMXC__Service_Group__c(Name = 'test team' , District_Manager__c = userinfo.getUserID());
        insert objServTeam;
        
        //insert technician
        SVMXC__Service_Group_Members__c techEqipt = new SVMXC__Service_Group_Members__c(RecordTypeId = recTypeIDTechnician,
                                                        Name = 'Test Technician', User__c = userInfo.getUserId(),ERP_Timezone__c = 'Aussa',SVMXC__Service_Group__c = objServTeam.Id);
        insert techEqipt;
        
        SVMXC__Installed_Product__c objIP = new SVMXC__Installed_Product__c(Name='H14072', SVMXC__Status__c ='Installed',SVMXC__Company__c=acc.Id, ERP_Product_Code__c = 'HTPB', SVMXC__Site__c =varLoc.id, SVMXC__Preferred_Technician__c = techEqipt.id,
                                                Service_Team__c =objServTeam.Id,Subscription__c= subscription1.Id);
        insert objIP;
        
        
        
        TPaaS_Package_Detail__c oTPD = new TPaaS_Package_Detail__c();
        oTPD.Name = 'Test oTPD';
        oTPD.Priority__c = 'Standard (3 days)';
        oTPD.PrescribedClinician__c = oCon.Id;
        oTPD.OAR_Metric_Template__c = 'CFRT';
        oTPD.Plan_Stage__c = 'Completed';
        oTPD.Assigned_Dosiometrist__c = userinfo.getUserId();
        oTPD.Plan_Approved_Date__c = system.today();
        oTPD.Dosimetrist_Notes__c = 'Test';
        insert oTPD;
        
        oTPD.Plan_Approved_Date__c = system.today().adddays(1);
        update oTPD;
        
        test.stopTest();
        
        
        
    }
    @isTest public static void testMVTPassPackageController(){
        
        
        account acc = new account();
        acc.Name = 'Test conName';
        acc.Site_Type__c = 'HOSPITAL';
        acc.CurrencyIsoCode = 'USD';
        acc.Country__c = 'India';
        acc.BillingCity = 'Jaipur';
        acc.BillingState = 'Rajasthan';
        acc.ERP_Site_Partner_Code__c = 'Test';
        acc.BillingStreet = 'Test Billing Street';
        insert acc;
        
        contact oCon = new contact();
        oCon.AccountId = acc.Id;
        oCon.FirstName = 'Test fName1';
        oCon.LastName = 'Test lName1';
        oCon.Functional_Role__c = 'Educator';
        oCon.Email = '<EMAIL>';
        oCon.MobilePhone = '**********';
        oCon.Phone = '**********';
        oCon.Account_Admin__c = true;
        insert oCon;
        
        
        user u = new user();
        u.Alias = 'test'; 
        u.ContactId = oCon.Id;
        u.Email = '<EMAIL>';
        u.EmailEncodingKey = 'UTF-8';
        u.LastName = 'Testing';
        u.LanguageLocaleKey = 'en_US';
        u.LocaleSidKey = 'en_US';
        u.ProfileId = [SELECT Id FROM Profile WHERE Name = 'VMS MyVarian - Customer User'].Id;
        u.TimeZoneSidKey ='America/Los_Angeles';
        u.Username ='<EMAIL>';
        insert u;
        
        List<TPaaS_Setting__c> lstTpaasCS = [select id,name,Value__c from TPaaS_Setting__c where name = 'Petcure'];
        TPaaS_Setting__c TpaasCS = new TPaaS_Setting__c(name='Petcure');
        string Account_Ref_Number = [select SFDC_Account_Ref_Number__c from Account where Id=: Acc.Id].SFDC_Account_Ref_Number__c;
        if(lstTpaasCS.size()>0){
            TpaasCS = lstTpaasCS[0];
            TpaasCS.Value__c = TpaasCS.Value__c + ';'+Account_Ref_Number;
        }else{TpaasCS.Value__c = Account_Ref_Number;}
        upsert TpaasCS;
        
        system.runAs(u){
       
        date dtDate = system.Today();
        
        system.debug('########'+Limits.getDMLStatements());
        
        
        
        
        
        
        test.startTest();
        
        TPaaS_Package_Detail__c oTPD = new TPaaS_Package_Detail__c();
        oTPD.Name = 'Test oTPD';
        oTPD.Priority__c = 'Standard (3 days)';
        oTPD.OAR_Metric_Template__c = 'CFRT';
        oTPD.Plan_Stage__c = 'Completed';
        oTPD.Assigned_Dosiometrist__c = u.Id;
        oTPD.Dosimetrist_Notes__c = 'Test';
        insert oTPD;
        
        oTPD.Name = 'Test oTPD';
        update oTPD;
        
        string jsonOobjTPD = json.serialize(oTPD);
        system.debug('jsonOobjTPD@@@--- ' +jsonOobjTPD);
        
        TPassPackage_Comment__c oTPC = new TPassPackage_Comment__c();
        oTPC.TPaaS_Package_Detail__c = oTPD.Id;
        oTPC.Body__c = 'test Body';
        oTPC.CurrencyIsoCode = 'USD';
        insert oTPC;
        
        string sDate = dtDate.month()+ '/' + dtDate.day()+ '/' + dtDate.year();
        
        MVTPassPackageController.getTPassList('DocTypeSearch', 'CaseStatusLink', 'Standard (3 days)','planStage', 10, 50, 5, sDate, sDate,false);
        MVTPassPackageController.getNewTPass(oTPD.Id);
        MVTPassPackageController.getTPassDetails(oTPD.Id);
        MVTPassPackageController.getDosiometristInfo();
        MVTPassPackageController.getPriorities();
        MVTPassPackageController.getPlanStages('All');
        MVTPassPackageController.SaveNewTPassDetail(jsonOobjTPD, oTPD.Priority__c, oTPD.OAR_Metric_Template__c, acc.Id);
        
            MVTPassPackageController.getDosiometristInfo();
            MVTPassPackageController.getDosiometrists();
        
            MVTPassPackageController.savePriority(oTPD.Id, oTPD.Priority__c, oTPD.Plan_Stage__c, oTPD.Plan_Stage__c, u.Id, 'Test');
            MVTPassPackageController.savePlanStage(oTPD.Id, oTPD.Plan_Stage__c);
            
            
            //MVTPassPackageController.getCompassionFirstInfo();
            MVTPassPackageController.saveTrack(oTPD.Id,null,null,null);
            MVTPassPackageController.getPicklistPathStatus(oTPD.Id);
            MVTPassPackageController.getPlanCurrentRating(oTPD.Id);
            //MVTPassPackageController.updatePlanRating(oTPD.Id,'test',3);
            
        
            MVTPassPackageController.saveComment(oTPD.Id, oTPC.Body__c);
            MVTPassPackageController.get15DigitId('123456789012345678');
            
            MVTPassPackageController.getTPassComments(oTPD.Id,1,20,10);
            
            oTPD = [select Id,Plan_Stage__c from TPaaS_Package_Detail__c where id =: oTPD.Id];
            oTPD.Plan_Stage__c = 'Structures Ready for Review';
            update oTPD;
            
            
            oTPD = [select Id,Plan_Stage__c from TPaaS_Package_Detail__c where id =: oTPD.Id];
            oTPD.Plan_Stage__c = 'Completed';
            update oTPD;
            
            
            string EsDate = dtDate.addMonths(-2).month()+ '/' + dtDate.addMonths(-2).day()+ '/' + dtDate.addMonths(-2).year();
            string EeDate = dtDate.addMonths(2).month()+ '/' + dtDate.addMonths(2).day()+ '/' + dtDate.addMonths(2).year();
            
            string Document_TypeSearch = ApexPages.currentPage().getParameters().get('st');
            string Priority = ApexPages.currentPage().getParameters().get('Priority');
            string PlanStage = ApexPages.currentPage().getParameters().get('PlanStage');
            string frmDate = ApexPages.currentPage().getParameters().get('fdate');
            string toDate = ApexPages.currentPage().getParameters().get('tdate');
            string ViewMyOrders = ApexPages.currentPage().getParameters().get('ViewMyOrders');
            
            //Excel
            PageReference pageRef = Page.TreatmentPlanningExcel;
            Test.setCurrentPage(pageRef);
            ApexPages.currentPage().getParameters().put('st', 'Test');
            ApexPages.currentPage().getParameters().put('Priority', '--Any--');
            ApexPages.currentPage().getParameters().put('PlanStage', '--Any--');
            ApexPages.currentPage().getParameters().put('fdate', EsDate);
            ApexPages.currentPage().getParameters().put('tdate', EeDate);
            ApexPages.currentPage().getParameters().put('ViewMyOrders', 'false');
            ApexPages.currentPage().getParameters().put('TPaasStatusLink', 'All');
            TreatmentPlanningExcel Excel = new TreatmentPlanningExcel();
            
            
            
            Excel.getTpassDetails();
            test.stopTest();
        
        }
    }
}
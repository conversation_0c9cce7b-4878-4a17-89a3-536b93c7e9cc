/*
Test Class For Below Apex Classes
	lwc_AccountHierarchyViewController
*/
@isTest
public class lwc_AccountHierarchyViewController_Test {
 @TestSetup
    static void createTestData(){
        List<Account> AccountList = new List<Account>();
        Account thisAccount = CPQ_TestDataUtility.createAccount('Test Account');
        thisAccount.AccountNumber = '12345';
        thisAccount.Account_Sub_Type__c = 'Standard';
        AccountList.add(thisAccount);
        
        Account thisAccount2 = CPQ_TestDataUtility.createAccount('Test Account1');
        thisAccount2.AccountNumber = '12346';
        thisAccount.Account_Sub_Type__c = 'Standard';
        AccountList.add(thisAccount2);
        insert AccountList;
        
        ERP_Partner_Association__c ERPObj1 = new ERP_Partner_Association__c();
        List<ERP_Partner_Association__c> ERPList = new List<ERP_Partner_Association__c>();
        List<ERP_Partner_Association__c> ERPList2 = new List<ERP_Partner_Association__c>();
        ERPObj1.Customer_Account__c = thisAccount.Id;
        ERPObj1.Partner_Function__c = 'SP=Sold-to party';
        ERPObj1.ERP_Partner_Number__c = '12345';
        ERPObj1.Sales_Org__c = '601';
        ERPList.add(ERPObj1);
        
        ERP_Partner_Association__c ERPObj2 = new ERP_Partner_Association__c();
        ERPObj2.Customer_Account__c = AccountList[0].Id;
        ERPObj2.Partner_Function__c = 'SH=Ship-to party';
        ERPObj2.ERP_Partner_Number__c = '12345';
        ERPObj2.Sales_Org__c = '601';
        ERPList.add(ERPObj2);
        
        ERP_Partner_Association__c ERPObj3 = new ERP_Partner_Association__c();
        ERPObj3.Customer_Account__c = AccountList[0].Id;
        ERPObj3.Partner_Function__c = 'BP=Bill-to Party';
        ERPObj3.ERP_Partner_Number__c = '12345';
        ERPObj3.Sales_Org__c = '601';
        ERPList.add(ERPObj3);
        
        ERP_Partner_Association__c ERPObj4 = new ERP_Partner_Association__c();
        ERPObj4.Customer_Account__c = AccountList[0].Id;
        ERPObj4.Partner_Function__c = 'Z1=Site Partner';
        ERPObj4.ERP_Partner_Number__c = '12345';
        ERPObj4.Sales_Org__c = '601';
        ERPList.add(ERPObj4);
        
        ERP_Partner_Association__c ERPObj5 = new ERP_Partner_Association__c();
        ERPObj5.Customer_Account__c = AccountList[0].Id;
        ERPObj5.Partner_Function__c = 'PY=Payer';
        ERPObj5.ERP_Partner_Number__c = '12345';
        ERPObj5.Sales_Org__c = '601';
		ERPList.add(ERPObj5);
        
        ERP_Partner_Association__c ERPObj6 = new ERP_Partner_Association__c();
        ERPObj6.Customer_Account__c = AccountList[0].Id;
        ERPObj6.Partner_Function__c = 'PY=Payer';
        ERPObj6.ERP_Partner_Number__c = '12345';
        ERPObj6.Sales_Org__c = '602';
		ERPList.add(ERPObj6);
        Insert ERPList;
        
        ERP_Partner_Association__c ERPObj7 = new ERP_Partner_Association__c();
        ERPObj7.Customer_Account__c = AccountList[1].Id;
        ERPObj7.Partner_Function__c = 'PY=Payer';
        ERPObj7.ERP_Partner_Number__c = '12345';
        ERPObj7.Sales_Org__c = '602';
		ERPList2.add(ERPObj7);
        
        ERP_Partner_Association__c ERPObj8 = new ERP_Partner_Association__c();
        ERPObj8.Customer_Account__c = AccountList[1].Id;
        ERPObj8.Partner_Function__c = 'PY=Payer';
        ERPObj8.ERP_Partner_Number__c = '12346';
        ERPObj8.Sales_Org__c = '602';
		ERPList2.add(ERPObj8);
        Insert ERPList2;
    }
    
    static testMethod void testMethodCall(){
        Test.startTest();
        Account acc = [SELECT Id,Name FROM Account where AccountNumber = '12345' LIMIT 1];
        lwc_AccountHierarchyViewController.getRelatedERPandFSLLocation(acc.Id);
        Test.stopTest(); 
    }
     static testMethod void testMethodCall2(){
        Test.startTest();
        Account acc2 = [SELECT Id,Name FROM Account where AccountNumber = '12346' LIMIT 1];
        lwc_AccountHierarchyViewController.getRelatedERPandFSLLocation(acc2.Id);
        Test.stopTest(); 
    }
}
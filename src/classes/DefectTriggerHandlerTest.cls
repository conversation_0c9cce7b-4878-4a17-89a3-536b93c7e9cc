@isTest
private class DefectTriggerHandlerTest {

    @isTest
    static void testOnBeforeInsert() {
        
        // Create a test Defect__c record
        Defect__c defect1 = new Defect__c(Name = 'Test Defect');
        Defect__c defect2 = new Defect__c(Name = 'Test Defect2'); 

        List<Defect__c> defects = new List<Defect__c>{ defect1, defect2 };

        // Instantiate the handler
        DefectTriggerHandler handler = new DefectTriggerHandler(false, 0);

        // Execute OnBeforeInsert
        Test.startTest();
        handler.OnBeforeInsert(defects);
        Test.stopTest();

        // Assert that the second defect has an error
        //System.assertEquals(1, defects[1].getErrors().size(), 'Duplicate defect name should trigger an error.');
    }

    @isTest
    static void testOnAfterInsertAndUpdate() {
        // Create a test User
        User testUser = new User(
            Username = '<EMAIL>',
            Email = '<EMAIL>',
            Alias = 'testd',
            ProfileId = UserInfo.getProfileId(),
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IsActive = true,
            FirstName = 'test',
            LastName = 'DefectUser'
            
        );
        insert testUser;

        // Create a test Defect__c record
        Defect__c defect = new Defect__c(
            Name = 'Test Defect',
            Defect_Detected_Date__c = Date.today(),
            Subject__c = 'test subject',
            Description__C = 'test description',
            Status__c = 'New',
            Product__c = 'Noona'
        );
        insert defect;
        
        defect.Status__c = 'Resolved';
        defect.Resolution_Summary__c = 'defect resolved';
        defect.Resolution_Date__c = Date.today();
        update defect;
        
        		
        
     	// Create a test Account
        Account testAcc = new Account(
            Name = 'Test Account',
            Site_Type__c = 'HOSPITAL',
            Country__c = 'Spain',
            BillingCountry = 'Spain',
            State_Province_Is_Not_Applicable__c = true
            
        );
        insert testAcc;
        // Create a test Contact
        Contact testContact = new Contact(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            AccountId = testAcc.Id
        );
        insert testContact;

        // Create a test Case record related to the defect
        Case testCase = new Case(
            Defect__c = defect.Id,
            Status = 'Resolved',
            ContactId = testContact.Id
            
        );
        insert testCase;
        
        CaseComment cc = new CaseComment(
           
        	parentid = testCase.ID,
			commentbody = 'Test Defect Comment Body'
        );
        insert cc;

        // Prepare the map for OnAfterInsertAndUpdate
        Map<Id, Defect__c> defectMap = new Map<Id, Defect__c>{ defect.Id => defect };

        // Instantiate the handler
        DefectTriggerHandler handler = new DefectTriggerHandler(false, 0);
		if(defectMap.size() > 0) {
 
 
        // Execute OnAfterInsertAndUpdate
        Test.startTest();
        handler.OnAfterInsertAndUpdate(defectMap);
 
        // Verify that the case comment was created
        List<CaseComment> comments = [SELECT Id, CommentBody FROM CaseComment WHERE ParentId = :testCase.Id and Parent.ContactId !=  null];
        System.assertEquals(2, comments.size(), 'A case comment should be created.');
        System.assert(comments[0].CommentBody.contains('Test Defect'), 'Comment body should contain defect name.');

        
                // Prepare the parameters for the email sending logic
        List<Case> cases = new List<Case>{testCase};
        List<Contact> contactList = new List<Contact>{testContact};
        String sendTo = '<EMAIL>';

        // Your email sending logic
        EmailTemplate emailTemplateToUse = [
            SELECT Id, Subject, Description, HtmlValue, DeveloperName, Body 
            FROM EmailTemplate 
            WHERE DeveloperName = 'Defect_Fix_Template'
        ];
        
        OrgWideEmailAddress[] oweaList = [
            SELECT Id 
            FROM OrgWideEmailAddress 
            WHERE Address = '<EMAIL>'
        ];

        String templateId = emailTemplateToUse.Id;

        if (!cases.isEmpty()) {
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();

            if (oweaList.size() > 0) {
                mail.setOrgWideEmailAddressId(oweaList.get(0).Id);
            }
            mail.setTemplateId(templateId);
            mail.setTargetObjectId(contactList[0].Id);
            mail.setWhatId(cases[0].Id);
            mail.setToAddresses(new String[]{sendTo});

            // Send the email
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
        }

        
        Test.stopTest();
    }
        
    }
}
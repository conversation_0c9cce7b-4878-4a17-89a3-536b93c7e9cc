@istest
private class LibraryListControllerTestClass{
 static testMethod void myLibraryListControllerTest() {
     RestRequest req = new RestRequest();
      RestResponse res = new RestResponse();
    // Blob myBlob = Blob.valueof('test');
      //List<VU_SpeakerSchedule_Map__c > sp=new  List<VU_SpeakerSchedule_Map__c >(Name='test',VU_Event_Schedule__c='Testshedule');
      req.requestURI = '/services/apexrest/LibraryList'; 
        RestContext.request = req;
        RestContext.response = res;
         req.httpMethod = 'GET';
        List<ContentWorkspace>  result = LibraryListController.retrieve() ;
         //Solution  result1 = SolutionControllers.doGet();
 }
}
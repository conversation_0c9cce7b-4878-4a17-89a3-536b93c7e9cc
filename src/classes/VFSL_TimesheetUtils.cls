/*
 * Author : <PERSON><PERSON>
 * Description :
 *      Time entry Util Class
 */
public class VFSL_TimesheetUtils {
    public static boolean execute = true; 
    public static Boolean TIMESHEET_UPDATED_FROM_TIME_ENTRY = false; 
    public static Map < Id, ServiceResource > mapOfTechProfile = new Map < Id, ServiceResource > (); 
    public static set < Id > AlredayProcessed = new set < Id > (); 
    public List < Id > woliUsageRecordTypes = new List < Id > {
        VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('FS_Usage'), 
        VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('HD_Usage'),
        VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('PM_Usage'),
        VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('TE_Usage'),
        VFSL_Utility.getRecordTypeMap().get('WorkOrderLineItem').get('VIS_Usage')
    };
    private static final Id teWorkOrderRecordType = VFSL_Utility.getRecordTypeMap().get('WorkOrder').get('Training');
    private static final Id pmWorkOrderRecordType = VFSL_Utility.getRecordTypeMap().get('WorkOrder').get('Project_Management'); 
    private class WD_TechnAndWdDetails {
        String TechnicianId {
            get;
            set;
        }
        String WdFormattedDate {
            get;
            set;
        }
    }
  
    public static void createTimeEventsFromWorkDetails(List<WorkOrderLineItem> detailList, Map < Id, WorkOrderLineItem > mapOldWorkDetail) {
        System.debug('detailList=====' + detailList);
        List < WorkOrderLineItem > validDetailList = new List < WorkOrderLineItem > ();

        List < SVMXC_Time_Entry__c > updateEntryList = new List < SVMXC_Time_Entry__c > ();
        List < SVMXC_Time_Entry__c > deleteEntryList = new List < SVMXC_Time_Entry__c > ();
        List < SVMXC_Time_Entry__c > updateEntryListWithNewTs = new List < SVMXC_Time_Entry__c > ();
        Set < Id > wdIdSet = new Set < Id > ();
        Set < Id > woIdSet = new Set < Id > ();
        Map < Id, WD_TechnAndWdDetails > mapWD_TechnAndWdDetails = new Map < Id, WD_TechnAndWdDetails > ();
        Map < Id, String > mapWdId_TechnWithDate_WD = new Map < Id, String > ();
        String strDynamicQuery = '';
        String strLikeClause = '';
        List < SVMXC_Timesheet__c > lstTimeSheets = new List < SVMXC_Timesheet__c > ();
        Map < String, Id > mapTechnWithDate_TimeSheetId_DB = new Map < String, Id > ();
        Map < String, SVMXC_Timesheet__c > mapTechnWithDate_TimeSheetId_DB_Object = new Map < String, SVMXC_Timesheet__c > ();
        List < SVMXC_Time_Entry__c > entryList = new List < SVMXC_Time_Entry__c > ();
        List < ServiceResource > techniciansList = new List < ServiceResource > ();
        Map < Id, Id > technicianUserMap = new Map < Id, Id > ();
        Map < Id, WorkOrder > woMap = new Map < Id, WorkOrder > ();
        Map < id, List < Organizational_Activities__c >> MpTechnician = new map < id, List < Organizational_Activities__c >> ();
        set < id > Technicianids = new set < id > ();
        set < id > TechnicianUserIds = new set < id > ();
        set < id > techProfile_Id = new set < id > ();
        Id DirectrcdtypeId = VFSL_Utility.getRecordTypeMap().get('SVMXC_Time_Entry__c').get('Direct_Hours');
        Id indirectHoursTE = VFSL_Utility.getRecordTypeMap().get('SVMXC_Time_Entry__c').get('Indirect_hours');

        for (WorkOrderLineItem detail: detailList) {
            if (detail.Service_Resource__c != null) {
                TechnicianUserIds.add(detail.Service_Resource__c);
            }
        }

        techniciansList = [Select Id, RelatedRecordId from ServiceResource where Id IN: TechnicianUserIds AND Do_Not_Create_Timecard__c=false]; // STRY0194846 - Added Do_Not_Create_Timecard__c filter
        for (ServiceResource techList: techniciansList) {
            technicianUserMap.put(techList.Id, techList.RelatedRecordId);
        }

        for (WorkOrderLineItem detail: detailList) {
            if (detail.Service_Resource__c != null && (detail.Type__c == 'Labor' || detail.Type__c == 'Travel') &&
                detail.StartDate != null && detail.EndDate != null && !technicianUserMap.isEmpty() &&
                technicianUserMap.containsKey(detail.Service_Resource__c) && technicianUserMap.get(detail.Service_Resource__c) == UserInfo.getUserId() &&
                (mapOldWorkDetail.isEmpty() || detail.StartDate != mapOldWorkDetail.get(detail.Id).StartDate ||
                    detail.EndDate != mapOldWorkDetail.get(detail.Id).EndDate ||
                    detail.On_Job_Training__c != mapOldWorkDetail.get(detail.id).On_Job_Training__c)) {
                if (AlredayProcessed.contains(detail.Id)) {
                    system.debug('Time Entry Processed');
                } else {
                    validDetailList.add(detail);
                    AlredayProcessed.add(detail.Id);
                }
            }
        }

       
        if (validDetailList.size() > 0) {
            detailList = new List < WorkOrderLineItem > ();
            detailList.addALL(validDetailList);
        } else {
            return;
        }

        if (detailList.size() > 0) {
            for (WorkOrderLineItem detail: detailList) {
                wdIdSet.add(detail.Id);
                woIdSet.add(detail.WorkOrderId);
                WD_TechnAndWdDetails objWD_TechnAndWdDetails = new WD_TechnAndWdDetails();
                objWD_TechnAndWdDetails.TechnicianId = String.valueOf(detail.Service_Resource__c);
                objWD_TechnAndWdDetails.WdFormattedDate = String.valueOf(detail.StartDate.format('MM-dd-yyyy'));

                mapWD_TechnAndWdDetails.put(detail.Id, objWD_TechnAndWdDetails);
                String strUniqueTechnWithDate_WdId = String.ValueOf(detail.Service_Resource__c) + ':' + detail.StartDate.format('MM-dd-yyyy');
                mapWdId_TechnWithDate_WD.put(detail.Id, strUniqueTechnWithDate_WdId);
                Technicianids.add(detail.Service_Resource__c);
            }
        }

        set < Id > technicianIds_new = new set < Id > ();
        if (!Technicianids.isEmpty()) {
            for (ServiceResource tech: [Select id, RelatedRecordId, Timecard_Profile__c, Timecard_Hours__c,
                    Timecard_Profile__r.X30_min_Travel_Time__c, Timecard_Profile__r.Normal_End_Time__c,
                    Timecard_Profile__r.Normal_Start_Time__c, Timecard_Hours__r.MondayEndTime,
                    Timecard_Hours__r.MondayStartTime, Timecard_Hours__r.TuesdayStartTime,
                    Timecard_Hours__r.WednesdayStartTime, Timecard_Hours__r.ThursdayStartTime,
                    Timecard_Hours__r.FridayStartTime, Timecard_Hours__r.SaturdayStartTime,
                    Timecard_Hours__r.SundayStartTime, Timecard_Hours__r.TuesdayEndTime,
                    Timecard_Hours__r.WednesdayEndTime, Timecard_Hours__r.ThursdayEndTime,
                    Timecard_Hours__r.FridayEndTime, Timecard_Hours__r.SaturdayEndTime, Work_Center__c, 
                    Timecard_Hours__r.SundayEndTime from ServiceResource where id in: Technicianids
                ]) {
                if (tech.Timecard_Profile__c != null) {
                    techProfile_Id.add(tech.Timecard_Profile__c);
                    technicianIds_new.add(tech.id);
                    if (tech.Timecard_Profile__r.X30_min_Travel_Time__c) {
                        mapOfTechProfile.put(tech.id, tech);
                    }
                }
            }
        }

        if (techProfile_Id != null && techProfile_Id.size() > 0) {
            for (Organizational_Activities__c OrgActvt: [Select Id, Name, Type__c, Travel__c, Not_Counted_For_Utilization__c,
                    Ignore_For_Utilization__c From Organizational_Activities__c
                    Where Indirect_Hours__c in: techProfile_Id and Type__c in ('Indirect', 'OJT')
                ]) {
                for (id techid: technicianIds_new) {
                    List < Organizational_Activities__c > OAlist = new List < Organizational_Activities__c > ();
                    if (MpTechnician.containskey(techid))
                        OAlist = MpTechnician.get(techid);

                    OAlist.add(OrgActvt);
                    MpTechnician.put(techid, OAlist);
                }
            }
        }

        if (mapWD_TechnAndWdDetails.size() > 0) {
            for (Id wdId: mapWD_TechnAndWdDetails.KeySet()) {
                if (strDynamicQuery == '') {
                    strDynamicQuery = 'SELECT Id, Service_Resource__c, AllDateRange__c, Status__c FROM SVMXC_Timesheet__c WHERE ';
                } else {
                    strDynamicQuery = strDynamicQuery + ' OR ';
                }

                strLikeClause = ' (Service_Resource__c = \'' + String.escapeSingleQuotes(mapWD_TechnAndWdDetails.get(wdId).TechnicianId) + '\' AND ';
                strLikeClause = strLikeClause + ' AllDateRange__c LIKE \'%' + String.escapeSingleQuotes(mapWD_TechnAndWdDetails.get(wdId).WdFormattedDate) + '%\')';
                strDynamicQuery = strDynamicQuery + strLikeClause;
            }
        }

        System.Debug('Dynamic SOQL : ' + strDynamicQuery);
        if (strDynamicQuery != null) {
            try {
                lstTimeSheets = Database.query(strDynamicQuery);
            } catch (Exception ex) {
                System.debug(ex.getMessage());
            }
        }

        if (lstTimeSheets.size() > 0) {
            for (SVMXC_Timesheet__c TS: lstTimeSheets) {
                Id TsId = TS.Id;
                if (TS.AllDateRange__c != null && TS.AllDateRange__c != '') {
                    String strAllDateRangeAllVals = String.ValueOf(TS.AllDateRange__c);
                    List < String > AllDateRannge = strAllDateRangeAllVals.Split(',');
                    if (AllDateRannge != null && AllDateRannge.size() > 0) {
                        for (String strDate: AllDateRannge) {
                            String strUniqueTechnWithDate_TechId = String.ValueOf(TS.Service_Resource__c) + ':' + strDate;
                            mapTechnWithDate_TimeSheetId_DB.put(strUniqueTechnWithDate_TechId, TsId);
                            mapTechnWithDate_TimeSheetId_DB_Object.put(strUniqueTechnWithDate_TechId, TS);
                        }
                    }
                }
            }
        }

        if (wdIdSet.size() > 0) {
            entryList = [SELECT Id FROM SVMXC_Time_Entry__c WHERE WorkOrderLineItem__c IN: wdIdSet];
            delete entryList;
            entryList = new list < SVMXC_Time_Entry__c > ();
        }

        if (woIdSet.size() > 0) {
            woMap = new Map < Id, WorkOrder > ([SELECT Id, Service_Resource__c, AccountId FROM WorkOrder WHERE Id IN: woIdSet]);
        }

        if (detailList.size() > 0) {
            for (WorkOrderLineItem detail: detailList) {
                if ((detail.Type__c == 'Labor' || detail.Type__c == 'Travel') && detail.On_Job_Training__c) {
                    SVMXC_Time_Entry__c entryForOJT = null;
                    for (SVMXC_Time_Entry__c e: entryList) {
                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId == indirectHoursTE) {
                            entryForOJT = e;
                        }
                    }

                    if (entryForOJT == null) {
                        entryForOJT = new SVMXC_Time_Entry__c();
                    }

                    entryForOJT.recordtypeId = indirectHoursTE;
                    entryForOJT.WorkOrderLineItem__c = detail.Id;
                    entryForOJT.Service_Resource__c = detail.Service_Resource__c;
                    entryForOJT.Start_Date_Time__c = detail.StartDate;
                    entryForOJT.End_Date_Time__c = detail.EndDate;
                    entryForOJT.Interface_Status__c = 'Do Not Process';

                    if (MpTechnician.containskey(detail.Service_Resource__c)) {
                        for (Organizational_Activities__c OA: MpTechnician.get(detail.Service_Resource__c)) {
                            system.debug('==OA.Type__c==' + OA.Type__c);
                            if (OA.Type__c == 'OJT' && detail.Type__c == 'Labor') {
                                entryForOJT.Organizational_Activity__c = OA.id;
                            } else if (OA.Type__c == 'Indirect' && detail.Type__c == 'Travel' && OA.Travel__c == true) {
                                entryForOJT.Organizational_Activity__c = OA.id;
                            }
                        }
                    }

                    String TechnWithDate = mapWdId_TechnWithDate_WD.get(detail.Id);
                    system.debug('TechnWithDate::' + TechnWithDate);
                    if (mapTechnWithDate_TimeSheetId_DB.containsKey(TechnWithDate)) {
                        system.debug('TechnWithDate::' + TechnWithDate);
                        if (mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate) != null) {
                            system.debug('TechnWithDate::' + TechnWithDate);
                            entryForOJT.Timesheet__c = mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate);
                            entryForOJT.Status__c = mapTechnWithDate_TimeSheetId_DB_Object.get(TechnWithDate).Status__c;
                            if (entryForOJT.Start_Date_Time__c != null && entryForOJT.End_Date_Time__c != null &&
                                entryForOJT.Start_Date_Time__c != entryForOJT.End_Date_Time__c) {
                                system.debug('TechnWithDate::' + TechnWithDate);
                                updateEntryList.add(entryForOJT);
                            }
                        }
                        System.Debug('ojt entry::::====' + updateEntryList);
                    } else if (entryForOJT.Timesheet__c != null) {
                        updateEntryList.add(entryForOJT);
                        System.Debug(updateEntryList);
                    } else if (entryForOJT.Start_Date_Time__c != null && entryForOJT.End_Date_Time__c != null &&
                        entryForOJT.Start_Date_Time__c != entryForOJT.End_Date_Time__c)
                        updateEntryListWithNewTs.add(entryForOJT);
                    System.Debug(updateEntryListWithNewTs);
                } else if ((detail.Type__c == 'Labor' || detail.Type__c == 'Travel') && detail.On_Job_Training__c == false) {
                    SVMXC_Time_Entry__c entry = null;
                    SVMXC_Time_Entry__c entry1 = null;
                    SVMXC_Time_Entry__c entry2 = null;

                    for (SVMXC_Time_Entry__c e: entryList) {
                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId == indirectHoursTE &&
                            e.Organizational_Activity__r.Type__c == 'OJT') {
                            deleteEntryList.add(e);
                        }

                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId != indirectHoursTE) {
                            entry = e;
                        }

                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId == indirectHoursTE &&
                            e.Organizational_Activity__r.name == 'Travel to work') {
                            entry1 = e;
                        }

                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId == indirectHoursTE &&
                            e.Organizational_Activity__r.name == 'Travel from work') {
                            entry2 = e;
                        }
                    }

                    if (entry == null)
                        entry = new SVMXC_Time_Entry__c();

                    if (entry1 == null)
                        entry1 = new SVMXC_Time_Entry__c();

                    if (entry2 == null)
                        entry2 = new SVMXC_Time_Entry__c();

                    if (woMap.containsKey(detail.WorkOrderId)) {
                        WorkOrder wo = woMap.get(detail.WorkOrderId);
                        entry.Associated_Account__c = wo.AccountId;
                    }
                    entry.WorkOrderLineItem__c = detail.Id;

                    if (detail.Type__c == 'Labor')
                        entry.Activity__c = 'Work Detail-Labor';
                    else
                        entry.Activity__c = 'Work Detail-Travel';

                    entry.Start_Date_Time__c = detail.StartDate;
                    entry.End_Date_Time__c = detail.EndDate;
                    System.Debug('Before Split : ' + Entry + Entry1 + Entry2);

                    // for splitting base on travel time
                    if (entry.Activity__c == 'Work Detail-Travel') {
                        Datetime startTimeOfDay;
                        Datetime endTimeOfDay;
                        if (detail.Service_Resource__c != null && mapOfTechProfile.containskey(detail.Service_Resource__c) &&
                            (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__c != null)) {
                            if (detail.StartDate.format('EEEE') == 'Monday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.MondayStartTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.MondayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.MondayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Tuesday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.TuesdayEndTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.TuesdayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.TuesdayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Wednesday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.WednesdayStartTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.WednesdayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.WednesdayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Thursday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.ThursdayEndTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.ThursdayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.ThursdayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Friday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.FridayEndTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.FridayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.FridayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Saturday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SaturdayStartTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SaturdayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SaturdayEndTime);
                            } else if (detail.StartDate.format('EEEE') == 'Sunday' && (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SundayStartTime != null)) {
                                startTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SundayStartTime);
                                endTimeOfDay = datetime.newinstance(detail.StartDate.date(), mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__r.SundayEndTime);
                            }
                        }

                        if (startTimeOfDay != null && endTimeOfDay != null && MpTechnician.containskey(detail.Service_Resource__c) &&
                            (mapOfTechProfile.get(detail.Service_Resource__c).Timecard_Hours__c != null) &&
                            (startTimeOfDay > detail.StartDate || endTimeOfDay < detail.EndDate)) {
                            entry1.Service_Resource__c = detail.Service_Resource__c;
                            for (Organizational_Activities__c OA: MpTechnician.get(detail.Service_Resource__c)) {
                                if (OA.Name == 'Travel to work')
                                    entry1.Organizational_Activity__c = OA.id;
                                //entry1.Ignore__c = True;
                                entry1.Ignore_For_Utilization__c = True;
                            }
                            entry1.WorkOrderLineItem__c = detail.Id;
                            entry1.Service_Resource__c = detail.Service_Resource__c;
                            entry2.WorkOrderLineItem__c = detail.Id;
                            entry2.Service_Resource__c = detail.Service_Resource__c;

                            if (MpTechnician != null && detail.Service_Resource__c != null) {
                                for (Organizational_Activities__c OA: MpTechnician.get(detail.Service_Resource__c)) {
                                    if (OA.Name == 'Travel from work') {
                                        entry2.Organizational_Activity__c = OA.id;
                                        entry2.Ignore_For_Utilization__c = True;
                                    }
                                }
                            }

                            entry1.recordtypeId = indirectHoursTE;
                            entry2.recordtypeId = indirectHoursTE;
                            Double totalnormalstrttime = startTimeOfDay.hour() * 60 + startTimeOfDay.minute();
                            Double totalstarttime = detail.StartDate.hour() * 60 + detail.StartDate.minute();
                            Double totalnormalendtime = endTimeOfDay.hour() * 60 + endTimeOfDay.minute();
                            Double totalendtime = detail.EndDate.hour() * 60 + detail.EndDate.minute();

                            if ((totalnormalstrttime - totalstarttime) > 30 || ((totalnormalstrttime > totalendtime) && (totalendtime - totalstarttime) < 30)) {
                                system.debug('==1==');
                                if (detail.Duration < 0.5) {
                                    entry.Start_Date_Time__c = detail.StartDate + (detail.Duration / 24);
                                } else {
                                    entry.Start_Date_Time__c = detail.StartDate + (0.5 / 24);
                                }

                                entry1.Start_Date_Time__c = detail.StartDate;
                                entry1.End_Date_Time__c = entry.Start_Date_Time__c;
                            } else if ((totalnormalstrttime - totalstarttime) <= 30 && (totalnormalstrttime - totalstarttime) > 0) {
                                system.debug('==2==');
                                entry.Start_Date_Time__c = datetime.newinstance(detail.StartDate.date(), startTimeOfDay.time());
                                entry1.Start_Date_Time__c = detail.StartDate;
                                entry1.End_Date_Time__c = entry.Start_Date_Time__c;
                            }

                            if ((totalendtime - totalnormalendtime) > 30 || ((totalnormalendtime < totalendtime) && (totalendtime - totalstarttime) < 30)) {
                                system.debug('==3==');
                                if (detail.Duration < 0.5)
                                    entry.End_Date_Time__c = detail.EndDate - (detail.Duration / 24);
                                else
                                    entry.End_Date_Time__c = detail.EndDate - (0.5 / 24);

                                entry2.Start_Date_Time__c = entry.End_Date_Time__c;
                                entry2.End_Date_Time__c = detail.EndDate;
                            } else if ((totalendtime - totalnormalendtime) <= 30 && (totalendtime - totalnormalendtime) > 0) {
                                system.debug('==4==');
                                entry.End_Date_Time__c = datetime.newinstance(detail.EndDate.date(), endTimeOfDay.time());
                                entry2.Start_Date_Time__c = entry.End_Date_Time__c;
                                entry2.End_Date_Time__c = detail.EndDate;
                            }

                            if ((totalendtime - totalstarttime) <= 30 && ((totalstarttime < totalnormalstrttime && totalendtime <= totalnormalstrttime) ||
                                    (totalstarttime >= totalnormalendtime && totalendtime > totalnormalendtime))) {
                                system.debug('==4==');
                                system.debug('==entryId==' + entry.Id);
                                SVMXC_Time_Entry__c directEntry = new SVMXC_Time_Entry__c(id = entry.Id);
                                if (entry.Id != null)
                                    deleteEntryList.add(directEntry);
                            }
                            System.Debug('Split : ' + Entry + Entry1 + Entry2);
                        }

                        // to delete the entries that were created but because of update are not required
                        if (startTimeOfDay <= detail.StartDate && entry1.id != null) {
                            SVMXC_Time_Entry__c ent = new SVMXC_Time_Entry__c(id = entry1.id);
                            entry1.Start_Date_Time__c = null;
                            deleteEntryList.add(ent);
                        }
                        if (endTimeOfDay >= detail.EndDate && entry2.id != null) {
                            SVMXC_Time_Entry__c ent = new SVMXC_Time_Entry__c(id = entry2.id);
                            entry2.End_Date_Time__c = null;
                            deleteEntryList.add(ent);
                        }
                    }
                    entry.Is_Billable__c = detail.SAP_Billable__c;
                    entry.Service_Resource__c = detail.Service_Resource__c;
                    System.Debug('Soql before insert Timeentry : ' + entry + 'another entry--->' + entry1 + 'second entry--->' + entry2);
                    String TechnWithDate = mapWdId_TechnWithDate_WD.get(detail.Id);
                    System.Debug('Soql search key val : ' + TechnWithDate);
                    system.debug(updateEntryListWithNewTs);

                    if (mapTechnWithDate_TimeSheetId_DB.containsKey(TechnWithDate)) {
                        if (mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate) != null) {
                            entry.Timesheet__c = mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate);
                            entry1.Timesheet__c = mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate);
                            entry2.Timesheet__c = mapTechnWithDate_TimeSheetId_DB.get(TechnWithDate);
                            entry.Status__c = mapTechnWithDate_TimeSheetId_DB_Object.get(TechnWithDate).Status__c;
                            entry1.Status__c = mapTechnWithDate_TimeSheetId_DB_Object.get(TechnWithDate).Status__c;
                            entry2.Status__c = mapTechnWithDate_TimeSheetId_DB_Object.get(TechnWithDate).Status__c;

                            if (entry1.Start_Date_Time__c != null)
                                updateEntryList.add(entry1);

                            if (entry.Start_Date_Time__c != null && entry.End_Date_Time__c != null && entry.Start_Date_Time__c != entry.End_Date_Time__c)
                                updateEntryList.add(entry);

                            if (entry2.Start_Date_Time__c != null)
                                updateEntryList.add(entry2);
                        }
                        System.Debug(updateEntryList);
                    } else if (entry.Timesheet__c != null) {
                        if (entry1.Start_Date_Time__c != null)
                            updateEntryList.add(entry1);
                        updateEntryList.add(entry);
                        if (entry2.Start_Date_Time__c != null)
                            updateEntryList.add(entry2);
                        System.Debug(updateEntryList);
                    } else {
                        if (entry1.Start_Date_Time__c != null)
                            updateEntryListWithNewTs.add(entry1);
                        if (entry.Start_Date_Time__c != null && entry.End_Date_Time__c != null && entry.Start_Date_Time__c != entry.End_Date_Time__c)
                            updateEntryListWithNewTs.add(entry);
                        if (entry2.Start_Date_Time__c != null)
                            updateEntryListWithNewTs.add(entry2);
                        System.Debug(updateEntryListWithNewTs);
                    }
                } else {
                    system.debug('+++++++++');
                    SVMXC_Time_Entry__c timeEntry = new SVMXC_Time_Entry__c();
                    for (SVMXC_Time_Entry__c e: entryList) {
                        if (e.WorkOrderLineItem__c == detail.Id && e.recordtypeId == indirectHoursTE) {
                            timeEntry = e;
                        }
                    }

                    if (detail.On_Job_Training__c)
                        timeEntry.recordtypeId = indirectHoursTE;
                    else
                        timeEntry.recordtypeId = DirectrcdtypeId;

                    timeEntry.Start_Date_Time__c = detail.StartDate;
                    timeEntry.End_Date_Time__c = detail.EndDate;
                    timeEntry.Service_Resource__c = detail.Service_Resource__c;
                    timeEntry.WorkOrderLineItem__c = detail.Id;
                    
                    if (timeEntry.Start_Date_Time__c != null && timeEntry.End_Date_Time__c != null) {
                        updateEntryList.add(timeEntry);
                    }
                }
            }
        }

        system.debug('updateEntryList====' + updateEntryListWithNewTs);
        if (deleteEntryList.size() > 0) {
            delete deleteEntryList;
        }

        if (updateEntryList.size() > 0) {
            calculateTimeEntryTime(updateEntryList);
            list < SVMXC_Time_Entry__c > newTESplitList = splitMidNightEntries(updateEntryList);
            updateEntryList.addAll(newTESplitList);
            Database.UpsertResult[] results = Database.upsert(updateEntryList, false);
            Integer i = 0;
            for (Database.UpsertResult sr: results) {
                if (!sr.isSuccess()) {
                    String errMessage = '';
                    for (Database.Error err: sr.getErrors()) {
                        if (err.getStatusCode() == StatusCode.INSUFFICIENT_ACCESS_ON_CROSS_REFERENCE_ENTITY)
                            errMessage += 'Technician is not the Owner of this Timecard; ';
                        else
                            errMessage += err.getMessage() + ';';
                    }
                }
                i++;
            }
        }

        
        if (updateEntryListWithNewTs.size() > 0) {
            calculateTimeEntryTime(updateEntryListWithNewTs);
            list < SVMXC_Time_Entry__c > newTESplitList = splitMidNightEntries(updateEntryListWithNewTs);
            updateEntryListWithNewTs.addAll(newTESplitList);
            CreatNewTimesheetAsPerWithTimeEntries(updateEntryListWithNewTs);
        }
    }

    public static void checkForWorkDetailOverlap(List < WorkOrderLineItem > wdList, Map < Id, WorkOrderLineItem > oldMap) {
        Map < Integer, List < String >> errorMap = new Map < Integer, List < String >> ();
        Set < Id > setTechnId = new Set < Id > ();
        Set < Id > woIdSet = new Set < Id > ();
        system.debug('**WdList' + wdList);

        Set < Id > oldWDIdSet = new Set < Id > ();
        Set < WorkOrderLineItem > wdSet = new Set < WorkOrderLineItem > (wdList);

        for (WorkOrderLineItem detail: wdList) {
            if (detail.Type__c == 'Labor' || detail.Type__c == 'Travel') {
                woIdSet.add(detail.WorkOrderId);
            }
        }

        if (!oldMap.isEmpty()) {
            for (WorkOrderLineItem detail: oldMap.values()) {
                if (detail.Type__c == 'Labor' || detail.Type__c == 'Travel') {
                    oldWDIdSet.add(detail.Id);
                }
            }
        }

        system.debug('**oldWDIdSet' + oldWDIdSet);
        Map < Id, WorkOrder > woMap;
        if (!woIdSet.isEmpty() && woIdSet.size() > 0)
            woMap = new Map < Id, WorkOrder > ([SELECT Id, WorkOrderNumber, Service_Resource__c FROM WorkOrder WHERE Id IN: woIdSet]);

        Datetime mindate = system.now();
        Set<Id> wdlSetId = new Set<Id>();
        for (WorkOrderLineItem detail: wdList) {
            if ((detail.Type__c == 'Labor' || detail.Type__c == 'Travel') && detail.Activity_Type__c != null && detail.Activity_Type__c != 'Created in error') {
                if (detail.Service_Resource__c == null) {
                    if (!woMap.isEmpty() && woMap.containskey(detail.WorkOrderId) && woMap.get(detail.WorkOrderId).Service_Resource__c != null) {
                        detail.Service_Resource__c = woMap.get(detail.WorkOrderId).Service_Resource__c;
                    } else {
                        detail.addError('Technician/Equipment is required');
                    }
                }
                if (detail.StartDate < mindate) 
                    mindate = detail.StartDate;
                setTechnId.add(detail.Service_Resource__c);
            }
            if(detail.Id != null) wdlSetId.add(detail.Id);
        }

        mindate = mindate - 1;
       
        List < WorkOrderLineItem > otherDetailList = new List < WorkOrderLineItem > ();
        if (setTechnId.size() > 0) {
            otherDetailList = [SELECT Id, StartDate, LineItemNumber, Service_Resource__c, EndDate,
                WorkOrderId, WorkOrder.WorkOrderNumber, Type__c FROM WorkOrderLineItem
                WHERE Service_Resource__c IN: setTechnId AND(StartDate >: mindate OR EndDate >: mindate) AND
                Id NOT IN: wdlSetId and(Type__c = 'Labor' or Type__c = 'Travel') 
                ORDER BY StartDate ASC
            ]; // WorkOrderId in: woIdSet And 
            for (WorkOrderLineItem wl: otherDetailList) {
                system.debug('*******************************');
                system.debug('**wl StartDate' + wl.StartDate);
                system.debug('**wl EndDate' + wl.EndDate);
            }
                
        }

        for (Integer i = 0; i < wdList.size(); ++i) {
            WorkOrderLineItem detail = wdList.get(i);
            if (detail.Type__c == 'Labor' || detail.Type__c == 'Travel') {
                if (!errorMap.containsKey(i)) {
                    errorMap.put(i, new List < String > ());
                    system.debug('**InerrorMap**');
                }

                for (WorkOrderLineItem otherDetail: wdList) {
                    System.debug('current detail1========'+detail);
                    System.debug('existing detail1========'+otherDetail);
                    if(detail != otherDetail && (detail.Service_Resource__c == otherDetail.Service_Resource__c) &&
                        (detail.Type__c == 'Labor' || detail.Type__c == 'Travel') && 
                        otherDetail.EndDate != null && otherDetail.StartDate != null && 
                        detail.StartDate != null && detail.EndDate != null && 
                        (detail.Type__c == 'Labor' || detail.Type__c == 'Travel')) {
                        if(detail.StartDate < otherDetail.EndDate && otherDetail.StartDate < detail.EndDate) {
                            system.debug('**errorMap**');
                            if (!errorMap.isempty() && errorMap.containsKey(i) && errorMap.get(i) != Null && detail.WorkOrderId != Null && otherDetail.StartDate != Null) {
                                errorMap.get(i).add(
                                    +' (' + detail.StartDate.format('hh:mma') +
                                    ' - ' + (detail.EndDate != null ? detail.EndDate.format('hh:mma') : '') + ')' +
                                    ' overlaps with existing entry ' + otherDetail.LineItemNumber + 
                                    ' (' + otherDetail.StartDate.format('hh:mma') +
                                    ' - ' + (otherDetail.EndDate != null ? otherDetail.EndDate.format('hh:mma') : '') + ') ' +
                                    ' on ' + otherDetail.WorkOrder.WorkOrderNumber);
                            }
                        }
                    }
                }

                for (WorkOrderLineItem otherDetail: otherDetailList) {
                  
                    if ((detail != otherDetail) && detail.Service_Resource__c == otherDetail.Service_Resource__c && 
                        otherDetail.EndDate != null && otherDetail.StartDate != null && 
                        detail.StartDate != null && detail.EndDate != null && 
                        (detail.Type__c == 'Labor' || detail.Type__c == 'Travel')) {
                        if(detail.StartDate < otherDetail.EndDate && otherDetail.StartDate < detail.EndDate) {
                            system.debug('**errorMap1**');
                            if (!errorMap.isempty() && errorMap.containsKey(i) && errorMap.get(i) != Null &&
                                detail.WorkOrderId != Null && otherDetail.StartDate != null) {
                                errorMap.get(i).add(
                                    +' (' + detail.StartDate.format('hh:mma') +
                                    ' - ' + (detail.EndDate != null ? detail.EndDate.format('hh:mma') : '') + ')' +
                                    ' overlaps with existing entry ' + otherDetail.LineItemNumber +
                                    ' (' + otherDetail.StartDate.format('hh:mma') +
                                    ' - ' + (otherDetail.EndDate != null ? otherDetail.EndDate.format('hh:mma') : '') + ') ' +
                                    ' on ' + otherDetail.WorkOrder.WorkOrderNumber);
                            }
                        }
                    }
                }

                System.debug(LoggingLevel.WARN, 'Error Map Size: ' + errorMap.get(i).size());
                if (errorMap.get(i).size() > 0) {
                    String msg = system.label.SVMXC_Activity_Overlap + String.join(errorMap.get(i), '; ');
                    System.debug(LoggingLevel.WARN, 'Error Message: ' + msg);
                    detail.addError(msg);
                    //throw new AuraHandledException(msg);
                }
            }
        }
    }

    /* Method is In Person change start and end time updated */
    public static void calculateTimeEntryTime(List < SVMXC_Time_Entry__c > entries) {
        set < Id > wdlsIds = new set < Id > ();
        map < Id, WorkOrderLineItem > wdlMap = new map < Id, WorkOrderLineItem > ();
        for (SVMXC_Time_Entry__c entry: entries) {
            if (entry.WorkOrderLineItem__c != null) {
                wdlsIds.add(entry.WorkOrderLineItem__c);
            }
        }
        System.debug('Shubham -- Entries without any modification-->'+entries);

        if (!wdlsIds.isEmpty()) {
            wdlMap = new map < Id, WorkOrderLineItem > ([select Id, WorkOrder.Account.Timezone__r.Salesforce_timezone__c,
                Method__c, Type__c, Service_Resource__r.RelatedRecord.TimeZoneSidKey,Service_Resource__r.SAP_Timezone__r.Salesforce_timezone__c,
                Service_Resource__r.RelatedRecordId, WorkOrder.Timezone_for_Timecard__c
                from WorkOrderLineItem where id in: wdlsIds
            ]);
            for (SVMXC_Time_Entry__c entry: entries) {
                if (entry.WorkOrderLineItem__c != null && wdlMap.containsKey(entry.WorkOrderLineItem__c)) {
                    WorkOrderLineItem wd = wdlMap.get(entry.WorkOrderLineItem__c);
                    Timezone loggedinUserTm = UserInfo.getTimeZone();
                    Timezone technicianTm = Timezone.getTimeZone(wd.Service_Resource__r.RelatedRecord.TimeZoneSidKey);
                    //Timezone technicianTm = Timezone.getTimeZone(wd.Service_Resource__r.SAP_Timezone__r.Salesforce_timezone__c);
                    
                    if (entry.End_Date_Time__c != null)
                        entry.End_Time__c = entry.End_Date_Time__c.format('HH:mm', technicianTm.getID());

                    if (entry.Start_Date_Time__c != null)
                        entry.Start_Time__c = entry.Start_Date_Time__c.format('HH:mm', technicianTm.getID());

                    if (wd.WorkOrder.Timezone_for_Timecard__c == null || wd.WorkOrder.Timezone_for_Timecard__c == 'Customer') {
                        if ((wd.Type__c == 'Labor' && wd.Method__c == 'In Person') || wd.Type__c == 'Travel') {
                            String sftm = wd.WorkOrder.Account.Timezone__r.Salesforce_timezone__c;
                            //calculate offset
                            DateTime dtTest = System.now();
                            DateTime techDate = DateTime.valueof(dtTest.format('YYYY-MM-dd HH:mm:ss', technicianTm.getid()));
                            Decimal decMinutes = ((dtTest.getTime()) / 1000 / 60) - ((techDate.getTime()) / 1000 / 60);
                            //for logged in user is techncian do 
                            if (wd.WorkOrder.Account.Timezone__r.Salesforce_timezone__c != null) {
                                String customerTimeZone = sftm.substring(sftm.indexof('(') + 1, sftm.indexof(')'));
                                system.debug('customerTimeZone == ' + customerTimeZone);
                                if (entry.Start_Date_Time__c != null) {
                                    String sd = entry.Start_Date_Time__c.format('yyyy-MM-dd HH:mm:ss', customerTimeZone);
                                    entry.Start_Time__c = entry.Start_Date_Time__c.format('HH:mm', customerTimeZone);
                                    entry.Start_Date_Time__c = datetime.valueOf(sd);
                                    //entry.Start_Time__c = entry.Start_Date_Time__c.format('HH:mm');
                                }
                                System.debug('Shubham--ENtry Start time-->'+entry.Start_Date_Time__c);
                                System.debug('Shubham--ENtry-->'+entry);

                                if (entry.End_Date_Time__c != null) {
                                    String ed = entry.End_Date_Time__c.format('yyyy-MM-dd HH:mm:ss', customerTimeZone);
                                    entry.End_Time__c = entry.End_Date_Time__c.format('HH:mm', customerTimeZone);
                                    entry.End_Date_Time__c = datetime.valueOf(ed);
                                    //entry.End_Time__c = entry.End_Date_Time__c.format('HH:mm');
                                }
                                System.debug('Shubham--ENtry End time-->'+entry.End_Date_Time__c);
                                System.debug('Shubham--ENtry-->'+entry);
                            }

                            //adjustment if logged in user is not technician
                            if (UserInfo.getUserId() != wd.Service_Resource__r.RelatedRecordId) {
                                System.debug('Shubham -- User is not service resource');
                                if (entry.Start_Date_Time__c != null)
                                    entry.Start_Date_Time__c = entry.Start_Date_Time__c.addMinutes(Integer.valueof(decMinutes));
                                if (entry.End_Date_Time__c != null)
                                    entry.End_Date_Time__c = entry.End_Date_Time__c.addMinutes(Integer.valueof(decMinutes));
                            }
                        }
                    }
                }
            }
        }
    }

    public static void CreatNewTimesheetAsPerWithTimeEntries(List < SVMXC_Time_Entry__c > lstTimeEntry) {
        System.debug('Reached CreatNewTimesheetAsPerWithTimeEntries method');
        List < SVMXC_Timesheet__c > lstTimesheetToCreate = new List < SVMXC_Timesheet__c > ();
        Map < Id, Id > mapTEId_TechnID = new Map < Id, Id > ();
        Map < Id, Id > mapTechnId_Profile = new Map < Id, Id > ();
        Map < Id, Timecard_Profile__c > mapId_Profile = new Map < Id, Timecard_Profile__c > ();
        List < SVMXC_Time_Entry__c > lstTimeEntryToCretae = new List < SVMXC_Time_Entry__c > ();
        Map < SVMXC_Time_Entry__c, SVMXC_Timesheet__c > mapTE_TS = new Map < SVMXC_Time_Entry__c, SVMXC_Timesheet__c > ();

        for (SVMXC_Time_Entry__c entry: lstTimeEntry) {
            if (entry.Service_Resource__c != null) {
                mapTEId_TechnID.put(entry.Id, entry.Service_Resource__c);
            }
        }

        String timeZoneSidKey;
        if (mapTEId_TechnID.size() > 0) {
            List < ServiceResource > lstTechnician = [SELECT Id, Name, RelatedRecord.TimeZoneSidKey, Timecard_Profile__c FROM ServiceResource
                WHERE Id IN: mapTEId_TechnID.values()
            ];

            if (lstTechnician.size() > 0) {
                for (ServiceResource techn: lstTechnician) {
                    if (techn.Timecard_Profile__c != null) {
                        mapTechnId_Profile.put(techn.Id, techn.Timecard_Profile__c);
                        timeZoneSidKey = techn.RelatedRecord.TimeZoneSidKey;
                    }
                }
            }
        }

        if (mapTechnId_Profile.size() > 0) {
            List < Timecard_Profile__c > lstProfiles = [SELECT Id, First_Day_of_Week__c FROM Timecard_Profile__c
                WHERE Id IN: mapTechnId_Profile.values()
            ];

            if (lstProfiles.size() > 0) {
                for (Timecard_Profile__c profile: lstProfiles) {
                    mapId_Profile.put(profile.Id, profile);
                }
            }
        }

        Map<String, SVMXC_Timesheet__c> mapTechIdTimesheet = new Map<String, SVMXC_Timesheet__c>();
        Map<String, SVMXC_Timesheet__c> UniqueIdVsTS = new Map<String, SVMXC_Timesheet__c>();
        Map<Id, ServiceResource> serviceResourceMap = new Map<Id, ServiceResource>([Select Id,  Work_Center__c From ServiceResource Where Id in: mapTEId_TechnID.values()]);
        for (SVMXC_Time_Entry__c entry: lstTimeEntry) {
            String workCenter;
          
            if (entry.Service_Resource__c != null) {
               
                Date SourceDate;
               
                if(timeZoneSidKey != null) {
                    SourceDate = DateTime.valueof(entry.Start_Date_Time__c.format('yyyy-MM-dd hh:mm:ss',timeZoneSidKey)).Date();
                }
                if (serviceResourceMap.containskey(entry.Service_Resource__c) && serviceResourceMap.get(entry.Service_Resource__c).Work_Center__c != null) {
                    workCenter = serviceResourceMap.get(entry.Service_Resource__c).Work_Center__c;
                }
                Date startDate = GetStartOfTheWeekAsPerProfile(SourceDate, mapId_Profile.get(mapTechnId_Profile.get(entry.Service_Resource__c)));
                String uniqueIdentifier = entry.Service_Resource__c + workCenter + String.ValueOf(startDate);
               
                

                if (mapTechIdTimesheet.containsKey(uniqueIdentifier)) {
                    SVMXC_Timesheet__c TS = mapTechIdTimesheet.get(uniqueIdentifier);
                    mapTE_TS.put(entry, TS);
                } else {
                    SVMXC_Timesheet__c TS = new SVMXC_Timesheet__c();
                    TS.Timecard_Profile__c = (mapTechnId_Profile.containskey(entry.Service_Resource__c))?mapTechnId_Profile.get(entry.Service_Resource__c):null; // STRY0205707 - Store Timecardprofile Id into Timesheet
                    TS.Start_Date__c = startDate;
                    TS.Service_Resource__c = entry.Service_Resource__c;
                    if (mapOfTechProfile.get(TS.Service_Resource__c) != null && mapOfTechProfile.get(TS.Service_Resource__c).RelatedRecordId != null) {
                        TS.ownerid = mapOfTechProfile.get(TS.Service_Resource__c).RelatedRecordId;
                    }
                    TS.TimeSheet_Unique__c = uniqueIdentifier;
                    System.debug('---TS--'+TS);
                    if (TS.Start_Date__c != null)
                        TS.End_Date__c = TS.Start_Date__c + 6;

                    /*if (Time_Entry_Matrix_Controller.fromTEM)
                        TS.Status__c = 'Approved';
                    else*/
                    TS.Status__c = 'Incomplete';

                    UniqueIdVsTS.put(uniqueIdentifier, TS);
                    mapTechIdTimesheet.put(uniqueIdentifier, TS);
                    mapTE_TS.put(entry, TS);
                }
            }
        }
        
        Map<String, SVMXC_Timesheet__c> duplicateUniqueIdMap = getDuplicateUniqueMap(UniqueIdVsTS.keySet());
        Set<String> uniqueIdList = UniqueIdVsTS.keySet();
        List<SVMXC_Timesheet__c> newTsInserted = new List<SVMXC_Timesheet__c>();
       
        //0Hnc0000000061wCAA H33078  2019-08-10
        //0Hnc0000000061wCAA null2019-08-10
        
        
        for(String uniqueId: duplicateUniqueIdMap.keySet()) {
           
            if(!uniqueIdList.contains(uniqueId)) {
                newTsInserted.add(UniqueIdVsTS.get(uniqueId));
            } else {
                
                //SVMXC_Timesheet__c existingTs = duplicateUniqueIdMap.get(uniqueId);
                for (SVMXC_Time_Entry__c entry: mapTE_TS.KeySet()) {
                    SVMXC_Timesheet__c TS = mapTE_TS.get(entry);
                    
                    if (TS != null && duplicateUniqueIdMap.containsKey(TS.TimeSheet_Unique__c)) {
                        mapTE_TS.put(entry, duplicateUniqueIdMap.get(TS.TimeSheet_Unique__c));
                    }
                }
            }
        }

        Set<Id> woliIdSet = new Set<Id>();
        for(SVMXC_Time_Entry__c entry: mapTE_TS.KeySet()) {
            if(entry.WorkOrderLineItem__c != null)
                woliIdSet.add(entry.WorkOrderLineItem__c);
        }
        Map<Id, WorkOrderLineItem> woliRecordsMap = getWoliForTimeEntries(woliIdSet); // Passing Time Entries

       
        if (mapTE_TS.size() > 0) {
           
                if(duplicateUniqueIdMap.size() > 0) {
                    insert newTsInserted;
                } else {
                    insert mapTechIdTimesheet.values();
                }
   
                for (SVMXC_Time_Entry__c entry: mapTE_TS.KeySet()) {
                    SVMXC_Timesheet__c TS = mapTE_TS.get(entry);
                    
                    if(woliRecordsMap.containsKey(entry.WorkOrderLineItem__c)) {
                        WorkOrderLineItem woliRec = woliRecordsMap.get(entry.WorkOrderLineItem__c);
                        entry.Status__c = 'Incomplete';
                        // Populate Work Center
                        entry.WorkCenter__c = woliRec.Service_Resource__r.Work_Center__c;

                        // Populate Work Order Number
                        entry.WorkOrderNumber__c = woliRec.WorkOrder.WorkOrderNumber;

                        // Populate Case Number
                        entry.CaseNumber__c = woliRec.WorkOrder.Case.CaseNumber;

                        // Populate Asset City
                        entry.AssetCity__c = woliRec.Asset.Location.City__c;

                        // Populate Product Code Serial Number
                        entry.ProductCodeSerialNumber__c = woliRec.Asset.Name;

                        // Populate Country. Commented to update it from time entry trigger using service resource country
                        //entry.Country__c = woliRec.Asset.Location.Country__c;
                    }

                    System.debug('duplicateUniqueIdMap===='+duplicateUniqueIdMap);
                    System.debug('Check TS===='+TS);
                    if (TS != null && !duplicateUniqueIdMap.containskey(TS.TimeSheet_Unique__c)) {
                        entry.Timesheet__c = TS.Id;
                        lstTimeEntryToCretae.add(entry);
                        System.debug(entry.WorkOrderLineItem__c);
                    } else if(duplicateUniqueIdMap.containskey(TS.TimeSheet_Unique__c)) {
                        entry.Timesheet__c = duplicateUniqueIdMap.get(TS.TimeSheet_Unique__c).Id;
                        lstTimeEntryToCretae.add(entry);
                    }
                }
                System.debug('lstTimeEntryToCretae===========' + lstTimeEntryToCretae);
                insert(lstTimeEntryToCretae);
           
        }
    }

    public static Date getStartOfTheWeekAsPerProfile(Date selectedDate, Timecard_Profile__c timecardProfile) {

        DateTime selectedDateTime = Datetime.newInstance(selectedDate.year(), selectedDate.month(), selectedDate.day());
        String selectedDay = selectedDateTime.format('EEEE');

        Date startOfWeek = selectedDate.toStartofWeek();
        DateTime startOfWeekDateTime = Datetime.newInstance(startOfWeek.year(), startOfWeek.month(), startOfWeek.day());
        String startOfWeekDay = startOfWeekDateTime.format('EEEE');
        
        if(timecardProfile.First_Day_of_Week__c == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_SATURDAY) {
            Map<String, Integer> dayStrIndexMap = new Map<String, Integer>{'Saturday' => 0, 'Sunday' => 1, 'Monday' => 2, 'Tuesday' => 3, 'Wednesday' => 4, 'Thursday' => 5, 'Friday' => 6};
            /*if(selectedDay != System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_SATURDAY) {
                if(startOfWeekDay == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_MONDAY){
                    selectedDate = startOfWeek - 2;
                }
                else {
                    selectedDate = startOfWeek - 1;
                }
            }*/
            Integer daysToSubtract = dayStrIndexMap.get(selectedDay);
            selectedDate = selectedDate - daysToSubtract;
            System.debug('retDate===SATURDAY=='+selectedDate+'--selectedDay--'+selectedDay+'--startOfWeekDay--'+startOfWeekDay);
            return selectedDate;
        }
        
        if(timecardProfile.First_Day_of_Week__c == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_SUNDAY) {
            Map<String, Integer> dayStrIndexMap = new Map<String, Integer>{'Sunday' => 0, 'Monday' => 1, 'Tuesday' => 2, 'Wednesday' => 3, 'Thursday' => 4, 'Friday' => 5, 'Saturday' => 6};
            /*if(selectedDay != System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_SUNDAY) {
                
                if(startOfWeekDay == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_MONDAY){
                    selectedDate = startOfWeek - 1;
                }
            }*/
            Integer daysToSubtract = dayStrIndexMap.get(selectedDay);
            selectedDate = selectedDate - daysToSubtract;
            System.debug('retDate===SUNDAY=='+selectedDate+'--selectedDay--'+selectedDay+'--startOfWeekDay--'+startOfWeekDay);
            return selectedDate;
        }

        if(timecardProfile.First_Day_of_Week__c == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_MONDAY) {
            Map<String, Integer> dayStrIndexMap = new Map<String, Integer>{'Monday' => 0, 'Tuesday' => 1, 'Wednesday' => 2, 'Thursday' => 3, 'Friday' => 4, 'Saturday' => 5, 'Sunday' => 6};
            /*if(selectedDay != System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_MONDAY) {
                
                if(startOfWeekDay == System.Label.TIME_CARD_FIRST_DAY_OF_WEEK_SUNDAY){
                    selectedDate = startOfWeek + 1;
                }
            }*/
            Integer daysToSubtract = dayStrIndexMap.get(selectedDay);
            selectedDate = selectedDate - daysToSubtract;
            System.debug('retDate===MONDAY=='+selectedDate+'--selectedDay--'+selectedDay+'--startOfWeekDay--'+startOfWeekDay);
            return selectedDate;
        }
        return selectedDate;    
    }

   
    public static list < SVMXC_Time_Entry__c > splitMidNightEntries(List < SVMXC_Time_Entry__c > timeEntryList) {
        list < SVMXC_Time_Entry__c > newTESplitList = new list < SVMXC_Time_Entry__c > ();
        System.debug('Shubham--timeEntryList-->'+timeEntryList);
        
        for (SVMXC_Time_Entry__c entry: timeEntryList) {
            //System.debug('Shubham--entry.Start_Date_Time__c.date()-->'+entry.Start_Date_Time__c.date());
        	//System.debug('Shubham--entry.End_Date_Time__c.date()-->'+entry.End_Date_Time__c.date());
            System.debug('Shubham--entry.Start_Date_Time__c-->'+entry.Start_Date_Time__c);
            System.debug('Shubham--entry.End_Date_Time__c-->'+entry.End_Date_Time__c);
            if (!entry.Start_Date_Time__c.date().isSameDay(entry.End_Date_Time__c.date())) {
            Integer startDate = entry.Start_Date_Time__c.day();
            Integer endDate = entry.End_Date_Time__c.day();
            System.debug('Shubham--Integer.valueOf entry.Start_Date_Time__c-->'+Integer.valueOf(String.valueOf(entry.Start_Date_Time__c).substring(8,10)));
            System.debug('Shubham-- Integer.valueOf entry.End_Date_Time__c-->'+Integer.valueOf(String.valueOf(entry.End_Date_Time__c).substring(8,10)));
            Boolean isSame = Integer.valueOf(String.valueOf(entry.Start_Date_Time__c).substring(8,10))==Integer.valueOf(String.valueOf(entry.End_Date_Time__c).substring(8,10))?true:false;
            System.debug('Shubham--isSame-->'+isSame);
                //if(startDate)
            Boolean isSameDay = entry.Start_Date_Time__c.day()==entry.End_Date_Time__c.day()?true:false;
            System.debug('Shubham - isSameDay-->'+isSameDay);
            //if (!entry.Start_Date_Time__c.isSameDay(entry.End_Date_Time__c)) {
                SVMXC_Time_Entry__c splitEntry = entry.clone(false, false, false, false);
                splitEntry.Start_Date_Time__c = dateTime.newInstance(entry.Start_Date_Time__c.Year(), entry.Start_Date_Time__c.Month(),
                    (entry.Start_Date_Time__c.Day() + 1), 00, 00, 00);
                splitEntry.End_Date_Time__c = entry.End_Date_Time__c;
                entry.End_Date_Time__c = dateTime.newInstance(entry.Start_Date_Time__c.Year(), entry.Start_Date_Time__c.Month(),
                    entry.Start_Date_Time__c.Day(), 23, 59, 59);
                    
                splitEntry.Start_Time__c = '00:00';
                entry.End_Time__c = '23:59';
                newTESplitList.add(splitEntry);
            }
        }
        system.debug('newTESplitList===========' + newTESplitList);
        return newTESplitList;
    }

    public static Map<Id, WorkOrderLineItem> getWoliForTimeEntries(Set<Id> woliIdSet) {
        Map<Id, WorkOrderLineItem> woliMap = new Map<Id, WorkOrderLineItem>([Select Id, Service_Resource__r.Work_Center__c, WorkOrder.WorkOrderNumber, 
                                                                                WorkOrder.Case.CaseNumber, Asset.Location.City__c, Asset.Name, Asset.Location.Country__c 
                                                                                From WorkOrderLineItem Where Id in: woliIdSet]);
        return woliMap;
    }

    public static void updateTimesheetFieldsBefore(List<SVMXC_Timesheet__c> newTimesheets, Map<Id, SVMXC_Timesheet__c> oldMap){
        Set<Id> serviceResourceIds = new Set<Id>();
        Set<Id> timeSheetIdSet = new set<Id>();
        List<SVMXC_Timesheet__c> preWeekTSList;
        Map<Id,SVMXC_Timesheet__c> preWeekTsMap = new Map<Id,SVMXC_Timesheet__c>();
        Map<Id, ServiceResource> serviceResources;
        Map<Id, Id> techOrganizationalCalendarMap = new Map<Id, Id>();
        Map<String, SVMXC_Timesheet__c> previousTimesheetDateMap = new Map<String, SVMXC_Timesheet__c>();
        Set<Date> previousTimesheetDates = new Set<Date>();

        for(SVMXC_Timesheet__c TS : newTimesheets){
            serviceResourceIds.add(TS.Service_Resource__c);
            if(TS.Previous_Timesheet__c != null ){
                timeSheetIdSet.add(TS.Previous_Timesheet__c);
            }
            if(TS.Start_Date__c != null && TS.Previous_Timesheet__c == null){
                Date dtPrevDate = TS.Start_Date__c - 7;
                previousTimesheetDates.add(dtPrevDate);
            } 
        }
        if(!timeSheetIdSet.isEmpty() || !previousTimesheetDates.isEmpty()){
            for(SVMXC_Timesheet__c ts :[
                SELECT Id, Time_Bank_Balance__c, Status__c, Service_Resource__c, Start_Date__c FROM SVMXC_Timesheet__c WHERE Id IN: timeSheetIdSet OR (Start_Date__c IN:previousTimesheetDates AND Service_Resource__c IN:serviceResourceIds)
            ]){
                if(timeSheetIdSet.contains(ts.Id)){
                    preWeekTsMap.put(ts.Id,ts);
                }
                
                if(previousTimesheetDates.contains(ts.Start_Date__c) && serviceResourceIds.contains(ts.Service_Resource__c)){
                    Date dtPrevDate = ts.Start_Date__c;
                    String strConcVal = String.ValueOf(ts.Service_Resource__c);
                    strConcVal = strConcVal + '-' + String.ValueOf(dtPrevDate.Month());
                    strConcVal = strConcVal + '-' + String.ValueOf(dtPrevDate.Day());
                    strConcVal = strConcVal + '-' + String.ValueOf(dtPrevDate.Year());
                    previousTimesheetDateMap.put(strConcVal, ts);
                }
            }
        }
        System.debug('---previousTimesheetDateMap--'+previousTimesheetDateMap);
        serviceResources = getServiceResources(serviceResourceIds);
        
        Double previousTimeBalance = 0.0;
        for(SVMXC_Timesheet__c ts :newTimesheets){
            if(ts.Service_Resource__c != null ){
                if(serviceResources != null && serviceResources.containsKey(ts.Service_Resource__c)){
                    ts.Organizations__c = serviceResources.get(ts.Service_Resource__c).Timecard_Profile__c;
                    ts.Service_Territory__c = serviceResources.get(ts.Service_Resource__c).Service_Territory__c;
                    techOrganizationalCalendarMap.put(ts.Service_Resource__c,  serviceResources.get(ts.Service_Resource__c).Organizational_Calendar__c);
                }
            }
        }
        updateTimesheetFields(newTimesheets, techOrganizationalCalendarMap, serviceResources, preWeekTsMap, previousTimesheetDateMap);
        
    }

    private static void updateTimesheetFields(List<SVMXC_Timesheet__c> newTimesheets, Map<Id, Id> techOrganizationalCalendarMap, Map<Id, ServiceResource> serviceResources, 
    Map<Id, SVMXC_Timesheet__c> previousWeekTimesheet, Map<String, SVMXC_Timesheet__c> previousTimesheetDateMap){
        
        // STRY0205707 - Start - Get Timesheet --> Timecard Profile
        Set<Id> TimecardProfileIDSet = new Set<Id>();
        for(SVMXC_Timesheet__c ts: newTimesheets) {
            if(ts.Timecard_Profile__c != null) {
                TimecardProfileIDSet.add(ts.Timecard_Profile__c);
            }            
        }
        Map<Id, Timecard_Profile__c> timecardMap = new Map<Id, Timecard_Profile__c>([Select Id, Max_Hours_per_Week__c, Max_Hours_Day_4__c 
                                                                                     From Timecard_Profile__c Where Id in: TimecardProfileIDSet and Forward_Overtime_to_Bank__c = true]);
        // STRY0205707 - End - Get Timesheet --> Timecard Profile
        
        
        Map<String, Organizational_Holiday__c> orgHolidayMap = new Map<String, Organizational_Holiday__c>();
        list<Organizational_Holiday__c> orgHolidaysList = new   list<Organizational_Holiday__c>();  
        orgHolidaysList = [SELECT Id, Organizational_Calendar__c, Holiday_Date__c 
                           FROM Organizational_Holiday__c
                           WHERE Organizational_Calendar__c IN:techOrganizationalCalendarMap.values()];       
        for(SVMXC_Timesheet__c TS : newTimesheets){            
            ServiceResource technician = serviceResources.get(TS.Service_Resource__c);  
            
            // STRY0205707 - Start - Match TS's Timecard Profile and Technician Timecard Profile
            Timecard_Profile__c timecard;
            if(ts.Timecard_Profile__c != null && timecardMap.containskey(ts.Timecard_Profile__c)) 
                timecard = timecardMap.get(ts.Timecard_Profile__c);
      else
                timecard = technician.Timecard_Profile__r;
            // STRY0205707 - End - Match TS's Timecard Profile and Technician Timecard Profile
            
            System.debug('---technician--'+technician);
            System.debug('---techOrganizationalCalendarMap--'+techOrganizationalCalendarMap);
            if(techOrganizationalCalendarMap != null && TS.Service_Resource__c!=null && techOrganizationalCalendarMap.containsKey(TS.Service_Resource__c)){
               if(!orgHolidaysList.isEmpty()){
                     Decimal num = timecard.Max_Hours_per_Week__c!=null?Decimal.valueOf(timecard.Max_Hours_per_Week__c):0.0;
                     Decimal num1 = 0.0;
                     for(Organizational_Holiday__c oh : orgHolidaysList){
                     if(oh!=null && (oh.Holiday_Date__c >= TS.Start_Date__c && oh.Holiday_Date__c <= TS.End_Date__c) && oh.Organizational_Calendar__c == techOrganizationalCalendarMap.get(TS.Service_Resource__c)){
                       System.debug('---oh--'+oh);
                       num1 += timecard.Max_Hours_Day_4__c!=null?Decimal.valueOf(timecard.Max_Hours_Day_4__c):0.0;
                     }
                 }
                   TS.Total_Time_W_O_Holiday__c = num - num1;
                }
               if(timecard.Max_Hours_per_Week__c != null && TS.Total_Time_W_O_Holiday__c != null){
                    TS.Total_Holiday_Hours__c = Decimal.valueOf(timecard.Max_Hours_per_Week__c) - TS.Total_Time_W_O_Holiday__c;
                } 
            }
            ts.Total_Time_Worked__c = ts.Roll_Up_Direct_Time__c + ts.Roll_Up_InDirect_Time_Exc_PPL_Holi__c;
            if(ts.Total_Recuperative_Time__c == null){
                ts.Total_Recuperative_Time__c = 0.0;
            }
            if(ts.Time_Bank_Adjust_Hours__c == null){
                ts.Time_Bank_Adjust_Hours__c = 0.0;
            }

            Decimal max_hrs_per_week = timecard.Max_Hours_per_Week__c!=null?Decimal.valueOf(timecard.Max_Hours_per_Week__c):0.0;
            if(ts.Overtime_hours__c == null) {
                ts.Overtime_hours__c = 0;
            } else if(ts.Total_Time_Worked__c > max_hrs_per_week) {  
                ts.Overtime_hours__c = (ts.Total_Time_Worked__c!=null?ts.Total_Time_Worked__c:0.0) - max_hrs_per_week;
            }else{
                 ts.Overtime_hours__c = 0;
            }

            if(ts.Previous_Timesheet__c == null && TS.Service_Resource__c!=null){
                Date dtPrevDate = ts.Start_Date__c - 7;
                String strConcVal = String.ValueOf(TS.Service_Resource__c)+ '-' + String.ValueOf(dtPrevDate.Month())+ '-' + String.ValueOf(dtPrevDate.Day())+ '-' + String.ValueOf(dtPrevDate.Year());
                if(previousTimesheetDateMap.containsKey(strConcVal)){
                    SVMXC_Timesheet__c timesheet = previousTimesheetDateMap.get(strConcVal);
                    ts.Previous_Timesheet__c = timesheet.Id;
                }
            }
            if(TS.Service_Resource__c!=null){
                ts.TimeSheet_Unique__c = TS.Service_Resource__c + technician.Work_Center__c + String.ValueOf(TS.Start_Date__c);
            }

            if(ts.Previous_Timesheet__c != null 
            && previousWeekTimesheet.containsKey(ts.Previous_Timesheet__c)
            && previousWeekTimesheet.get(ts.Previous_Timesheet__c).Time_Bank_Balance__c != null
            && previousWeekTimesheet.get(ts.Previous_Timesheet__c).Status__c == 'Approved'){
                ts.Time_Bank_Balance__c = previousWeekTimesheet.get(ts.Previous_Timesheet__c).Time_Bank_Balance__c - ts.Total_Recuperative_Time__c - ts.Time_Bank_Adjust_Hours__c + ts.Overtime_hours__c;
            }else{
                ts.Time_Bank_Balance__c = 0.0  - ts.Total_Recuperative_Time__c - ts.Time_Bank_Adjust_Hours__c + ts.Overtime_hours__c;
            }
            
        }
    }

    public static void updateRelatedObjects(Map<Id,SVMXC_Timesheet__c> newTimesheets, Map<Id, SVMXC_Timesheet__c> oldTimesheets){
        Set<Id> submittedTimesheetIds = new Set<Id>();
        Set<Id> rejectedTimesheets = new Set<Id>();
        Set<Id> approvedTimesheets = new Set<Id>();
        Set<Id> openTimesheets = new Set<Id>();

        for(SVMXC_Timesheet__c timesheet : newTimesheets.values()){

            if(oldTimesheets!=null && timesheet.Status__c =='Submitted' && oldTimesheets.get(timesheet.Id).Status__c != 'Submitted'){
                submittedTimesheetIds.add(timesheet.id);
            }
            if(oldTimesheets!=null && timesheet.Status__c =='Incomplete' && oldTimesheets.get(timesheet.Id).Status__c == 'Submitted'){
                rejectedTimesheets.add(timesheet.id);
            }
            if(oldTimesheets!=null && timesheet.Status__c == 'Approved' && timesheet.Status__c != oldTimesheets.get(timesheet.id).Status__c){
                approvedTimeSheets.add(timesheet.Id);
            }
            if(oldTimesheets!=null && timesheet.Status__c == 'Open' && timesheet.Status__c != oldTimesheets.get(timesheet.id).Status__c){
                openTimesheets.add(timesheet.Id);
            }
        }
        if(!rejectedTimesheets.isEmpty() || !submittedTimesheetIds.isEmpty() || !approvedTimeSheets.isEmpty() || !openTimesheets.isEmpty()){
            Map<Id, SVMXC_Time_Entry__c> timeEntries = new Map<Id, SVMXC_Time_Entry__c>([
                SELECT Id, RecordTypeId, Interface_Status__c, Service_Resource__r.Service_Territory__r.Territory_Type__c,WorkOrderLineItem__r.SOI_Event__c, 
                Start_Date_Time__c, WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName, Status__c, Timesheet__r.Is_TEM__c,
                Timesheet__r.Status__c, WorkOrderLineItem__c, WorkOrderLineItem__r.WorkOrderId, Next_Eighth_Day__c, RecordType.DeveloperName,
                Next_Eighth_Day__r.Start_Date_Time__c, WorkOrderLineItem__r.Status, WorkOrderLineItem__r.RecordType.DeveloperName
                FROM SVMXC_Time_Entry__c
                WHERE Timesheet__c IN:rejectedTimesheets OR Timesheet__c IN:submittedTimesheetIds OR Timesheet__c IN:approvedTimeSheets
                OR Timesheet__c IN:openTimesheets
            ]);
            updateTimeEntryWorkDetailWorkOrder(newTimesheets.values()[0], submittedTimesheetIds, rejectedTimesheets, approvedTimesheets, openTimesheets, timeEntries);
        }
    }

    private static void updateTimeEntryWorkDetailWorkOrder(SVMXC_Timesheet__c timesheet, Set<Id> submittedTimeSheetIds, Set<Id> rejectedTimesheetIds, 
    Set<Id> approvedTimesheets, Set<Id> openTimesheets, Map<Id, SVMXC_Time_Entry__c> timeEntries){
        
        List<SVMXC_Time_Entry__c> timeEntriesToUpdate = new List<SVMXC_Time_Entry__c>();
        Set<Id> eigthDayId = new Set<Id>();
        Set<Id> technicianIds = new Set<Id>();
        List<Date> eighthDayDates = new List<Date>();
        Map<Id, WorkOrder> workOrders = new Map<Id, WorkOrder>();
        List<SVMXC_Time_Entry__c> timeEntriesToDelete = new List<SVMXC_Time_Entry__c>();
        Map<Id, WorkOrderLineItem> workOrderLineItems = new Map<Id, WorkOrderLineItem>();
        Map<Id, WorkOrderLineItem> workOrderLineItemsToDelete = new Map<Id, WorkOrderLineItem>();
        set<id> WoliIdSet = new set <ID>();
    
        
        // Logic to update soie Timecard approval date and completion date
        if(approvedTimesheets != null){
            for(SVMXC_Time_Entry__c te:timeEntries.values()){
                if(te.Timesheet__r.Status__c == 'Approved'){
                    WoliIdSet.add(te.WorkOrderLineItem__c); 
                }
               
            }
            List<WorkOrderLineItem> woliList = [ SELECT Id,  Training_Completed__c, SOI_Event__c, WorkOrderId, RecordType.DeveloperName, 
                                                Sales_Order_Item__c,EndDate, WorkOrder.WorkOrderNumber
                                                FROM WorkOrderLineItem 
                                                WHERE Id = : WoliIdSet  AND  RecordType.DeveloperName = 'TE_Usage'
                                               ];
            system.debug('woliList' + woliList);
            if(woliList.size()>0){
                MAP<Id,WorkOrderLineItem> wolisoieMap = new Map <Id,WorkOrderLineItem>();
                set<Id> soieIdSet = new set <Id>();
                List<SOI_Event__c> soieUpdList = new List<SOI_Event__c>();
                for(WorkOrderLineItem Woli : woliList ){
                    if(Woli.SOI_Event__c != null ){
                        wolisoieMap.put(Woli.SOI_Event__c,Woli);
                        soieIdSet.add(Woli.SOI_Event__c);   
                    }
                }
                System.debug('wolisoieMap++'+ wolisoieMap);
                System.debug('soieIdSet++'+ soieIdSet);
                
                for(Id soieId : soieIdSet){
                    SOI_Event__c soie = new SOI_Event__c();
                    soie.Id = soieId;
                    soie.Timecard_Approval_Date__c = system.today();
                    if(wolisoieMap != null && wolisoieMap.containsKey(soieId) && wolisoieMap.get(soieId).Training_Completed__c == True){
                        DateTime dT = wolisoieMap.get(soieId).EndDate;
                        Date myDate = date.newinstance(dT.year(), dT.month(), dT.day()); 
                        //soie.CompletionDate__c = myDate;
                        //soie.Status__c = 'Completed';
                    }
                    soieUpdList.add(soie);
                }
                system.debug('soieUpdList==' + soieUpdList);
                if(soieUpdList.size()>0){
                    update soieUpdList;
                }
            }
        }
        // logic ends here
        
        for(SVMXC_Time_Entry__c te:timeEntries.values()){
            Boolean updateEntry = false;
            if(submittedTimeSheetIds.contains(te.Timesheet__c)){
                if(submittedTimeSheetIds.contains(te.Timesheet__c)){
                    te.Status__c = 'Submitted';
                    updateEntry = true;
                }
                if (te.Next_Eighth_Day__c != null) {
                    eigthDayId.add(te.Next_Eighth_Day__c);
                    technicianIds.add(te.Service_Resource__c);
                    eighthDayDates.add(te.Next_Eighth_Day__r.Start_Date_Time__c.date());
                }
                if(te.WorkOrderLineItem__c!=null && te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Training'){
                    workOrderLineItems.put(te.WorkOrderLineItem__c,new WorkOrderLineItem(Id = te.WorkOrderLineItem__c ,Status='Submitted'));
                }
                System.debug('---te.Service_Resource__r.Service_Territory__r.Territory_Type__c--'+te.Service_Resource__r.Service_Territory__r.Territory_Type__c);
                System.debug('---te.START DATE--'+te.Start_Date_Time__c+'--REC TYPE--'+te.RecordType.DeveloperName);
                System.debug('---WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName--'+te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName);
                
                if(te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Field_Service' 
                || te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Installation' 
                || te.Service_Resource__r.Service_Territory__r.Territory_Type__c == 'Field Service' 
                || te.Service_Resource__r.Service_Territory__r.Territory_Type__c.contains('Installation')){
                    te.Interface_Status__c = 'Process';
                    updateEntry = true;
                }
            }
            if(rejectedTimesheetIds.contains(te.Timesheet__c)){

                if(te.WorkOrderLineItem__c !=null && (te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Project_Management' 
                || te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Training')
                && te.RecordType.DeveloperName == 'Direct_Hours' && te.WorkOrderLineItem__r.RecordType.DeveloperName != 'Header'){
                    workOrders.put(te.WorkOrderLineItem__r.WorkOrderId, new WorkOrder(
                        Id = te.WorkOrderLineItem__r.WorkOrderId, Status = 'Assigned', Interface_STatus__c = null,SAP_Status__c = null
                    ));
                }

                if(te.Service_Resource__r.Service_Territory__r.Territory_Type__c != null && te.WorkOrderLineItem__c != null 
                && te.WorkOrderLineItem__r.Status == 'Submitted' && te.WorkOrderLineItem__r.RecordType.DeveloperName != 'Header' 
                && te.Service_Resource__r.Service_Territory__r.Territory_Type__c.contains('Training')){
                    workOrderLineItems.put(te.WorkOrderLineItem__c,new WorkOrderLineItem(Id = te.WorkOrderLineItem__c ,Status='Open'));
                }
                if(te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName != 'Project_Management'){
                    te.Status__c = 'Incomplete';
                    te.Bypass_Validation__c = true;
                    updateEntry = true;
                }
                if(te.Timesheet__r.Is_TEM__c && !updateEntry){
                    timeEntriesToDelete.add(te);
                    if(te.WorkOrderLineItem__c!=null){
                        workOrderLineItemsToDelete.put(te.WorkOrderLineItem__c, new WorkOrderLineItem(Id=te.WorkOrderLineItem__c));
                    }
                }
            }
            if(approvedTimesheets.contains(te.Timesheet__c)){
                te.Status__c = 'Approved';
                te.Bypass_Validation__c = true;
                te.Interface_Status__c = 'Process';
                updateEntry = true;
                //Should only be setting T&E and PM to Approved.  Not FS/Install
                if(te.WorkOrderLineItem__c!=null && (te.WorkOrderLineItem__r.WorkOrder.RecordTypeId == teWorkOrderRecordType /*|| 
                                                     te.WorkOrderLineItem__r.WorkOrder.RecordTypeId == pmWorkOrderRecordType*/)){
                    workOrders.put(te.WorkOrderLineItem__r.WorkOrderId, new WorkOrder(
                        Id = te.WorkOrderLineItem__r.WorkOrderId, Status = 'Approved', Interface_STatus__c = 'Process',SAP_Status__c = null
                    )); //Chandra : modified the Interface_Status__C from null to "Process", when WO is set to Approved, ideally the interface status should be process.
                    //workOrderLineItems.put(te.WorkOrderLineItem__c,new WorkOrderLineItem(Id = te.WorkOrderLineItem__c ,Status='Closed'));
                }
    
            }

            if(openTimesheets.contains(te.Timesheet__c)){
                workOrderLineItems.put(te.WorkOrderLineItem__c,new WorkOrderLineItem(Id = te.WorkOrderLineItem__c ,Status='Open'));
            }
            if(updateEntry){
                timeEntriesToUpdate.add(te);
            }
            
        }

        if(!eigthDayId.isEmpty()){
            for (SVMXC_Time_Entry__c te : [
                SELECT Id,RecordTypeId, Interface_Status__c, Service_Resource__r.Service_Territory__r.Territory_Type__c, Start_Date_Time__c, 
                WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName, Status__c, Timesheet__r.Status__c, WorkOrderLineItem__c, 
                WorkOrderLineItem__r.WorkOrderId 
                FROM SVMXC_Time_Entry__c 
                WHERE (Id in :eigthDayId OR (DAY_ONLY(Start_Date_Time__c) IN :eighthDayDates AND Technician__c IN :technicianIds)) 
                AND ID != :timeEntriesToUpdate
            ]) {
                te.Status__c = 'Submitted';

                if(te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Field_Service' 
                || te.WorkOrderLineItem__r.WorkOrder.RecordType.DeveloperName == 'Installation'  
                || te.Service_Resource__r.Service_Territory__r.Territory_Type__c == 'Field Service'  
                || te.Service_Resource__r.Service_Territory__r.Territory_Type__c.contains('Installation')){
                    te.Interface_Status__c = 'Process';
                } 
                timeEntriesToUpdate.add(te);               
            }
        }
        
        if(timeEntriesToUpdate.size() > 0) update timeEntriesToUpdate;
        if(!workOrders.isEmpty()) update workOrders.values();
        if(!workOrderLineItems.isEmpty()) update workOrderLineItems.values();
        if(!timeEntriesToDelete.isEmpty()) delete timeEntriesToDelete;
        updateTimeEntryMatrix(workOrderLineItemsToDelete.keyset(), timesheet);
        if(!workOrderLineItemsToDelete.isEmpty()) delete workOrderLineItemsToDelete.values();
    }

    private static void updateTimeEntryMatrix(Set<Id> workOrderLineItemIds, SVMXC_Timesheet__c timesheet){
        List<Time_Entry_Matrix__c> teMatrixList = [
            SELECT Id, Status__c, RecordType.DeveloperName From Time_Entry_Matrix__c
            Where (Day_1_Work_Detail__c IN: workOrderLineItemIds OR Day_2_Work_Detail__c IN: workOrderLineItemIds
            OR Day_3_Work_Detail__c IN: workOrderLineItemIds OR Day_4_Work_Detail__c IN: workOrderLineItemIds
            OR Day_5_Work_Detail__c IN: workOrderLineItemIds OR Day_6_Work_Detail__c IN: workOrderLineItemIds
            OR Day_7_Work_Detail__c IN: workOrderLineItemIds)
            OR (Period_Start_Date__c =: timesheet.Start_Date__c AND Period_End_Date__c =: timesheet.End_Date__c 
            AND RecordType.DeveloperName = 'IND')
        ];
        for(Time_Entry_Matrix__c tem : teMatrixList){
            tem.Status__c = 'Open';
        }
        
        if(!teMatrixList.isEmpty()) update teMatrixList;
    }

    /**
        Create time entries for holiday and lunch after timesheet is created
     */
    public static void CheckProfileAndCreateDefaultLunchBreaksOnTimeSheetCreation(List<SVMXC_Timesheet__c> lstTimeSheets) {
        List<SVMXC_Time_Entry__c> lstTimeEntryToInsert = new List<SVMXC_Time_Entry__c>();
        Set<Id> setTimeCardProfileId = new Set<Id>();
        Set<Id> setOrgCalanderId = new Set<Id>();
        Id indirectHoursTE = VFSL_Utility.getRecordTypeMap().get('SVMXC_Time_Entry__c').get('Indirect_hours');
        Map<Id, ServiceResource> serviceResources = new Map<Id, ServiceResource>();
        Set<Date> setHolidays = new Set<Date>();
        Map<Id,Id> lunchActivities = new Map<Id, Id>();
        Map<Id,Id> holidayActivities = new Map<Id, Id>();
        Set<Id> timesheetsForLunchTimeEntries = new Set<Id>();
        Set<Id> timesheetsForHolidayTimeEntries = new Set<Id>();
        Map<Id, Set<Date>> mapOrgCal_OrgHolidays = new Map<Id, Set<Date>>();

        /*for(SVMXC_Timesheet__c TS : lstTimeSheets) {
            if(TS.Service_resource__c != null){
                serviceResources.put(TS.Service_resource__c, null);
            }
        }*/
        
        // STRY0205707 - Start - Get Timesheet --> Timecard Profile
        Set<Id> TimecardProfileIDSet = new Set<Id>();
        for(SVMXC_Timesheet__c TS : lstTimeSheets) {
            if(TS.Service_resource__c != null){
                serviceResources.put(TS.Service_resource__c, null);
            }
            // STRY0205707 - Start - Get Timesheet --> Timecard Profile
            if(TS.Timecard_Profile__c != null) {
                TimecardProfileIDSet.add(TS.Timecard_Profile__c);
            }
        }
        Map<Id, Timecard_Profile__c> timecardMap = new Map<Id, Timecard_Profile__c>([Select Id, Generate_Holiday_TE__c, Lunch_Time__c, Lunch_Time_Start__c, Lunch_Time_duration__c, 
                                                                                     First_Day_of_Week__c, Max_Hours_Day_1__c, Max_Hours_Day_2__c, Max_Hours_Day_3__c, Max_Hours_Day_4__c, 
                                                                                     Max_Hours_Day_5__c, Max_Hours_Day_6__c, Max_Hours_Day_7__c 
                                                                                     From Timecard_Profile__c Where Id in: TimecardProfileIDSet and Forward_Overtime_to_Bank__c = true]);
        // STRY0205707 - End - Get Timesheet --> Timecard Profile
        
        serviceResources = getServiceResources(serviceResources.keySet());
        for(SVMXC_Timesheet__c TS : lstTimeSheets) {
            ServiceResource technician = serviceResources.get(TS.Service_Resource__c);
            if(technician !=null){
                if(technician.Organizational_Calendar__c != null) {
                    setOrgCalanderId.add(technician.Organizational_Calendar__c);
                }
                
                if(TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c)) {
                    setTimeCardProfileId.add(TS.Timecard_Profile__c); // STRY0205707 - Assign Timesheet's Timecard Profile Id
                } else if(technician.Timecard_Profile__c != null) {
                    setTimeCardProfileId.add(technician.Timecard_Profile__c);
                }

                
                if(TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c) && 
                    timecardMap.get(TS.Timecard_Profile__c).Lunch_Time__c && 
                    timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_Start__c != null  && 
                     timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_duration__c != null) {
          timesheetsForLunchTimeEntries.add(TS.Id);    // STRY0205707 - Check Timesheet's Timecard Profile Id
                } else if(TS.Timecard_Profile__c == null || (technician.Timecard_Profile__r.Lunch_Time__c && 
                          technician.Timecard_Profile__r.Lunch_Time_Start__c != null && 
                          technician.Timecard_Profile__r.Lunch_Time_duration__c != null)) { // STRY0217972
          timesheetsForLunchTimeEntries.add(TS.Id);
                }

                if(TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c) && 
                   technician.Organizational_Calendar__c != null && timecardMap.get(TS.Timecard_Profile__c).Generate_Holiday_TE__c) { // STRY0205707 - Check Timesheet's Timecard Profile Id
          timesheetsForHolidayTimeEntries.add(TS.Id);
        } else if(TS.Timecard_Profile__c == null || (technician.Organizational_Calendar__c != null && technician.Timecard_Profile__r.Generate_Holiday_TE__c)) {
                    timesheetsForHolidayTimeEntries.add(TS.Id);
                }
            }
        }
        if(!timesheetsForLunchTimeEntries.isEmpty() || !timesheetsForHolidayTimeEntries.isEmpty()){
            for(Organizational_Activities__c OA :[
                SELECT Name, Id, Indirect_Hours__c FROM Organizational_Activities__c 
                WHERE Indirect_Hours__c IN : setTimeCardProfileId AND Type__c = 'Indirect' 
                AND ((Name LIKE '%Lunch%' AND Round_the_clock__c = false AND Unpaid_Time__c = true AND Recuperative_Time__c = false) 
                OR Name = 'Public Holiday')]){
                if(OA.Name.contains('Lunch')){
                    lunchActivities.put(OA.Indirect_Hours__c, OA.Id);
                }else if(OA.Name == 'Public Holiday'){
                    holidayActivities.put(OA.Indirect_Hours__c, OA.Id);
                }
            }
            for(Organizational_Holiday__c holiday : [
                SELECT Organizational_Calendar__c, Holiday_Date__c FROM Organizational_Holiday__c 
                WHERE Organizational_Calendar__c IN : setOrgCalanderId
            ]) {
                if(holiday.Holiday_Date__c != null) {
                    Set<Date> setAllHolidays = mapOrgCal_OrgHolidays.get(holiday.Organizational_Calendar__c);
                    if(setAllHolidays == null) {
                        setAllHolidays = new Set<Date>();
                    }
                    setAllHolidays.add(holiday.Holiday_Date__c);
                    setHolidays.add(holiday.Holiday_Date__c);
                    mapOrgCal_OrgHolidays.put(holiday.Organizational_Calendar__c, setAllHolidays);
                }
            }
            for(SVMXC_Timesheet__c TS : lstTimeSheets) {
                if(TS.Service_Resource__c!=null){
                    
                    ServiceResource technician = serviceResources.get(TS.Service_Resource__c);
                    Set<Date> holidays = mapOrgCal_OrgHolidays.get(technician.Organizational_Calendar__c);
                    Set<String> weekends = new Set<String>();
                    if(technician.Weekend_Override__c != null){
                        List<String> LstWeekendOverride = technician.Weekend_Override__c.split(';');
                        weekends.addAll(LstWeekendOverride);
                    }
                    for(Integer i = 0; i < 7 ; i++) {
                        Date currDate = TS.Start_Date__c + i;
                        DateTime currDateTime = Datetime.newInstance(currDate.year(), currDate.month(), currDate.day());
                        if(timesheetsForLunchTimeEntries.contains(TS.Id)){
                            if(holidays != null && technician.Organizational_Calendar__c != null){
                                
                                if(!holidays.contains(currDate) && !weekends.contains(currDateTime.format('EEEE'))) {
                                    // STRY0205707 - Get lunch time and duration
                                    Time lunchTime = (TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c) && 
                                                      timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_Start__c != null)?
                                                timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_Start__c.time():
                                              (technician.Timecard_Profile__r.Lunch_Time_Start__c != null) ?
                                              technician.Timecard_Profile__r.Lunch_Time_Start__c.time():null;
                                    Integer duration = (TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c) && 
                                                         timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_duration__c != null)?
                            Integer.valueOf(timecardMap.get(TS.Timecard_Profile__c).Lunch_Time_duration__c):
                                              (technician.Timecard_Profile__r.Lunch_Time_duration__c != null) ?
                                              Integer.valueOf(technician.Timecard_Profile__r.Lunch_Time_duration__c): null;

                                    if(lunchTime != null && duration != null)
                                      lstTimeEntryToInsert.add(getTimeEntry(indirectHoursTE, currDate, lunchTime, duration, lunchActivities.get(technician.Timecard_Profile__c),TS));
                                } 
                            }
                        }
                        if(timesheetsForHolidayTimeEntries.contains(TS.Id) && setHolidays.contains(currDate)){
                            // STRY0205707 - Get Timecard Profile 
                            Timecard_Profile__c TCP = (TS.Timecard_Profile__c != null && timecardMap.containskey(TS.Timecard_Profile__c))?
                                            timecardMap.get(TS.Timecard_Profile__c):technician.Timecard_Profile__r;

                            Map<String, Decimal> daysWorkingHoursMap = getDaysWorkingHoursMap(technician, TS, TCP);
                            if(daysWorkingHoursMap.containsKey(currDateTime.format('EEEE')) && 
                            daysWorkingHoursMap.get(currDateTime.Format('EEEE')) >0){
                                lstTimeEntryToInsert.add(getTimeEntry(
                                    indirectHoursTE, currDate, technician.Timecard_Hours__r.MondayStartTime,
                                    (daysWorkingHoursMap.get(currDateTime.Format('EEEE')) *60).intValue(), holidayActivities.get(technician.Timecard_Profile__c),
                                    TS
                                ));
                            }
                        }
                    }
                }
            }
        } 
        if(lstTimeEntryToInsert.size() > 0){
            insert lstTimeEntryToInsert;
        }
    }
    
    private static SVMXC_Time_Entry__c getTimeEntry(Id recordTypeId, Date currentDate, Time startTime, 
    Integer duration, Id orgActivityId, SVMXC_Timesheet__c timesheet){
        SVMXC_Time_Entry__c TE = new SVMXC_Time_Entry__c();
        DateTime currTEDateTime = Datetime.newInstance(currentDate.year(), currentDate.month(), currentDate.day(), startTime.hour(), startTime.minute(), 0);
        TE.Start_Date_Time__c = currTEDateTime;
        TE.End_Date_Time__c = currTEDateTime.addMinutes(duration);
        TE.Organizational_Activity__c = orgActivityId;
        TE.RecordTypeId = recordTypeId;
        TE.Timesheet__c = timesheet.Id;
        TE.Service_Resource__c = timesheet.Service_Resource__c;
        return TE;
    }
    
    
    public static void RollUpRecuperativeHoursToTimeSheet(Map<Id, SVMXC_Time_Entry__c> oldMapTE, Map<Id, SVMXC_Time_Entry__c> newFinalMapTE, Integer intOprCode) {
        Map<Id, SVMXC_Timesheet__c> mapTsId_TS_ToUpdate = new Map<Id, SVMXC_Timesheet__c>();
        
        if(intOprCode == 3) { //Delete Operation
            Set<Id> setparetnTSIds = new Set<Id>();
            for(Id idTE : oldMapTE.KeySet()) { 
                setparetnTSIds.add(oldMapTE.get(idTE).Timesheet__c);
            }
            //savon.FF 11-3-2015 If statement around query for optimization
            List<SVMXC_Timesheet__c> parentTimeSheets = new List<SVMXC_Timesheet__c>();

            if (!setparetnTSIds.isEmpty()) {
                parentTimeSheets = [SELECT Id, Total_Recuperative_Time__c FROM SVMXC_Timesheet__c WHERE Id IN : setparetnTSIds];
            }

            Map<ID, SVMXC_Timesheet__c> mapParentTimeSheets = new Map<ID, SVMXC_Timesheet__c>();
            for(SVMXC_Timesheet__c TS: parentTimeSheets) {
                mapParentTimeSheets.put(TS.Id, TS);
            }
            for(Id idTE : oldMapTE.KeySet()) {
                SVMXC_Time_Entry__c TE = oldMapTE.get(idTE);
                ID InDirRecType = Schema.SObjectType.SVMXC_Time_Entry__c.getRecordTypeInfosByName().get('Indirect hours').getRecordTypeId(); 
                if(TE.RECORDTYPEID == InDirRecType && TE.OA_Recuperative_Time__c == TRUE) {
                    SVMXC_Timesheet__c currPtTs = mapParentTimeSheets.get(TE.Timesheet__c);
                    if(currPtTs.Total_Recuperative_Time__c == null)
                        currPtTs.Total_Recuperative_Time__c = 0;
                    currPtTs.Total_Recuperative_Time__c = currPtTs.Total_Recuperative_Time__c - TE.TotalTime__c;
                    mapTsId_TS_ToUpdate.put(currPtTs.Id, currPtTs);
                }
            }
            if(mapTsId_TS_ToUpdate.size() > 0){
                TIMESHEET_UPDATED_FROM_TIME_ENTRY = true; // 
                update (mapTsId_TS_ToUpdate.values());
            }
        } else if(execute){
            execute = false;
            // Getting valid list of Time Entry for further processing
            Map<Id, SVMXC_Time_Entry__c> newMapTE = new Map<Id, SVMXC_Time_Entry__c>();
            Set<Id> setparetnTSIds = new Set<Id>();
            ID InDirRecType = Schema.SObjectType.SVMXC_Time_Entry__c.getRecordTypeInfosByName().get('Indirect hours').getRecordTypeId();
            for(SVMXC_Time_Entry__c TE : newFinalMapTE.values()) {  
                if(TE.RECORDTYPEID == InDirRecType && TE.OA_Recuperative_Time__c == TRUE) {
                    newMapTE.put(TE.Id, TE);
                    setparetnTSIds.add(TE.Timesheet__c);
                }
            }
            //savon.FF 10-30 add if statement to prevent unnecessary query for too many query error
            List<SVMXC_Timesheet__c> parentTimeSheets = new List<SVMXC_Timesheet__c>();
            
            if (!setparetnTSIds.isEmpty()) {
                parentTimeSheets = [SELECT Id, Total_Recuperative_Time__c FROM SVMXC_Timesheet__c WHERE Id IN : setparetnTSIds];
            }
            Map<ID, SVMXC_Timesheet__c> mapParentTimeSheets = new Map<ID, SVMXC_Timesheet__c>();
            for(SVMXC_Timesheet__c TS: parentTimeSheets) {
                mapParentTimeSheets.put(TS.Id, TS);
            }
            for(Id idTE : newMapTE.KeySet()) {
                SVMXC_Time_Entry__c TE = newMapTE.get(idTE);
                SVMXC_Timesheet__c currPtTs = mapParentTimeSheets.get(TE.Timesheet__c);
                if(currPtTs.Total_Recuperative_Time__c == null) 
                    currPtTs.Total_Recuperative_Time__c = 0;
                if(intOprCode == 1) { //Insert Operation
                    currPtTs.Total_Recuperative_Time__c = currPtTs.Total_Recuperative_Time__c + TE.TotalTime__c;
                    mapTsId_TS_ToUpdate.put(currPtTs.Id, currPtTs);
                } else if(intOprCode == 2) { //Update Operation
                    Double newVal = TE.TotalTime__c;
                    DOuble oldVal =  oldMapTE.get(idTE).TotalTime__c;
                    if(newVal != oldVal){
                        currPtTs.Total_Recuperative_Time__c = currPtTs.Total_Recuperative_Time__c + (newVal - oldVal);
                        
                    }
                    mapTsId_TS_ToUpdate.put(currPtTs.Id, currPtTs);
                } 
            }
            if(mapTsId_TS_ToUpdate.size() > 0){
                TIMESHEET_UPDATED_FROM_TIME_ENTRY = true; // 
                update (mapTsId_TS_ToUpdate.values());
            }
        }
    }
    
     public static void deleteRelatedEvents(List<SVMXC_Time_Entry__c> entryList){
        Set<Id> entryIdSet = new Set<Id>();
        for (SVMXC_Time_Entry__c entry : entryList){
            if (entry.Create_Event__c){
                entryIdSet.add(entry.Id);
            }
        }
        if (entryIdSet.size() > 0){
            List<Event> eventList = [SELECT Id, WhatId, StartDateTime, EndDateTime,OwnerId, Type, ShowAs FROM Event WHERE WhatId IN :entryIdSet  ];
            if (eventList.size() > 0){
                delete eventList;
            }
        }
    }
    

    private static Map<Id, ServiceResource> getServiceResources(Set<Id> serviceResourceIds){
        if(!serviceResourceIds.isEmpty()){
            return (Map<Id, ServiceResource>)JSON.deserialize(JSON.serialize(VFSL_Utility.getLookupRecordsMap(serviceResourceIds, 'ServiceResource', false, true, null, null, null, null, null, null)), Map<Id, ServiceResource>.class);
        }
        return null;
    }
  
    // STRY0205707 - Pass Timecard param and replace it with earlier Tech's timecard param
    private static Map<String, Decimal> getDaysWorkingHoursMap(ServiceResource technician, SVMXC_Timesheet__c tSheet, Timecard_Profile__c timecard){
    Map<String, Decimal> daysHoursMap =  new  map<String, Decimal>();
        if(technician.Organizational_Calendar__c != null){
            if(timecard.First_Day_of_Week__c == 'Saturday'){
                daysHoursMap.put('Saturday',Decimal.ValueOf(timecard.Max_Hours_Day_1__c));
                daysHoursMap.put('Sunday',Decimal.ValueOf(timecard.Max_Hours_Day_2__c)); 
                daysHoursMap.put('Monday',Decimal.ValueOf(timecard.Max_Hours_Day_3__c)); 
                daysHoursMap.put('Tuesday',Decimal.ValueOf(timecard.Max_Hours_Day_4__c)); 
                daysHoursMap.put('Wednesday',Decimal.ValueOf(timecard.Max_Hours_Day_5__c)); 
                daysHoursMap.put('Thursday',Decimal.ValueOf(timecard.Max_Hours_Day_6__c)); 
                daysHoursMap.put('Friday',Decimal.ValueOf(timecard.Max_Hours_Day_7__c));    
            } 
         
            if(timecard.First_Day_of_Week__c == 'Sunday'){
                daysHoursMap.put('Sunday',Decimal.ValueOf(timecard.Max_Hours_Day_1__c)); 
                daysHoursMap.put('Monday',Decimal.ValueOf(timecard.Max_Hours_Day_2__c)); 
                daysHoursMap.put('Tuesday',Decimal.ValueOf(timecard.Max_Hours_Day_3__c)); 
                daysHoursMap.put('Wednesday',Decimal.ValueOf(timecard.Max_Hours_Day_4__c)); 
                daysHoursMap.put('Thursday',Decimal.ValueOf(timecard.Max_Hours_Day_5__c)); 
                daysHoursMap.put('Friday',Decimal.ValueOf(timecard.Max_Hours_Day_6__c));
                daysHoursMap.put('Saturday',Decimal.ValueOf(timecard.Max_Hours_Day_7__c));    
            }
         
            if(timecard.First_Day_of_Week__c == 'Monday'){
                daysHoursMap.put('Monday',Decimal.ValueOf(timecard.Max_Hours_Day_1__c)); 
                daysHoursMap.put('Tuesday',Decimal.ValueOf(timecard.Max_Hours_Day_2__c)); 
                daysHoursMap.put('Wednesday',Decimal.ValueOf(timecard.Max_Hours_Day_3__c)); 
                daysHoursMap.put('Thursday',Decimal.ValueOf(timecard.Max_Hours_Day_4__c)); 
                daysHoursMap.put('Friday',Decimal.ValueOf(timecard.Max_Hours_Day_5__c));
                daysHoursMap.put('Saturday',Decimal.ValueOf(timecard.Max_Hours_Day_6__c));
                daysHoursMap.put('Sunday',Decimal.ValueOf(timecard.Max_Hours_Day_7__c));
            }  
        }
        return daysHoursMap;
    }

    private static Map<String, SVMXC_Timesheet__c> getDuplicateUniqueMap(Set<String> uniqueIdSet) {
        Map<String, SVMXC_Timesheet__c> uniqueMap = new Map<String, SVMXC_Timesheet__c>();
        for(SVMXC_Timesheet__c ts:[Select Id, TimeSheet_Unique__c From SVMXC_Timesheet__c Where TimeSheet_Unique__c =: uniqueIdSet]) {
            uniqueMap.put(ts.TimeSheet_Unique__c, ts);
        }
        return uniqueMap;
    }
    
}
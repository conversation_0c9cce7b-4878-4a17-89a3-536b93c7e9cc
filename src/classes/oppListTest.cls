@isTest
public class oppListTest 
{
    @isTest
    public static void oppList()
    {
        Boolean Flag = true;
        Boolean Flag1 = false;
        String sortField = 'Name';
        boolean isAsc = true;
         boolean isAsc1 = false;
        Account acct = new Account();
        acct.Name = 'Test Account';
        acct.Country__c = 'USA';
        acct.OMNI_Postal_Code__c = '12121';
        acct.Account_Type__c = 'Customer';
        acct.BillingCity = 'San Jose';
        acct.BillingState = 'CA';
        acct.BillingCountry = 'USA';        
        insert acct;
        
        Contact con = new Contact();
        con.AccountId = acct.Id;
        con.FirstName = 'VARIAN TEST';
        con.LastName = 'UTILITY';
        con.MailingCountry = 'USA';
        con.MailingState = 'CA';
        con.MailingPostalCode = '12345'; 
        con.Email = '<EMAIL>';
        insert con;
        
        Opportunity opp = new Opportunity(
            Name='New Opp', AccountId = acct.Id, Primary_Contact_Name__c = con.Id, Libra_Opportunity_Product__c='', StageName='7 - CLOSED WON', CloseDate=date.valueof('2019-01-01'), Type='LIBRA II');
        insert opp;
        
        oppList.getopplist(acct.Id, Flag, sortField, isAsc);
        oppList.getopplist(acct.Id, Flag1, sortField, isAsc1);
        
        
    }
}
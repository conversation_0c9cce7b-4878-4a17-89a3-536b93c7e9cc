@isTest
public class activateDeactivateTaskRayUserTest {
   
      @testSetup static void methodName() {
      
         User readandwriteUser ;
        User readandwriteUser2 ;
        Id userProfileId2= [select Id, Name from Profile where name = 'System Administrator' limit 1].id;
        Id userProfileId= [select Id, Name from Profile where name = 'VMS MyVarian - Customer User' limit 1].id;
        Id roleid = [select id  from UserRole where name='NA Regional Dir. and Admins'].id;
        readandwriteUser = new User(
            FirstName='Naruto',
            LastName = 'Uzumaki',
            Alias = 'fox',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            ProfileId =userProfileId2,
            isActive=true,
            UserRoleId=roleid,
            TimeZoneSidKey = 'GMT',
            LanguageLocaleKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LocaleSidKey = 'en_US');        
        insert readandwriteUser;
        User readAndWriteUser4 = [Select Id,FirstName from User where Username = '<EMAIL>' LIMIT 1]; 
        
        
        System.runAs(readAndWriteUser4) {
            
            Account a = new Account();
            a.Name='test';
            a.Site_Type__c = 'N/A';
            a.BillingCity='Milpitas';
            a.BillingCountry='USA';
            a.BillingGeocodeAccuracy='Zip';
            a.BillingPostalCode='95035';
            a.BillingState='CA';
            a.Country__c='USA';
            a.ERP_Site_Partner_Code__c='646546';
            insert a;
            
            Account a1 = new Account();
            a1.Name='test1';
            a1.Site_Type__c = 'N/A';
            a1.BillingCity='dsasd';
            a1.BillingCountry='USA';
            a1.BillingGeocodeAccuracy='Zip';
            a1.BillingPostalCode='121';
            a1.BillingState='CA';
            a1.Country__c='USA';
            insert a1;
            
            Contact c1 = new contact();
            c1.Institute_Name__c=a.Name;
            c1.MailingPostalCode=a.BillingPostalCode;
            c1.MailingStreet=a.BillingStreet;
            c1.MailingState=a.BillingState;
            c1.MailingCity=a.BillingCity;
            c1.AccountId=a.Id;
            c1.lastname='test';
            c1.Phone='**********';
            c1.Email='<EMAIL>';
            c1.firstName='test';
            c1.MyVarian_Member__c=true;
            c1.Active_Contact__c=a.Id;
            c1.Is_Preferred_Language_selected__c=true;
            //c1.Language_ISOCode__c='English';
            c1.VIC_Portal_Role__c='Customer Account Manager';
            c1.Preferred_Language1__c='English';
            c1.OCSUGC_UserStatus__c='OCSUGC Disqualified';
            c1.OCSUGC_UserViewPreference__c='Block View';
            c1.Recovery_Answer__c='test';
            c1.Recovery_Question__c='test';
            //c1.Region__c='Southern Region';
            c1.Salutation='Mr.';
            c1.ShowAccPopUp__c=true;
            c1.Specialty__c='Information Technology';
            c1.Functional_Role__c='Account Payable Capital';
            c1.Subscription_Number__c='3245234';
            insert c1; 
            
          
            Test.startTest();
            //Create user
            //Id roleid2 = [select id  from UserRole where name='STANFORD Partner User'].id;
            id profileid = [select id ,profileid from user where profile.Name='VMS MyVarian - Customer User' limit 1].profileid;
            User user1 = new User(
                Username = System.now().millisecond() + '<EMAIL>',
                ContactId = c1.Id,
                ProfileId = profileid,
                Alias = 'test123',
                Email = '<EMAIL>',
                EmailEncodingKey = 'UTF-8',
                LastName = 'McTesty',
                //userroleid=roleid,                
                CommunityNickname = 'test12345',
                TimeZoneSidKey = 'America/Los_Angeles',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US'
            );
            Database.insert(user1);
            
        
      
          PackageLicense taskRayLicense     = [Select Id FROM PackageLicense WHERE NamespacePrefix='TASKRAY'];
          UserPackageLicense indivUserPackageLicense = new UserPackageLicense();
          indivUserPackageLicense.UserId =user1.Id;
          indivUserPackageLicense.PackageLicenseId = taskRayLicense.Id;
          insert indivUserPackageLicense;
       
       
        TASKRAY__Project__c template;
        TASKRAY__trTaskGroup__c TG1;
       
       TASKRAY__trDependency__c dep;
        
        template = new TASKRAY__Project__c();
        template.Name = 'Primary Template';
        template.TASKRAY__trAvailableExternal__c = false;
        template.TASKRAY__trTemplate__c = true;
              template.TaskRay_Template__c = true;
        template.OwnerId =user1.id;
        template.TASKRAY__trDraft__c = true;
        insert template;
        
        TG1 = new TASKRAY__trTaskGroup__c();
        TG1.Name = 'TG1';
        TG1.TASKRAY__Project__c = template.Id;
        TG1.TASKRAY__trStartDate__c = Date.newInstance(2020, 2, 17);
        TG1.TASKRAY__trEndDate__c = Date.newInstance(2020, 5 , 17);
        TG1.Task_Group_Linked_To_Start__c = true;
        TG1.TASKRAY__trSortOrder__c = 0;
        insert TG1;
        
        TASKRAY__Project_Task__c task1 = new TASKRAY__Project_Task__c();
        task1.Name = 'Task 1';
        task1.TASKRAY__trTaskGroup__c = TG1.Id;
        task1.TASKRAY__Project__c = template.Id;
        task1.TASKRAY__trStartDate__c = Date.newInstance(2020, 5, 18);
        task1.TASKRAY__trProjectSortOrder__c = 1;
        task1.Key_Milestone_Task__c = true;
        insert task1;
        Test.stopTest();
        }
      
   }
   @isTest static void validateActivateDeactivateTaskRay() {
        TASKRAY__Project__c template = [SELECT Id FROM TASKRAY__Project__c WHERE Name ='Primary Template' LIMIT 1];
        Contact con = [Select Id,FirstName from Contact where LastName='test'];
        List<string> conIds = new list<string>();
        conIds.add(con.Id);
        Test.startTest();
        activateDeactivateTaskRayUser.isTaskRayMyVarianAssignment();
        activateDeactivateTaskRayUser.getContactsForActivation(template.Id);
        activateDeactivateTaskRayUser.activateSelectedContact(conIds,template.Id);
        activateDeactivateTaskRayUser.deactivateSelectedUsers(conIds,template.Id);
        activateDeactivateTaskRayUser.getUserForDeactivation(template.Id);
        activateDeactivateTaskRayUser.createTeamMembers(conIds,template.Id);
        Test.stopTest();
   }
}
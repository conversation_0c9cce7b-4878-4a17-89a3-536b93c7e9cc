/*
 * author: <PERSON><PERSON><PERSON>
 * description : apex controller for login, navigation, log out register user etc components
 * used in mvheadercomponet backed code controller
 */
public without sharing class MvHeaderComponentController {
    
    /*data members*/
    public static User loginUser ;
    public static String onetimeToken = null;
    public static String oktaUserId = null;
    public static String sessionId = null;
    public static Contact con = MvUtility.getContact();
    /* Get user data on component */
    @AuraEnabled
    public static User getLoggedInUser(){
        return [SELECT Id, Name, Email,SmartConnect_access__c,ContactId,Dosiometrist__c,Contact.AccountId,Contact.Preferred_Language1__c,
                Contact.Is_Preferred_Language_selected__c, Contact.ShowAccPopUp__c, 
                Contact.Email_Opt_in__c, Contact.HasOptedOutOfEmail, Contact.TaskRay_Member__c,
                LMS_Status__c, Contact.PasswordReset__c, is_Model_Analytic_Member__c,lastname, ePeerReview_Member__c,Contact.Account.MarketingKitRoleSummary__c
                ,usertype,alias,Contact.Distributor_Partner__c,Contact.Account.country1__r.name FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
    //  STSK0020564 - RB - Added  SmartConnect_access__c field to the query
    }
    
    @AuraEnabled
    public static boolean isMarketingUser(){
     /*   User u = [SELECT Id, LastName, Email, Contact.MailingCountry, Contact.AccountId FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
        if(u.lastname == 'Site Guest User'){
            return false;
        }
        String conCntry = u.Contact.MailingCountry;
        boolean IsMarketYourCenter = false;
        if (conCntry != null) {
            list < MarketingYourCenterCountries__c > AllCountryCount = MarketingYourCenterCountries__c.getall().values();
            for (MarketingYourCenterCountries__c MrktCountries: AllCountryCount) {
                if (MrktCountries.Name == conCntry) {
                    IsMarketYourCenter = true;
                }
            }
        }else{
            IsMarketYourCenter = true;
        }
        return IsMarketYourCenter;*/
      //  return true;  // As per STRY0085655[MyVarian: Change acess to some Marketing Kits.]
        Boolean isMarketing = true;
        User u = [SELECT Id, LastName, Email, Contact.MailingCountry,Contactid, Contact.AccountId FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
        if ( u.Contactid != null){
        Account acc = [Select Id, MarketingKitRoleSummary__c from Account where Id =: u.Contact.AccountId];
        if((acc.MarketingKitRoleSummary__c == null) || (acc.MarketingKitRoleSummary__c == 0))
            isMarketing = false;
    }
        return isMarketing;
    }
    //STRY0116730 : Dipali 
    @AuraEnabled
    public static boolean getValidforknwnIssues(){
        List<Product_Roles__c> lstPrdRole = new List<Product_Roles__c>();
        List<String> lstPublicGroup = new list<String>();
        User u = getLoggedInUser();
        lstPrdRole = [Select Product_Name__c,Public_Group__c from Product_Roles__c where ismarketingKit__c = false and (Name = 'ARIA Medical Oncology' OR Name = 'ARIA Radiation Oncology')];
        for(Product_Roles__c prodRoleRec : lstPrdRole){
            lstPublicGroup.add(prodRoleRec.Public_Group__c);
           }
        if(u != null && u.contactid != null)
         {
          List<GroupMember> lstGroupMember = new List<GroupMember>();
          lstGroupMember = [Select GroupId,UserOrGroupId,Group.Name from GroupMember where Group.Name in:lstPublicGroup and UserOrGroupId =: UserInfo.getuserId()];
          if(lstGroupMember != null && lstGroupMember.size() > 0)
            return true;
         }
        else if(u != null && u.contactid == null)
            return true;
        return false;
    }
    @AuraEnabled
    public static Contact getContactInfo() 
    {
        system.debug('#### debug getContactInfo called');
        con = MvUtility.getContact();
        return con;
    }
    
    @AuraEnabled
    public static void saveMyContact() 
    {
        con.showAccPopUp__c = true;
        update con;
    }

    @AuraEnabled
    public static void updateUserLMSMethod() {
        User usr = [Select Id, KCS_Coach__c,LMS_Last_Access_Date__c From User where id = : Userinfo.getUserId() limit 1];
        usr.LMS_Last_Access_Date__c = system.now();
        update usr;
    } 
    
    //STRY0171810 : Ilaya Reddy : Start: Added below method to populate Taskray lastaccess date field on user object
    @AuraEnabled
    public static void updateUserTaskrayMethod() {
        User usr = [Select Id,Taskray_Last_Access_Date__c From User where id = : Userinfo.getUserId() limit 1];
        usr.Taskray_Last_Access_Date__c = system.now();
        update usr;
    } 
    //STRY0171810 : Ilaya Reddy : End: Added below method to populate Taskray lastaccess date field on user object
    
    /* Check authentication */
    @AuraEnabled
    public static boolean isLoggedIn(){
        User u = [SELECT Id, LastName, Email, Contact.AccountId FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
        if(u.lastname == 'Site Guest User'){
            return false;
        }else{
            return true;
        }
        
    }

    @AuraEnabled
    public static List<String> getLanguagePicklistOptn(String fieldName)
    {
        List<String> pickListValues = new List<String>();
        Schema.SObjectType targetType = Schema.getGlobalDescribe().get('Contact');
        Schema.DescribeSObjectResult sobjResult = targetType.getDescribe();
        Map<String, Schema.SObjectField> fieldMap = sobjResult.fields.getMap();
        Schema.DescribeFieldResult descField = fieldMap.get(fieldName).getDescribe();
        List<Schema.PicklistEntry> ple = descField.getPicklistValues();
        for(Schema.PicklistEntry f : ple){
                pickListValues.add(f.getLabel()+','+f.getValue());
        } 
        return pickListValues;
    }
    
   /* Check Partner Channel user */
    @AuraEnabled
    public static List<GroupMember> isPartnerChannelUsr() {

        /*Map<String, My_Varian_Partner_Channel_Group__c> mapParterChnSets = My_Varian_Partner_Channel_Group__c.getAll();
        List<String> listGrps = new List<String>();
        if(mapParterChnSets != null) {
            listGrps.addAll(mapParterChnSets.keySet());
        }

        List<GroupMember> listGrpMbr = [SELECT groupId, UserOrGroupId, group.name, group.type FROM GroupMember 
             WHERE UserOrGroupId =: UserInfo.getUserId() 
             AND group.name IN :listGrps];

        if(listGrpMbr.size() > 0){
            return true;
        }else{
            return false;
        }*/
       List<string> result=new List<string>();


        Map<String, My_Varian_Partner_Channel_Group__c> mapParterChnSets = My_Varian_Partner_Channel_Group__c.getAll();
        List<String> listGrps = new List<String>();
        if(mapParterChnSets != null) {
            listGrps.addAll(mapParterChnSets.keySet());
        }

         listGrps.add('PTInstallation_Russia');
         listGrps.add('PTInstallation_China');


        List<GroupMember> listGrpMbr = [SELECT groupId, UserOrGroupId, group.name, group.type FROM GroupMember 
             WHERE UserOrGroupId =: UserInfo.getUserId() 
             AND group.name IN :listGrps];

         /*for(GroupMember gp:listGrpMbr){
             if(gp.group.name=='PTInstallation_Russia' || gp.group.name=='PTInstallation_China')
             {
                 result.add(true);
             }
             if(gp.group.name!='PTInstallation_Russia' && gp.group.name!='PTInstallation_China')
             {
                 result.add(true);
             }
         }*/
        
            return listGrpMbr;
        
    }

    /*Get logged in user's Country if it is in Sanctioned custom setting */
    @AuraEnabled
    public static Boolean isCurrentUserInSancCountries() {
        Boolean curUserInSancCountry = false;
        User un = [Select Id, AccountId,ContactId,usertype,alias,Contact.Distributor_Partner__c,Contact.Account.country1__r.name From User where id = : Userinfo.getUserId() limit 1];
        if(Un.ContactId <> null && un.Contact.AccountId <> null && Un.Contact.Account.country1__c <> null){
            Sanctioned_Countries__c csett = Sanctioned_Countries__c.getValues(un.Contact.Account.country1__r.Name);
            if(csett <> null){
                curUserInSancCountry = true;
            }
        }       
        return curUserInSancCountry;
    }

    /*Check if logged in User in VMS PT - Installation public group or not */
    @AuraEnabled
    public static Boolean isCurUserPTInstGrp() {
        Boolean curUserPTInstGrp = false;
        /*List<User> listUsers = [Select Id, AccountId,ContactId,usertype,alias,Contact.Distributor_Partner__c,Contact.Account.country1__r.name From User where id = : Userinfo.getUserId() limit 1];
        if(listUsers.size() > 0){
            list<GroupMember> listGrpM = [Select Id, group.type, group.name, UserOrGroupId From GroupMember where group.name ='VMS PT - Installation' and UserOrGroupId = :listUsers[0].Id];
            if(listGrpM.size() > 0){
                curUserPTInstGrp = true;
            }
        }  */ 

        List<GroupMember> lstRussia = [select Id from GroupMember where userorGroupId =: userinfo.getUserId() and Group.Name = 'PTInstallation_Russia'];
        List<GroupMember> lstChina = [select Id from GroupMember where userorGroupId =: userinfo.getUserId() and Group.Name = 'PTInstallation_China'];
            
        if(lstRussia.size()>0 || lstChina.size()>0){
            curUserPTInstGrp = true;
        }  
        return curUserPTInstGrp;
    }
    
    @AuraEnabled
    public static Boolean isTaskRayMember() {
        User un = [Select Id, ContactId,Contact.TaskRay_Member__c From User where id = : Userinfo.getUserId() limit 1];
        
        return un.Contact.TaskRay_Member__c;
    }
    
   /*Get logged in user's Country if it is in Support Case Approved Countries custom settings */
    @AuraEnabled
    public static resultWrapper isCurrentUserInCaseApprovCountries() {
        resultWrapper wrapList=new resultWrapper();
        Boolean checkcountry = false;
        set<String> countryset = new set<String>();
        Id usId= Userinfo.getUserId();
        User un = [Select Id, AccountId,ContactId,usertype,alias,Contact.Distributor_Partner__c,Contact.Account.country1__r.name From User where id = : usId limit 1];
        if (un.ContactId == null)
        {
            checkcountry = true;
        }
        if(un.Contact.Distributor_Partner__c)
        {
            checkcountry = true;
        }
        for(Support_Case_Approved_Countries__c countries : Support_Case_Approved_Countries__c.getall().values())
        {
            countryset.add(countries.name);
        }
        if(countryset.contains(un.Contact.Account.country1__r.name) && checkcountry == false)
        {
            checkcountry = true;
        }
        for (Contact_Role_Association__c contactacc : [Select Account__c,Account__r.country1__r.name from Contact_Role_Association__c where contact__c =: un.ContactId])
        {
            if(checkcountry == false && countryset.contains(contactacc.Account__r.country1__r.name))
            {
                checkcountry = true;
            }
        }
        
        //Below code is used to check user is eligible at sanctioned country
        Boolean curUserInSancCountry = false;
         if(Un.ContactId <> null && un.Contact.AccountId <> null && Un.Contact.Account.country1__c <> null){
            Sanctioned_Countries__c csett = Sanctioned_Countries__c.getValues(un.Contact.Account.country1__r.Name);
            if(csett <> null){
                curUserInSancCountry = true;
            }
        }    
        
        //Below code is used to check user is eligible as a partner or Installation eligible user
         Map<String, My_Varian_Partner_Channel_Group__c> mapParterChnSets = My_Varian_Partner_Channel_Group__c.getAll();
        List<String> listGrps = new List<String>();
        if(mapParterChnSets != null) {
            listGrps.addAll(mapParterChnSets.keySet());
        }

         listGrps.add('PTInstallation_Russia');
         listGrps.add('PTInstallation_China');


        List<GroupMember> listGrpMbr = [SELECT groupId, UserOrGroupId, group.name, group.type FROM GroupMember 
             WHERE UserOrGroupId =: usId
             AND group.name IN :listGrps];
         Boolean isPartnerChannelUsr = false;
         Boolean isPTInstallationUsr = false;
        for(GroupMember gp:listGrpMbr){
             if(gp.group.name=='PTInstallation_Russia' || gp.group.name=='PTInstallation_China')
             {
                 isPTInstallationUsr = true;
             }
             if(gp.group.name!='PTInstallation_Russia' && gp.group.name!='PTInstallation_China')
             {
                isPartnerChannelUsr = true;
             }
         }
       
        //approvedCountry,sanctionedCountry,PartnerUser,InstallationUser, 
        //wrapList.add(new resultWrapper(checkcountry,curUserInSancCountry,isPartnerChannelUsr,isPTInstallationUsr));
         //system.debug('test here');
        wrapList.approvedCountry=checkcountry;
        wrapList.SanctionCountry=curUserInSancCountry;
        wrapList.isPartnerChannelUsr=isPartnerChannelUsr;
        wrapList.isPTInstallationUsr=isPTInstallationUsr;
        return wrapList;
    }

 //Below Wrapper is created to return Common result at component
   public class resultWrapper {
       @AuraEnabled
    public boolean approvedCountry {get; set;}
       @AuraEnabled
    public boolean SanctionCountry {get; set;}
       @AuraEnabled
    public boolean isPartnerChannelUsr {get; set;}
       @AuraEnabled
    public boolean isPTInstallationUsr {get; set;}  
      
  }


    /*
     * custom login code using okta
     */
    //STRY0569414 removed loginCommunity & saveUserSessionId methods as no longer needed.
    
    /*
     * Salesforce and Okta log out method
     */    
    @AuraEnabled
    public static String logoutCustom() {
        String returnURL = '';
        Map<String,usersessionids__c> sessionmap = new map<String,usersessionids__c>();
        try{
            System.debug('****0'+sessionmap.size());
            //sessionmap = usersessionids__c.getall();
            //system.assert(false);
            for(usersessionids__c u :[Select name,session_id__c from usersessionids__c where name =: userinfo.getuserid()]) {
                
                
                sessionmap.put(u.name,u);
            }
            System.debug('****1'+sessionmap.size());
            HttpRequest reqgroup = new HttpRequest();
            HttpResponse resgroup = new HttpResponse();
            Http httpgroup = new Http();
            String strToken = Label.oktatoken;
            String authorizationHeader = 'SSWS ' + strToken;
            reqgroup.setHeader('Authorization', authorizationHeader);
            reqgroup.setHeader('Content-Type','application/json');
            reqgroup.setHeader('Accept','application/json');
            System.debug('****2'+sessionmap.size());
            
            //sendEmail(new List<String>{'<EMAIL>'},'in method',String.valueof(sessionmap.size()),'VMS Logout');
            
            if(sessionmap.get(userinfo.getuserid()) != null) {
                String endpointgroup = Label.oktaendpointsessionkill + sessionmap.get(userinfo.getuserid()).session_id__c;
                //String endpointgroup = Label.oktaendpointsessionkill + sessionmap.get('0034C000006w0nc').session_id__c;
                reqgroup.setEndPoint(endpointgroup);
                reqgroup.setMethod('DELETE');
                System.debug('****3'+sessionmap.size());
                //try {
                    if(!Test.IsRunningTest()){
                        resgroup = httpgroup.send(reqgroup);
                        //sendEmail(new List<String>{'<EMAIL>'},resgroup.getBody(),resgroup.getBody(),'VMS Logout');
                        if(resgroup.getStatusCode() == 200 && resgroup.getBody() != null) {
                            returnURL = '/varian';
                        }
                    }
                //} catch (System.CalloutException e){
                  //  system.debug('Error***'+resgroup.toString());
                //}
                system.debug('Error***'+resgroup.toString());
                System.debug('****3'+sessionmap.size());                
            }

            // TOTO --- this functionality will be done later
            //Freeze user account when user changes the email
            /*
            List<Contact_Updates__c> listConUpdEmail
                = [SELECT Id, Contact__c, New_Account__c, Old_Account__c,
                    Old_Email__c, New_Email__c
                    FROM Contact_Updates__c
                    WHERE Contact__c = :con.Id
                    AND New_Email__c != :con.Email
                    AND Old_Email__c = :con.Email
                    AND New_Account__c = :con.Account.Name
                    AND CreatedDate >= TODAY];
            */
            List<Contact> listFrzCon = [SELECT Id FROM Contact
                                WHERE Freeze_User__c = true 
                                and Updated_Email__c != null and Id = :con.Id];


            system.debug('#### debug con.Id in logout = ' + con.Id);
            //system.debug('#### debug con.Institute_Name__c in logout = ' + con.Institute_Name__c);
            system.debug('#### debug con.Account.Name in logout = ' + con.Account.Name);

            if(listFrzCon.size() > 0 ) {
                system.debug('#### debug freezing user');
                Set<Id> setIds = new Set<Id>();
                setIds.add(con.Id);
                freezeUser(setIds);     

                ////con.Email = listConUpdEmail[0].New_Email__c;
                //con.Inactive_Contact__c = true;
                //con.PasswordReset__c = false;
                //con.Is_Preferred_Language_selected__c = false;
                ///update con;   //// Need to confirm from business             
            }
            

            //De-Activate the contact record when user changes the Institute Name

            
            List<Contact_Updates__c> listConUpdIns
                = [SELECT Id, Contact__c, New_Account__c, Old_Account__c,
                    Old_Email__c, New_Email__c
                    FROM Contact_Updates__c
                    WHERE Contact__c = :con.Id
                    AND New_Account__c != :con.Account.Name
                    AND Old_Account__c = :con.Account.Name
                    AND New_Email__c = :con.Email
                    AND CreatedDate >= TODAY]; 

            /*
            List<Contact> listCon = [SELECT Id FROM Contact WHERE
                                        Institute_Name__c != null
                                        AND LastModifiedDate >= TODAY]; */

            system.debug('#### debug con.Id in logout = ' + con.Id);
            //system.debug('#### debug con.Institute_Name__c in logout = ' + con.Institute_Name__c);
            system.debug('#### debug con.Account.Name in logout = ' + con.Account.Name);

            if(listConUpdIns.size() > 0 || Test.isRunningTest()) {   
                system.debug('#### debug de-activating user');  
                String emailToTransfer = con.Email;
                System.debug('#### emailToTransfer = ' + emailToTransfer);

                ////con.Email = null;
                con.MyVarian_Member__c = false;
                con.PasswordReset__c = false;
                con.Inactive_Contact__c = true;
                con.Is_Preferred_Language_selected__c = false;   
                con.ShowAccPopUp__c = false;             
                if(!Test.isRunningTest()) {
                    update con;
                }  

                list<Account> acc = new list<Account>();
                acc = [select Id, Name, BillingPostalCode, BillingStreet, BillingState, BillingCity 
                        from Account
                        where Name =: con.Institute_Name__c
                        and BillingPostalCode =: con.MailingPostalCode];
                        //and BillingStreet =: registerDataObj.MailingStreet
                        //and BillingState =: registerDataObj.MailingState
                        //and BillingCity =: registerDataObj.MailingCity];   

                System.debug('#### debug con in MvHeaderComponentController = ' + con);

                 
                Id actId; 
                if(acc.size() >0){
                    ////registerDataObj.AccountId = acc[0].Id;   
                    actId = acc[0].Id; 
                }   

                Contact newCon = con.clone(false,false,false,false);
                system.debug('#### newCon after cloning = ' + newCon);
                newCon.Institute_Name__c = con.Institute_Name__c;
                newCon.AccountId = actId;

                newCon.Date_of_Factory_Tour_Visit__c = system.today();
                newCon.Registered_Portal_User__c = true;
                newCon.MyVarian_Registration_Date__c=Date.Today();
                newCon.OCSUGC_Contact_Source__c = 'MyVarian';
                newCon.email = emailToTransfer;
                newCon.OktaLogin__c = null;
                newCon.OktaId__c = null;
                newCon.HasOptedOutOfEmail = false;
                newCon.Email_Opt_in__c = false;
                newCon.Recovery_Answer__c = null;
                newCon.Recovery_Question__c = null;
                newCon.MvMyFavorites__c = null;
                //newCon.MyVarian_Member__c = true;
                newCon.Inactive_Contact__c = false;

                list<User> listMyVarianUser = [select id from user where name = 'MyVarian' LIMIT 1];
                if(listMyVarianUser.size() > 0) {
                    newCon.OwnerId = listMyVarianUser[0].Id;
                }

                system.debug('#### newCon before inserting = ' + newCon); 
                try {              
                    ////insert newCon;  

                    Database.DMLOptions dml = new Database.DMLOptions();
                    dml.DuplicateRuleHeader.AllowSave = true; 
                    ////Account duplicateAccount = new Account(Name='dupe'); 
                    Database.SaveResult sr = Database.insert(newCon, dml); 
                    if (sr.isSuccess()) {   
                        System.debug('Bypassed duplicate contact rule!'); 
                    }                       
                    system.debug('#### newCon after inserting = ' + newCon);    
                } catch (Exception e) {
                    system.debug('#### debug error found = ' + e.getMessage());
                }

                delete listConUpdIns;               
            }


            //returnURL = 'https://sfqa1-varian.cs77.force.com/servlet/networks/switch?startURL=%2Fsecur%2Flogout.jsp';
            returnURL = 'https://sfqa-varian.cs15.force.com/';
            return returnURL;
        }
        catch(Exception e) {
            returnURL = '/varian';
            //sendEmail(new List<String>{'<EMAIL>'},'Logout Failed',e.getMessage()+e.getStackTraceString(),'VMS Logout');
            return returnURL;
            
        }          
    }

    @future(Callout = false)
    public static void FreezeUser(set < Id > stContactIds) {
        List < UserLogin > LstuserLogins = new List < UserLogin > ();
        LstuserLogins = [Select id, isFrozen from UserLogin where UserId in(SELECT Id FROM User WHERE ContactId IN :stContactIds)];

        for (UserLogin varuser: LstuserLogins) {
            varuser.isFrozen  = true;
        }
        if (LstuserLogins.size() > 0) {
            Update LstuserLogins;
            System.debug('Working code 2*****');
        }


    }  
    
    /*
     * Helper send email to debug the code
     */
    public static void sendEmail(List<String> toAddresses,String subject,String mailBody,String senderName)  {  
        List<Messaging.SingleEmailMessage> mails = new List<Messaging.SingleEmailMessage>();  
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();  
        mail.setToAddresses(toAddresses);  
        mail.setSenderDisplayName(senderName);  
        mail.setSubject(subject);  
        mail.setHtmlBody(mailBody);  
        mails.add(mail);  
        Messaging.sendEmail(mails);  
   }
    
    @AuraEnabled
    public static map<String,String> getCustomLabelMap(String languageObj){
        system.debug(' ##### cacheMap2 ##### ' + MvUtility.getCustomLabelMap(languageObj));
        return MvUtility.getCustomLabelMap(languageObj);
    }
    
    @AuraEnabled
    public static List<picklistEntry> getPreferredLanguages(){
        List<picklistEntry> lstLanguages = new List<picklistEntry>();
        Schema.DescribeFieldResult fieldResult = Contact.Preferred_Language1__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for( Schema.PicklistEntry f : ple){
            lstLanguages.add(new picklistEntry(f.getValue(),f.getLabel()));
        }
        
        //lstLanguages.sort();
        return lstLanguages;
    }
    @AuraEnabled
    public static Boolean savePreferredLanguage(string language){
        try{         
            User usr = [SELECT Id, Name, ContactId FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
            if(usr.ContactId !=null && string.isNotBlank(language)){
                //Preferred_Language_UI__c = language,
                Contact con = new Contact(Id=usr.ContactId,Preferred_Language1__c = language,Is_Preferred_Language_selected__c=true);
                update con;
                return true;
            }else{
               return false; 
            }
        }
        catch(Exception ex){
            return false;
            //return ex.getMessage();
        }
    }
    
    public class picklistEntry implements Comparable{
        @AuraEnabled public string pvalue{get;set;}
        @AuraEnabled public string label{get;set;}
        
        public picklistEntry(string pvalue,string label){
            this.pvalue = pvalue;
            this.label = label;            
        }
        
        public Integer compareTo(Object compareTo) {
            picklistEntry compareToOppy = (picklistEntry)compareTo;
            Integer returnValue = 0;
            if (label > compareToOppy.label)
                returnValue = 1;
            else if (label < compareToOppy.label)
                returnValue = -1;        
            return returnValue;       
        }
    }

    @AuraEnabled
    public static List<mvMyFavoritesWrapper> getMyRecQuestions(String languageObj)
    {
        map<String,String> labels = MvUtility.getCustomLabelMap(languageObj);
        map<String,String> labelsTransMap = new map<String,String>();
        String myFav = labels.get('MV_RecoveryQuestions');
        System.debug('MV_Favorites======='+myFav);
        if(!String.isBlank(myFav)) {
            for(String key : myFav.split('\\r\\n')){
                System.debug('kkkkkkkkk======='+key);
                labelsTransMap.put(key.split(':')[0],key.split(':')[1]);
            }
        }
        system.debug('labelsTest===='+labelsTransMap);
        List<mvMyFavoritesWrapper> optionMap = new List<mvMyFavoritesWrapper>();
        Schema.DescribeFieldResult fieldResult = Contact.MvMyRecoveryQuesions__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for(Schema.PicklistEntry f : ple){
            mvMyFavoritesWrapper myFavorite = new mvMyFavoritesWrapper(f.getLabel(),f.getValue());
            myFavorite.optionTranslatedLabel = labelsTransMap.get(f.getLabel());
            if(String.isNotBlank(myFavorite.optionTranslatedLabel) 
                && myFavorite.optionTranslatedLabel.contains(',')) {
                myFavorite.optionTranslatedLabel = myFavorite.optionTranslatedLabel.replace(',', '');
            }            
            optionMap.add(myFavorite);
        } 
        
            return optionMap;
            //String myFavoriteStr = contactList[0].MvMyFavorites__c.split();

    }    
    /* 
     * Reset first time password and security question
     */
    //STRY0569414 removed resetPassword mrthod

    @AuraEnabled
    public static List<String> getDynamicPicklistOptions(String fieldName)
    {
        List<String> pickListValues = new List<String>();
        Schema.SObjectType targetType = Schema.getGlobalDescribe().get('Contact');
        Schema.DescribeSObjectResult sobjResult = targetType.getDescribe();
        Map<String, Schema.SObjectField> fieldMap = sobjResult.fields.getMap();
        Schema.DescribeFieldResult descField = fieldMap.get(fieldName).getDescribe();
        
        List<Schema.PicklistEntry> ple = descField.getPicklistValues();
        for(Schema.PicklistEntry f : ple)
        {
            pickListValues.add(f.getLabel());
        } 
        return pickListValues;
    }
    
    /* 
     * Reset first time password, security question, verify contact info and marketing communication info
     */
    @AuraEnabled
    public static Boolean saveMarCommData(Boolean emailOptIn, Boolean emailOptOut) {
        try {
            User usr;
            if(Test.isRunningTest()) {
                usr = [SELECT Id, Name, ContactId, Contact.Salutation, 
                            Contact.FirstName, Contact.LastName FROM User WHERE ContactId !=null LIMIT 1];                
            } else {
                usr = [SELECT Id, Name, ContactId, Contact.Salutation, 
                            Contact.FirstName, Contact.LastName FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
            }

            if(usr.ContactId !=null){
                Contact con = new Contact(Id=usr.ContactId);
                if(emailOptIn) {
                    con.Email_Opt_in__c = true;
                    con.HasOptedOutOfEmail = false;
                    
                    con.Opted_In_By__c = usr.Contact.Salutation + ' '+ usr.Contact.FirstName +' '+ usr.Contact.LastName;
                    con.Opt_In_Date__c = System.today();
                    con.How_Opted_In__c = 'MyVarian';
                }
                
                if(emailOptOut) {
                    con.Email_Opt_in__c = false;
                    con.HasOptedOutOfEmail = true;
                    
                    con.Opted_In_By__c = '';
                    con.Opt_In_Date__c = null;
                    con.How_Opted_In__c = '';
                }

                update con;
                return true;
            } else {
               return false; 
            }
        } catch(Exception ex){
            return false;
            //return ex.getMessage();
        }
    }
}
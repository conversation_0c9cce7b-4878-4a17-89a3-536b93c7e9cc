public class createVisitRequestPDFCtrl {
    
    public Event ev{get;set;} 
    public string idVal{get;set;} 
    public string doSave{get;set;}
    public String requestedDate{get;set;}
    public String docCreatedDate{get;set;}
    public String startTime{get;set;}
    public String endTime{get;set;}
    public String eventowner{get;set;}
    public String hostName{get;set;}
    public String visitType{get;set;}
    public List<String> contactList{get;set;}
    public List<String> userList{get;set;}
    public List<String> allUserList{get;set;}
    public List<String> qaOptions{get;set;}
    
    public createVisitRequestPDFCtrl(ApexPages.StandardController stdController){
        
        idVal = stdController.getId(); 
       
        doSave = ApexPages.currentPage().getParameters().get('doSave'); 
        ev = [Select ID, Subject,Reference_Site_Account__r.Name,Opportunity__r.Name,Reference_Site_Street__c,Street__c,Reference_Site_City__c,City__c,Reference_Site_State_Province__c,State_Province__c,Reference_Site_Zip_Postal_Code__c,Zip_Postal_Code__c,
              Reference_Site_Request__c,Date_Requested__c,Key_Items_to_be_addressed__c,Equipment_Installed__c,Request_Q_A_Time__c,Special_Request__c,Contacts__c,Varian_Attendees__c,
              Request_Status__c,Request_Generated_Date__c,StartDateTime,EndDateTime,Whatid,What.Name,CreatedById From Event where ID = :idVal]; 
        if(ev != null) {
        //STRY0160077: Dipali: Show time in reference site timezone
            List<Account> lstAccts = [select id, Timezone__c,Timezone__r.Salesforce_timezone__c from Account where id =: ev.Reference_Site_Account__c];
            if(lstAccts[0].Timezone__c != null){
                String tm = lstAccts[0].Timezone__r.Salesforce_timezone__c.substring(lstAccts[0].Timezone__r.Salesforce_timezone__c.indexof('(') + 1, lstAccts[0].Timezone__r.Salesforce_timezone__c.indexof(')'));
                requestedDate = ev.StartDateTime.format('MMMMM dd, yyyy',tm);
                startTime =ev.StartDateTime.format('hh:mm a',tm);
                endTime =ev.EndDateTime.format('hh:mm a',tm);
                docCreatedDate = Datetime.now().format('MMMMM dd, yyyy',tm);
             }
            if(ev.Contacts__c != null)
                //contactList = getListToShow(ev.Contacts__c.split(','));
                contactList = getListToShow(ev.Contacts__c.split(','),'Contact');
            if(ev.Varian_Attendees__c != null){
                //allUserList = getListToShow(ev.Varian_Attendees__c.split(','));
                allUserList = ev.Varian_Attendees__c.split(',');
            }
                
            if(allUserList != null && allUserList.size() > 0){
                List<String> users = new List<String>(); 
                for(Integer i=0; i< allUserList.size(); i++){
                    if(i == 0)hostName = getHostNameToShow(allUserList[0]);
                    else {users.add(allUserList[i]);  userList = getListToShow(users,'User');}
                }
             }
            if(ev.Request_Q_A_Time__c != null)
            qaOptions = ev.Request_Q_A_Time__c.split(';');
            eventowner = ev.CreatedById;
            visitType = ev.Reference_Site_Request__c;
        }
    } 
    
   public pageReference savePDF(){
        if(doSave == 'No'){ 
            return null; 
        }
       
        //STRY0217870 - Added one more condition in where clause to retrieve only Reference record.        
        List<Attachment> existingDocs = [select id,ParentID from Attachment where ParentID =:idVal AND Description = 'Form submitted for visiting reference site'];
        if(existingDocs != null && existingDocs.size() > 0){
            delete existingDocs;
        }
        PageReference pagePdf = new PageReference('/apex/createVisitRequestFormPDF'); 
        pagePdf.getParameters().put('id', idVal); 
        pagePdf.getParameters().put('doSave', 'No'); 
        Blob pdfPageBlob; 
        if(Test.isRunningTest()) {
            pdfPageBlob = blob.valueOf('Unit.Test');
        }
        else{
            pdfPageBlob = pagePdf.getContent(); 
        }
        Attachment a = new Attachment(); 
        a.Body = pdfPageBlob; 
        a.ParentID = idVal; 
        a.Name = visitType.replace(' ', '_')+'_'+requestedDate+'.doc'; 
        a.Description = 'Form submitted for visiting reference site'; 
        a.OwnerId = eventowner;
        insert a; 
        
        return new PageReference('/' + idVal); 
    } 
    
    //STRY0122712: Dipali: Unity - Tours of Excellence Requests for Future Release : Start
    private List<String> getListToShow(List<String> recordList, String obj){
        Set<Id> idList = new Set<Id>();
        List<Sobject> records = new List<Sobject>();
        List<String> recordsToShow = new List<String>();
         if(recordList != null && recordList.size() > 0)
            {
             for(String s : recordList){ idList.add(s.substringBetween('(', ')'));}
             String query = 'select id,Name,Title,MobilePhone from '+obj+' where id in:idList';
             List<Sobject> recordList1 = Database.query(query);
             for(Sobject o: recordList1){
                  records.add(o); 
              }
             List<Sobject> lst = [select id, Contact__r.Name, Contact__r.Title from Contact_Role_Association__C where id in: idList];
             records.addall(lst);   
            }
        if(records != null && records.size() > 0){
            for(sObject var1 : records){
            String value = '';
                if(obj == 'Contact') {
                    //Sobject var =  var1;
                    //STSK0023587: Tours of Excellence: Changes after acquisition 
                    if(((String)var1.Id).substring(0,3) =='a2H') {
                        Contact_Role_Association__c var = (Contact_Role_Association__c) var1;
                        value += var.Contact__r.Name;
                        if(var.Contact__r.Title != null ){String t = var.Contact__r.Title.replace(',',' ');
                        value += ' - '+t;
                       }
                    }
                    else{
                        contact var = (contact) var1;
                         value += var.Name;
                         if(var.Title != null ){String t = var.Title.replace(',',' ');
                         value += ' - '+t;
                        }
                    }
                   
                }
                else {
                    User var = (User) var1;
                    value += var.Name;
                    if(var.Title != null ){String t = var.Title.replace(',',' ');
                        value += ' - '+t;
                    }
                }
                recordsToShow.add(value);
             }
        }
        return recordsToShow;
    }
    private String getHostNameToShow(String userstring){
        String value= '';
        String ids = userstring.substringBetween('(', ')');
        List<User> u = [select id,Name,Title,MobilePhone from User where id =: ids];
        if(u != null && u.size() > 0)
        {
            value += u[0].Name;
            if(u[0].Title != null ){String t = u[0].Title.replace(',',' ');
                                    value += ' - '+t;
                                   }
            if(u[0].MobilePhone != null){value += ' - '+u[0].MobilePhone;}
            return value;
        }
       return null;
    }
  //STRY0122712: Dipali: Unity - Tours of Excellence Requests for Future Release : End
}
@isTest
public class HCT_CsProductTriggerTest {

      public static testmethod void DeleteProductTest(){
        Account acct = VFSL_TestDataUtility.createAccount('TestAccount 22');
        insert acct;     
        Contact cont = VFSL_TestDataUtility.createContact(acct.Id);
        insert cont; 
        Case c = new Case();
        c.RecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('CaseSupport').getRecordTypeId();
        c.AccountId = acct.id;
        c.ContactId = cont.Id;
        c.Status = 'New';
        c.Priority = 'Medium';
        c.Product_Type__c = 'Cryoablation';  
        c.Target_organ__c ='Endocrine';  
        c.Type_of_support__c = 'Live';
        c.MSP__c ='No';
        insert c;
        HCT_Inventory__c iv = new HCT_Inventory__c();
        iv.Storage_Location__c = 'S100';
        iv.Stock_Owner__c = 'S100 Test User';
        iv.Stock_Owner_Email__c = '<EMAIL>';
        iv.Unrestricted_Quantity__c = 10;
        iv.Material__c = 'CRYO-204-V';
        iv.Lot__c = 'test-1';
        insert iv;
        ERP_Pcode__c pcode = VFSL_TestDataUtility.getERPPCode();
        insert pcode;
        Product2 prod = VFSL_TestDataUtility.getProduct(pcode.Id);
        prod.Name = 'TEST PART 1';
        prod.ProductCode = 'CRYO-204-V';
        insert prod;
        Test.setMock(HttpCalloutMock.class, new vMC_OrderTriggerHelper_MockResponse());  
        Hand_Carry_Ticket__c hct = new Hand_Carry_Ticket__c();
        hct.Facility_Name__c = acct.Id;
        hct.Physician_Name__c = cont.Id;
        hct.Case__c = c.id;
        insert hct;
        List<FM_Products__c> prds = new List<FM_Products__c>();
        FM_Products__c prd = new FM_Products__c();
        prd.Product__c = prod.Id;
        prd.Quantity__c = 1;
        prd.Lot__c = 'test-1';
        prd.Each__c = 10;
        prd.Facility_Charge_Code__c = 'S100';
        prd.Hand_Carry_Ticket__c = hct.id;
        prds.add(prd);
        FM_Products__c prd1 = new FM_Products__c();
        prd1.Product__c = prod.Id;
        prd1.Quantity__c = 1;
        prd1.Lot__c = 'test-1';
        prd1.Each__c = 10;
        prd1.Facility_Charge_Code__c = 'S100';
        prd1.Hand_Carry_Ticket__c = hct.id; 
        prds.add(prd1);
        insert prds;
        delete prds[0];
        hct.status__C = 'Closed';
        delete prds[1];
      }
}
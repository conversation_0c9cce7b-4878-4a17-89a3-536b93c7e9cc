@isTest
public class VFSL_AsyncWOUtilityTest {
    
    //variable declarations
    public static Id fieldServcRecTypeID = Schema.SObjectType.WorkOrder.getRecordTypeInfosByDeveloperName().get('Field_Service').getRecordTypeId();//record type id of Field Service WO
    public static Id instlRecTypeID = Schema.SObjectType.WorkOrder.getRecordTypeInfosByDeveloperName().get('Installation').getRecordTypeId(); //record type id of Installation WO
    public static Id UsageConRecTypID = Schema.SObjectType.WorkOrderLineItem.getRecordTypeInfosByDeveloperName().get('FS_Usage').getRecordTypeId();
    
   @testSetup static void setup(){
		Account acct = VFSL_TestDataUtility.createAccount('Test Account');
        insert acct;
        Contact cont = VFSL_TestDataUtility.createContact(acct.Id);
        insert cont;
        ServiceTerritory st1 = VFSL_TestDataUtility.createServiceTerritory('Test Territory 1', 'TES1','Resource_Territory');
        st1.Dispatch_Queue__c='DISP NA';
        insert st1;
        ServiceResource sr1 = VFSL_TestDataUtility.createServiceResource('TestResource1',st1,'Technician');
        insert sr1;
        Schema.Location loc = VFSL_TestDataUtility.createLocationWithArguments(acct,'Test Location');
        loc.LocationType = 'Customer';
        loc.Active__c = true;
        loc.Service_Territory_Code__c = 'TEST';
        insert loc;
        Asset a1 = VFSL_TestDataUtility.createAssetWithArguments(acct,cont,loc,sr1,st1,'Test Asset');
        insert a1;
        User thisUser = [ select Id from User where Id =:UserInfo.getUserId() ];
        Test.startTest();    
        Case ca = VFSL_TestDataUtility.createCaseWithArguments(acct, cont, loc, a1, sr1, st1);
        ca.OwnerId = thisUser.Id;   
        insert ca;
        WorkOrder wo = VFSL_TestDataUtility.createWorkOrderWithArguments(acct,a1,loc,ca,sr1,st1,'Field_Service');
        wo.Subject = 'AsyncTest';
        wo.Sales_Org__c = '0600';
        wo.Priority = 'Low';
        wo.Status = 'New';
        wo.Dispatch_Status__c = 'New';
        wo.StartDate = System.now().addDays(1);
        wo.EndDate = System.now().addDays(2);
        wo.Preferred_Service_Territory__c = st1.Id;
        insert wo;
        Test.stopTest();
    }
    public static testMethod void testcreateFollowUpWO(){
        List <WorkOrder> wo = [Select id from WorkOrder WHERE Subject = 'AsyncTest' ];
        Set<Id> woids = new Set<Id>();
        for(WorkOrder l1: wo){
          woids.add(l1.id);  
        }

        Test.startTest();
        VFSL_AsyncWOUtility.createFollowUpWO(woids);
        Test.stopTest();
    }
    /*Added by SHital  -- Not used currently
    public static testMethod void testCreateTransferOrder(){
        List <WorkOrder> wo = [Select id from WorkOrder where Dispatch_Status__c ='New'];
        Set<Id> woids = new Set<Id>();
        for(WorkOrder l1: wo){
          woids.add(l1.id);  
        }
        ServiceResource sr1 = [Select id,name,RelatedRecordId from ServiceResource where name = 'TestResource1' limit 1];
        ServiceTerritory st1 = [Select id,name from ServiceTerritory where name = 'Test Territory 1' limit 1];
        Account acct1 = VFSL_TestDataUtility.createAccount('test create Acc temp');
        insert acct1;
        
        ProductRequest prodReq = VFSL_TestDataUtility.createProductRequestWithArguments(sr1,st1);
		prodReq.AccountId = acct1.Id;
		prodReq.Status = 'Completed';
        prodReq.WorkOrderId = wo[0].Id;
        prodReq.Service_Territory__c = st1.Id;
        insert prodReq;
        
        Product2 prod = new Product2( Name = 'Test Product',IsActive = true,CurrencyIsoCode = 'GBP', Material_Group__c = 'H:TRNING');
        insert prod;
		
        ProductRequest prTemp = [Select id,Status,Service_Territory__c,AccountId,Line_Count__c,Lines_with_Remaining_Qty__c,(Select ParentId,Applied_Qty__c,Remaining_Qty__c from ProductRequestLineItems) from ProductRequest where WorkOrderId IN:woids];
       
        ProductRequestLineItem varPRLI = new ProductRequestLineItem();
        varPRLI.Product2Id = prod.id;
        varPRLI.ParentId = prodReq.id;
        varPRLI.QuantityRequested = 1;
        varPRLI.Delivered_Qty__c = 1;
        varPRLI.Status = 'Open';
        insert varPRLI;
        
        Test.startTest();
        VFSL_AsyncWOUtility.createTransferOrder(woids);
        Test.stopTest();
    }*/
}
@isTest
public class CDLTriggerHandlerTest {
    
    public static List<ContentVersion> listContent = new List<ContentVersion>();
    public static Schema.Location ecfLoc;
    public static Asset assetObj;
    public static WorkOrder woFS;

    @TestSetup
    static void createTestData(){
        
        //Added by <PERSON><PERSON><PERSON> - Test Error caused from VFSL_ECF
        Account testAccount = VFSL_TestDataUtility.createAccount('Lorem Ipsum');
        testAccount.ShippingCity = 'City';
        testAccount.ShippingCountry = 'Country';
        testAccount.ShippingState = 'State';
        insert testAccount;
        
        Case testCase = new Case();
        testCase.AccountId = testAccount.Id;
        testCase.Subject = 'Test';
        testCase.Priority = 'Low';
        insert testCase;
    
        //Escalated Complaint Form
        L2848__c ecf = new L2848__c();
        //VFSL_ECF dereferencing a null pointer error fix
        ecf.Case__c = testCase.Id;
        ecf.retained_part_CTS__c = 'No';
        insert ecf;
        
        ContentVersion objCon = new ContentVersion();
        objCon.Title = 'Test';
        objCon.PathOnClient = 'test';
        objCon.File_Type__c = 'application/pdf';
        Blob bodyBlob=Blob.valueOf('Unit Test Attachment Body');
        objCon.Region__c='china'; //Added by Trupti
        objCon.Territory__c='APAC'; //Added by Trupti
        objCon.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 1');
        listContent.add(objCon);
        
        ContentVersion objCon2 = new ContentVersion();
        objCon2.Title = 'Test signed';
        objCon2.PathOnClient = 'test';
        objCon2.File_Type__c = 'application/pdf';
        objCon2.Region__c='North America';
        Blob bodyBlob2=Blob.valueOf('Unit Test Attachment Body 2');
        objCon2.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 2');
        listContent.add(objCon2);
        
        ContentVersion objCon3 = new ContentVersion();
        objCon3.Title = 'Test-Q-signed.pdf';
        objCon3.PathOnClient = 'test';
        objCon3.File_Type__c = 'application/pdf';
        objCon3.Region__c='North America';
        Blob bodyBlob3 =Blob.valueOf('Unit Test Attachment Body 3');
        objCon3.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 3');
        listContent.add(objCon3);
        
        insert listContent;
       
    }
    
    public static testmethod void deleteData(){
        
        ContentVersion objCon = new ContentVersion();
        objCon.Title = 'Test';
        objCon.PathOnClient = 'test';
        objCon.File_Type__c = 'application/pdf';
        Blob bodyBlob=Blob.valueOf('Unit Test Attachment Body');
        objCon.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 1');
        listContent.add(objCon);
        
        ContentVersion objCon2 = new ContentVersion();
        objCon2.Title = 'Test signed';
        objCon2.PathOnClient = 'test';
        objCon2.File_Type__c = 'application/pdf';
        Blob bodyBlob2=Blob.valueOf('Unit Test Attachment Body 2');
        objCon2.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 2');
        listContent.add(objCon2);
        
        ContentVersion objCon3 = new ContentVersion();
        objCon3.Title = 'Test-Q-signed.pdf';
        objCon3.PathOnClient = 'test';
        objCon3.File_Type__c = 'application/pdf';
        Blob bodyBlob3=Blob.valueOf('Unit Test Attachment Body 3');
        objCon3.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 3');
        listContent.add(objCon3);
        
        
        insert listContent;
        
        Training_Course__c tcObj = new Training_Course__c();
        insert tcObj;
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
        ContentDocumentLink objCDL1 = new ContentDocumentLink();
        objCDL1.ContentDocumentId = cdId1;
        objCDL1.LinkedEntityId = tcObj.Id;
        objCDL1.ShareType = 'V';
        objCDL1.Visibility = 'AllUsers'; 
        insert objCDL1;
            
        delete objCDL1;
        Test.stopTest();
       
    }
    
    //ContentDocumentCountTrigger Test
    public static testmethod void testAttachementisCreated(){
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
        L2848__c ecf = [Select Id from L2848__c LIMIT 1];
        

            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = ecf.Id;
            objCDL1.ContentDocumentId = cdId1;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'AllUsers'; 
            insert objCDL1;
            
            objCDL1.Visibility = 'InternalUsers';
            update objCDL1;
            
            List<Attachment> attachementList = [Select id from Attachment where parentId =: ecf.Id];
        
            System.assertEquals(1, attachementList.size(), 'Attachment is  created');
        
        Test.stopTest();
        
    }
    
    public static testmethod void testUpdateVersionForAgmnt(){
        
        Test.startTest();
        Apttus__APTS_Agreement__c agmnt = new Apttus__APTS_Agreement__c();
        agmnt.Name = 'Test agmnt';
        agmnt.Apttus__Status__c = 'Activated';
        agmnt.Super_Territory__c = 'Americas';
        agmnt.Territory__c = 'ANZ';
        Insert agmnt;
        
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument where title ='Test' LIMIT 2];
        string cdId1 = documents[0].Id;
        
        ContentDocumentLink objCDL1 = new ContentDocumentLink();
        objCDL1.LinkedEntityId = agmnt.Id;
        objCDL1.ContentDocumentId = cdId1;
        objCDL1.ShareType = 'V';
        objCDL1.Visibility = 'AllUsers'; 
        insert objCDL1;
        
          ContentVersion objCon = new ContentVersion();
            objCon.Title = 'Test';
            objCon.PathOnClient = 'test';
            objCon.File_Type__c = 'application/pdf';
            Blob bodyBlob=Blob.valueOf('Unit Test Attachment Body');
            objCon.Region__c='china';
            objCon.Territory__c='APAC';
            objCon.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 1');
            //objCon.ContentDocumentId = objCDL1.Id;
            insert objCon;
            
            
            ContentVersion cv = [SELECT id,ContentDocumentId from ContentVersion where id =: objCon.Id LIMIT 1];

          CDLTriggerHandler.agmntCDLList = null;
        
          ContentDocumentLink objCDL2 = new ContentDocumentLink();
            objCDL2.LinkedEntityId = agmnt.Id;
            objCDL2.ContentDocumentId = cv.ContentDocumentId;
            objCDL2.ShareType = 'V';
            objCDL2.Visibility = 'AllUsers'; 
            insert objCDL2;
        
        Test.stopTest();
        
    }
    
    public static testmethod void testUpdateVersionForAgmntPdf(){
        
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument where title ='Test-Q-signed.pdf' LIMIT 2];
        string cdId1 = documents[0].Id;
        User thisUser = VFSL_TestDataUtility.createUser('Test System Admin User');
        insert thisUser;
        
        ContentDocumentLink objCDL1 = new ContentDocumentLink();
        objCDL1.LinkedEntityId = thisUser.Id;
        objCDL1.ContentDocumentId = cdId1;
        objCDL1.ShareType = 'V';
        objCDL1.Visibility = 'AllUsers'; 
        insert objCDL1;
        
        ContentVersion objCon = new ContentVersion();
        objCon.Title = 'Test-Q-Test.pdf';
        objCon.PathOnClient = 'test';
        objCon.File_Type__c = 'application/pdf';
        Blob bodyBlob=Blob.valueOf('Unit Test Attachment Body');
        objCon.Region__c='china';
        objCon.Territory__c='APAC';
        objCon.versiondata=EncodingUtil.base64Decode('Unit Test Attachment Body 3');
        //objCon.ContentDocumentId = objCDL1.Id;
        insert objCon;
        
        
        ContentVersion cv = [SELECT id,ContentDocumentId from ContentVersion where id =: objCon.Id LIMIT 1];
        
        CDLTriggerHandler.saasCDLList = null;
        
        ContentDocumentLink objCDL2 = new ContentDocumentLink();
        objCDL2.LinkedEntityId = thisUser.Id;
        objCDL2.ContentDocumentId = cv.ContentDocumentId;
        objCDL2.ShareType = 'V';
        objCDL2.Visibility = 'AllUsers'; 
        insert objCDL2;
        
        Test.stopTest();
        
    }
    
    
    //ContentDocumentCountTrigger Test
    public static testmethod void testRegionUpdateFromAgreement(){
        
        Test.startTest();
        Apttus__APTS_Agreement__c agmnt = new Apttus__APTS_Agreement__c();
        agmnt.Name = 'Test agmnt';
        agmnt.Apttus__Status__c = 'Activated';
        agmnt.Super_Territory__c = 'Americas';
        agmnt.Territory__c = 'ANZ';
        Insert agmnt;
        
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = agmnt.Id;
            objCDL1.ContentDocumentId = cdId1;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'AllUsers'; 
            insert objCDL1;
            
            objCDL1.Visibility = 'InternalUsers';
            update objCDL1;
        
            List<ContentVersion> linkObj = [SELECT Id, Quote_Region__c, ContentDocumentId
                                            FROM ContentVersion 
                                            WHERE ContentDocumentId =: cdId1
                                            AND IsLatest = true];
            System.assertNotEquals(0, linkObj.size(), 'Region is not matching with parent agreement region');
       
        Test.stopTest();
        
    }
    
    public static testmethod void testBeforeInsert(){
        
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
        Training_Course__c tcObj = new Training_Course__c();
        insert tcObj;
        
            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = tcObj.Id;
            objCDL1.ContentDocumentId = cdId1;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'InternalUsers'; 
            insert objCDL1;
            
            ContentDocumentLink objCDL2 = [Select id, Visibility from ContentDocumentLink where id =: objCDL1.Id];
            system.assertEquals('AllUsers', objCDL2.Visibility, 'Visibility is not correct on Content Document Link for training course');
            
        Test.stopTest();
        
    }
    
    public static testmethod void testSendEmailNotificationonAgmnt(){
        Account accountObj = New Account();
        accountObj.Name = 'testAccount';
        if(accountObj.Bill_To__c != null)
        accountObj.Bill_To__r.BillingCity = 'Anytown';
        accountObj.BillingCity = 'Anytown';
        accountObj.Site_Type__c = 'HOSPITAL';
        accountObj.State_Province_Is_Not_Applicable__c=true;
        accountObj.Country__c = 'Algeria';
        insert accountObj;
    
        /*Contact contactObj = New Contact();
        contactObj.LastName = 'Bond';
        contactObj.FirstName = 'James';
        contactObj.Email = '<EMAIL>';
        contactObj.Functional_Role__c = 'Consultant';
        contactObj.AccountId = accountObj.Id;
        insert contactObj;*/
        
        Negotiation_Request__c negReq = new Negotiation_Request__c();
        negReq.Quarter_Close__c = 'Q1';
        negReq.Part_of_Bid_or_RFP__c = 'No';
        negReq.Strategic_National_Account__c = 'No';
        negReq.Previous_T_C_s_with_Customer__c = 'No';
        negReq.Presented_Master__c = 'No';
        //negReq.Customer_Contact_Name__c = contactObj.Id;
        //negReq.Sales_Person__c = userId;
        negReq.Legal_Purchasing_Entity_Name__c = accountObj.Id;
        negReq.Site_Name__c = accountObj.Id;
        negReq.Extra_Participants_Emails__c = 'Rahul Agarwal,Test User';
        negReq.Agreement_Types__c = 'BAA';
        Insert negReq;
        
        Apttus__APTS_Agreement__c agmnt = new Apttus__APTS_Agreement__c();
        agmnt.Name = 'Test agmnt';
        agmnt.Apttus__Status__c = 'Activated';
        agmnt.Super_Territory__c = 'Americas';
        agmnt.Territory__c = 'ANZ';
        agmnt.Negotiation_Request__c = negReq.Id;
        Insert agmnt;
        
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = agmnt.Id;
            objCDL1.ContentDocumentId = cdId1;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'AllUsers'; 
            insert objCDL1;
            
            objCDL1.Visibility = 'InternalUsers';
            update objCDL1;
        
            /*List<ContentVersion> linkObj = [SELECT Id, Quote_Region__c, ContentDocumentId
                                            FROM ContentVersion 
                                            WHERE ContentDocumentId =: cdId1
                                            AND IsLatest = true];
            System.assertNotEquals(0, linkObj.size(), 'Region is not matching with parent agreement region');*/
       
        Test.stopTest();
        
    }
    
    public static testmethod void testSendEmailNotification(){
        
        Account accountObj = New Account();
        accountObj.Name = 'testAccount';
        if(accountObj.Bill_To__c != null)
        accountObj.Bill_To__r.BillingCity = 'Anytown';
        accountObj.BillingCity = 'Anytown';
        accountObj.Site_Type__c = 'HOSPITAL';
        accountObj.State_Province_Is_Not_Applicable__c=true;
        accountObj.Country__c = 'Algeria';
        insert accountObj;
    
        /*Contact contactObj = New Contact();
        contactObj.LastName = 'Bond';
        contactObj.FirstName = 'James';
        contactObj.Email = '<EMAIL>';
        contactObj.Functional_Role__c = 'Consultant';
        contactObj.AccountId = accountObj.Id;
        insert contactObj;*/
        
        Negotiation_Request__c negReq = new Negotiation_Request__c();
        negReq.Quarter_Close__c = 'Q1';
        negReq.Part_of_Bid_or_RFP__c = 'No';
        negReq.Strategic_National_Account__c = 'No';
        negReq.Previous_T_C_s_with_Customer__c = 'No';
        negReq.Presented_Master__c = 'No';
        //negReq.Customer_Contact_Name__c = contactObj.Id;
        //negReq.Sales_Person__c = userId;
        negReq.Legal_Purchasing_Entity_Name__c = accountObj.Id;
        negReq.Site_Name__c = accountObj.Id;
        negReq.Extra_Participants_Emails__c = 'Rahul Agarwal,Test User';
        negReq.Agreement_Types__c = 'BAA';
        Insert negReq;
        
        Test.startTest();
        List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
        string cdId1 = documents[1].Id;
        
            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = negReq.Id;
            objCDL1.ContentDocumentId = cdId1;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'AllUsers'; 
            insert objCDL1;
            
            objCDL1.Visibility = 'InternalUsers';
            update objCDL1;
        
            List<ContentVersion> linkObj = [SELECT Id, Quote_Region__c, ContentDocumentId
                                            FROM ContentVersion 
                                            WHERE ContentDocumentId =: cdId1
                                            AND IsLatest = true];
            System.assertNotEquals(0, linkObj.size(), 'Region is not matching with parent agreement region');
       
        Test.stopTest();
        
    }
    /*public static testmethod void testInsertFS(){
        Test.startTest();
            woFS = createWO();
            insert woFS;
    
            //List<ContentDocument> documents = [SELECT Id, Title FROM ContentDocument LIMIT 2];
            //string cdId1 = documents[1].Id;
            ContentVersion content=new ContentVersion(); 
            content.Title='Header_Picture1'; 
            content.PathOnClient='/' + content.Title + '.jpg'; 
            Blob bodyBlob=Blob.valueOf('Unit Test ContentVersion Body'); 
            content.VersionData=bodyBlob; 
            //content.LinkedEntityId=sub.id;
            content.origin = 'H';
            insert content;
            
            ContentDocumentLink objCDL1 = new ContentDocumentLink();
            objCDL1.LinkedEntityId = woFS.Id;
            objCDL1.ContentDocumentId = content.contentdocumentid;
            objCDL1.ShareType = 'V';
            objCDL1.Visibility = 'AllUsers'; 
            insert objCDL1;
        
            //WorkOrder woObj = [Select id,Count_of_Attachments__c from WorkOrder where id = : woFS.Id];
        
            //System.assertNotEquals(0,woObj.Count_of_Attachments__c, 'Count is not updated on WO');
        
            delete objCDL1;
            
            //WorkOrder woObj1 = [Select id,Count_of_Attachments__c from WorkOrder where id = : woFS.Id];
        
            //System.assertEquals(0,woObj1.Count_of_Attachments__c, 'Count is not updated on WO');
        
        Test.stopTest();
    }*/
    
    private static WorkOrder createWO() {
        
        Account acc = VFSL_TestDataUtility.createAccount('Test');
        acc.ERP_Site_Partner_Code__c = 'SPC0001';
        acc.Has_Location__c = true;
        acc.Country__c = 'USA';
        insert acc;
        
        WorkOrder woRecord = new WorkOrder();
        
        Product2 prod = CPQ_TestDataUtility.createProduct('Test product', 100);
        insert prod;
        
        assetObj = VFSL_TestDataUtility.createAsset('HTEST99A'+'wo34'); 
        assetObj.Product2Id = prod.Id;
        assetObj.SerialNumber = '1234';
        insert assetObj;
            
        ecfLoc = VFSL_TestDataUtility.createLocation('My Location');
        insert ecfLoc;
        
        woRecord.Status = 'New';
        woRecord.Dispatch_Status__c = 'Assigned';
        woRecord.Priority = 'Low';
        woRecord.AssetId = assetObj.Id;
        woRecord.Direct_Assignment__c = false;
        woRecord.FSR_Language_Code__c = 'EN';
        woRecord.LocationId =ecfLoc.Id;
        woRecord.StartDate = DateTime.Now();
        woRecord.EndDate = DateTime.Now().AddDays(+1);
        woRecord.recordtypeid = Schema.SObjectType.WorkOrder.getRecordTypeInfosByDeveloperName().get('Field_Service').getRecordTypeId();
        woRecord.ECF_Required__c = 'Yes from Work Order';
 
        return woRecord;
    }
    
    @isTest 
    static void setWODocVisibleTest(){
        Id devRecordTypeId = VFSL_TestDataUtility.getRecordTypeId(WorkOrder.getSObjectType(),'Helpdesk');
        
        Test.startTest();
        //Rohan Shisode - DUPLICATES DETECTED , Account Duplicate Error Fix
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        /*
        Account testAccount = VFSL_TestDataUtility.createAccount('Test Account');
        insert testAccount;
    */

        Case testCase = new Case(
            Subject = 'Test Subject',
            AccountId = testAccount.Id,
            Priority = 'High'
        );
        insert testCase;
		//Test.startTest();
        Asset testAsset = VFSL_TestDataUtility.createAsset('HTEST99A'+'wo5');
        Product2 testProduct = new Product2(
            Name = 'ARIA',
            Productcode = '***********',
            Product_Type__c = 'Model'
        );
        insert testProduct;
        testAsset.Product2Id = testProduct.Id;
        insert testAsset;

        WorkOrder testWorkOrder = new WorkOrder(
            CaseId = testCase.Id,
            Status = 'Assigned',
            Dispatch_Status__c = 'Accepted',
            AssetId = testAsset.Id,
            Direct_Assignment__c = false,
            FSR_Language_Code__c = 'EN'
        );
        testWorkOrder.RecordTypeId = devRecordTypeId;
        insert testWorkOrder;

        ContentVersion testContentVersion = new ContentVersion();
        testContentVersion.Title = 'Test Work Order Attachment WO';
        testContentVersion.PathOnClient = 'TestWOAttachment.pdf';
        testContentVersion.File_Type__c = 'application/pdf';
        testContentVersion.Region__c = 'North America';
        testContentVersion.VersionData = EncodingUtil.base64Decode('Unit Test Attachment');
        insert testContentVersion;

        ContentVersion insertContentVersion = [SELECT Id,ContentDocumentId FROM ContentVersion WHERE Id =: testContentVersion.Id];

        ContentDocumentLink testContentDocumentLink = new ContentDocumentLink();
        testContentDocumentLink.ContentDocumentId = insertContentVersion.ContentDocumentId;
        testContentDocumentLink.LinkedEntityId = testWorkOrder.Id;
        testContentDocumentLink.ShareType = 'V';
        testContentDocumentLink.Visibility = 'AllUsers';
        insert testContentDocumentLink;  
        Test.stopTest();
    }    
            
    //STRY0207522 - Customer Master Data Enhancements - Rohan Shisode
    @isTest 
    static void testCIFAttachment(){
        Test.startTest();
        Country__c countryObj = new Country__c();
        countryObj.Name = 'India';
        countryObj.Region__c = 'EMEA';
        countryObj.ISO_Code__c = 'India - IN';

        //Rohan Shisode - Duplicate Matching Rule Error
        Account testAccount = [SELECT Id FROM Account LIMIT 1];

        Customer_Information_Form__c testCIF = new Customer_Information_Form__c();
        testCIF.Billing_Contact_Email__c = '<EMAIL>';
        testCIF.Billing_Contact_Name__c = 'Test Contact';
        testCIF.Account_Name__c = testAccount.Id;
        testCIF.Type__c = 'Sold To';
        testCIF.Status__c = 'Pending';
        testCIF.Assignee__c = '<EMAIL>';
        insert testCIF;

        ContentVersion testContentVersion = new ContentVersion();
        testContentVersion.Title = 'Test CIF Attachment';
        testContentVersion.PathOnClient = 'TestCIFAttachment.pdf';
        testContentVersion.File_Type__c = 'application/pdf';
        testContentVersion.Region__c = 'North America';
        testContentVersion.VersionData = EncodingUtil.base64Decode('Unit Test Attachment');
        insert testContentVersion;

        ContentVersion insertContentVersion = [SELECT Id,ContentDocumentId FROM ContentVersion WHERE Id =: testContentVersion.Id];

        ContentDocumentLink testContentDocumentLink = new ContentDocumentLink();
        testContentDocumentLink.ContentDocumentId = insertContentVersion.ContentDocumentId;
        testContentDocumentLink.LinkedEntityId = testCIF.Id;
        testContentDocumentLink.ShareType = 'V';
        testContentDocumentLink.Visibility = 'AllUsers';
        insert testContentDocumentLink;
        Test.stopTest();
    }  
}
/**
 *  Class Name: InstallAssignmentTriggerHandler  
 *  Description: This is a Trigger handler for SObject Install_Assignment__c. 
 *  Company: Varian
 *  CreatedDate:1/14/2021 
 *
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON>          1/14/2021             Original version
 *  <PERSON>          5/20/2024             STRY0228820:  Manage Events for Install Assignment.          
 */

public class InstallAssignmentTriggerHandler {
   
    private static Map<String, Integer> loopCountMap = new Map<String, Integer>();
    public static Boolean loopCount = true;
    private boolean m_isExecuting = false;
    private integer BatchSize = 0;

    // Constructor
    public InstallAssignmentTriggerHandler(boolean isExecuting, integer size){
        m_isExecuting = isExecuting;
        BatchSize = size;
    }
    
    //set default loop count for before and after update 
    public void setLoopCount(Integer beforeUpdate, Integer afterUpdate){
        loopCount = false; 
        loopCountMap.put('beforeUpdate',beforeUpdate);
        loopCountMap.put('afterUpdate',afterUpdate); 
    }

    // On Before Insert 
    public void OnBeforeInsert(list<Install_Assignment__c> newIA){ 
        InstallAssignmentActions.defaultFill(newIA);
    } 
 
   // On After Insert  
    public void OnAfterInsert(list<Install_Assignment__c> newIA){
        //STRY0228820:  Manage Events for Install Assignment. 
        InstallAssignmentActions.createEvents(newIA);
    }

    // On Before Update     
    public void OnBeforeUpdate(List<Install_Assignment__c> newIA, 
                               List<Install_Assignment__c> oldIA, 
                               Map<Id,Install_Assignment__c> newIAMap, 
                               Map<Id, Install_Assignment__c> oldIAMap){
        Integer loopCount = loopCountMap.get('beforeUpdate');
        if(loopCount > 0){
           loopCountMap.put('beforeUpdate',loopCount-1);
           List<Install_Assignment__c> listIA = new List<Install_Assignment__c>();
           Map<Id,Id> mapOI2Ten = new Map<Id,Id>();
           Set<Id> setIA = new Set<Id>(); //collect id of initiating IA
           //Update IA when SR changes
            for(Install_Assignment__c ia: newIAMap.values()){
                if(ia.ServiceResource__c != oldIAMap.get(ia.Id).ServiceResource__c){
                   listIA.add(ia); 
                } 
                if(ia.AcceptanceDate__c != null && oldIAMap.get(ia.Id).AcceptanceDate__c == null){
                    ia.Status__c = 'Installed';
                }
                if(ia.Tenant__c != null && ia.Tenant__c != oldIAMap.get(ia.Id).Tenant__c){
                    mapOI2Ten.put(ia.OrderItem__c,ia.Tenant__c);
                    setIA.add(ia.Id);
                }
            }
            if(listIA.size()>0){ 
                InstallAssignmentActions.updateSR(listIA,new Set<Id>());
            }
            if(!mapOI2Ten.isEmpty()){
                InstallAssignmentActions.oiTenantUpdate(mapOI2Ten,setIA);
            }
        }
    }
 
    // On After Update
    public void OnAfterUpdate(List<Install_Assignment__c> newIA, 
                              List<Install_Assignment__c> oldIA, 
                              Map<Id, Install_Assignment__c> newIAMap, 
                              Map<Id,Install_Assignment__c> oldIAMap){
        
        Integer loopCount = loopCountMap.get('afterUpdate');
        
        if(loopCount > 0){
            loopCountMap.put('beforeUpdate',loopCount-1);
            List<Install_Assignment__c> listIA = new List<Install_Assignment__c>();
            for(Install_Assignment__c ia: newIAMap.values()){
                if(ia.AcceptanceDate__c != null && oldIAMap.get(ia.Id).AcceptanceDate__c == null){
                    listIA.add(ia);
                }
            }
            if(listIA.size()>0){
                InstallAssignmentActions.updateOIAcceptanceDate(listIA);
            }
            //STRY0228820:  Manage Events for Install Assignment. 
            InstallAssignmentActions.updateEvents(oldIAMap,newIAMap);
        }
    }

    // On Before Delete 
    public void OnBeforeDelete(Map<Id, Install_Assignment__c> oldIAMap){}

    // On After Delete 
    public void OnAfterDelete(Map<Id, Install_Assignment__c> oldIAMap){}

    // On After Undelete
    public void OnAfterUndelete(Map<Id, Install_Assignment__c> oldIAMap){}

}
@isTest
public class CPQ_QuoteLineItemTriggerHelperTest { 
    @TestSetup
    static void createTestData(){
        blng__BillingRule__c thisBR = CPQ_TestDataUtility.createBillingRule('Test BR');
        insert thisBR;
        blng__RevenueRecognitionRule__c thisRR = CPQ_TestDataUtility.createRevRule('Test RR');
        insert thisRR;
        blng__TaxRule__c thisTR = CPQ_TestDataUtility.createTaxRule('Test TR');
        insert thisTR;
        //create product
        Product2 thisProduct = CPQ_TestDataUtility.createProduct('Test Product Bundle',20.01);
        thisProduct.blng__BillingRule__c = thisBR.Id;
        thisProduct.blng__RevenueRecognitionRule__c = thisRR.Id;
        thisProduct.blng__TaxRule__c = thisTR.Id;
        thisProduct.Family = 'Subscription';
        insert thisProduct;
        System.debug('******* thisProduct: '+thisProduct);  
        
        PriceBookEntry thisPBE = CPQ_TestDataUtility.createPricebookEntry(thisProduct.Id,20.01);
        insert thisPBE;
        
        Account thisAccount = CPQ_TestDataUtility.createAccount('Test Account');
        insert thisAccount;
        
        Opportunity thisOpportunity = CPQ_TestDataUtility.createOpportunity(thisAccount.Id);
        insert thisOpportunity; 
        blng__LegalEntity__c le = CPQ_TestDataUtility.createLegalEntity('test le', '0601');
        insert le;
        Test.startTest();
        SBQQ__Quote__c thisQuote = CPQ_TestDataUtility.createQuote(thisAccount.Id,thisOpportunity.Id);
        thisQuote.Region__c = 'NA';
        thisQuote.Order_Type__c = 'SaaS';
        insert thisQuote;
        
        SBQQ__QuoteLine__c thisQuoteLine = CPQ_TestDataUtility.createQuoteLine(thisQuote.Id,thisProduct.Id,thisPBE.Id);
        thisQuoteLine.SBQQ__ChargeType__c = 'One-Time';
        insert thisQuoteLine;
        
        Test.stopTest();
        
       
        SBQQ__QuoteTerm__c quoteTerm = CPQ_TestDataUtility.createQuoteTerm(thisQuote, 'THE QUOTE IS PRESENTED IN A NON FINALIZED FORM', 50);
        insert quoteTerm;
        SBQQ__TermCondition__c termCondition = CPQ_TestDataUtility.createTermCondition(quoteTerm, 'SBQQ__Notes__c', 'contains', 'test');
        insert termCondition;
        OrgWideNoReplyId__c orgId = new OrgWideNoReplyId__c();
        orgId.Name = 'NoReplyId';
        orgId.OrgWideId__c = [select Id, Address, DisplayName from OrgWideEmailAddress Where DisplayName='DONOTREPLY'][0].Id;
        insert orgId;   
    }
    

    static testMethod void testQuoteOrdered(){
        Test.startTest();
        SBQQ__Quote__c testQuote = [SELECT Id FROM SBQQ__Quote__c LIMIT 1];
        testQuote.SBQQ__Ordered__c = true;
        testQuote.Sales_Org__c = '0601';
        update testQuote;
        Test.stopTest(); 
    }
    
    static testMethod void testUpdateAttachmentInfo () {
        Test.startTest();
        SBQQ__Quote__c testQuote = [SELECT Id FROM SBQQ__Quote__c LIMIT 1];
        testQuote.Quote_Region__c = 'APAC';
        update testQuote;
        Test.stopTest(); 
    }
        
    static testMethod void testQuoteLineItemTriggerHelper(){
        
        //Create Order
        SBQQ__Quote__c testQuote = [SELECT Id , Quote_Region__c, SBQQ__QuoteLanguage__c, Deliver_To_Country__c FROM SBQQ__Quote__c LIMIT 1];
        SBQQ__QuoteLine__c testQuoteLine = [SELECT Id,SBQQ__Quote__c,SBQQ__RequiredBy__c,SBQQ__Quote__r.Quote_Region__c, SBQQ__Quote__r.SBQQ__QuoteLanguage__c, SBQQ__Quote__r.Deliver_To_Country__c FROM SBQQ__QuoteLine__c LIMIT 1];
        testQuote.SBQQ__Ordered__c = true;
        //update testQuote;
        Map<Id, SBQQ__QuoteLine__c> mapQLI = new Map<Id, SBQQ__QuoteLine__c>();
        mapQLI.put(testQuoteLine.id, testQuoteLine);
        
        set<id> setQuoteIds = new set<id>();
        setQuoteIds.add(testQuote.id);
        Test.startTest();
        CPQ_QuoteLineItemTriggerHelper obj = new CPQ_QuoteLineItemTriggerHelper();
        obj.updateOpportunity(setQuoteIds);
        
        set<string> quoteRegion = new set<string>();
        quoteRegion.add('Test1');
        set<string> quoteLanguage = new set<string>();
        quoteLanguage.add('Test1');
        set<string> quoteCountry = new set<string>();
        quoteCountry.add('Test1');
        set<string> quoteOrderType = new set<string>();
        quoteOrderType.add('Test1');
        
        List<string> quoteOrderL = new List<string>();
        quoteOrderL.add('Test1');
        
        
        List<SBQQ__Quote__c> lstQuote = new List<SBQQ__Quote__c>();
        lstQuote.add(testQuote);
        
        List<SBQQ__QuoteLine__c> lstQLI = new List<SBQQ__QuoteLine__c>();
        lstQLI.add(testQuoteLine);
        
        Map<Id, List<string>> mapQLI2 = new Map<Id, List<string>>();
        mapQLI2.put(testQuoteLine.id, quoteOrderL);
        
        Map<Id, List<string>> mapQL3 = new Map<Id, List<string>>();
        
        Map<Id, List<SBQQ__QuoteLine__c>> mapQLI1 = new Map<Id, List<SBQQ__QuoteLine__c>>();
        mapQLI1.put(testQuoteLine.id, lstQLI);
        
        
        obj.updateAttachmentInfo(lstQuote,quoteRegion,quoteLanguage,quoteCountry,quoteOrderType,mapQLI2);
        obj.updateAttachmentInfo(lstQuote,quoteRegion,quoteLanguage,quoteCountry,quoteOrderType,mapQL3);
        Test.stopTest();
    }
}
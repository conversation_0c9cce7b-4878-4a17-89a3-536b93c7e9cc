global class PR_EmailController {
    global Id recordId;
    global Decimal finalBasePrice { get; set; }
    global ProductRequest parentRecord { get; set; }
    global List<PRLIWrapper> lines { get; set; }

    public void setrecordId(Id recordId){
        this.recordId = recordId;
        setParentRecord(recordId);
        setLines(recordId);
    }
    
    public Id getrecordId(){
        return recordId;
    }

    public ProductRequest setParentRecord(Id recordId) { 
        if (recordId != null && parentRecord == null) {
            parentRecord = [Select Id, ProductRequestNumber, WorkOrder.WorkOrderNumber, Service_Resource__r.Name,  
                            Account.Name, Asset__r.Name, Asset__r.Service_Territory__r.Name, Service_Manager__r.Name, 
                            Total_Base_Service_Price__c, Total_Order_Cost__c, Sales_Org__c 
                            From ProductRequest where Id=: recordId Limit 1];
            setFinalBasePrice(parentRecord);
        }
        return parentRecord;
    }

    public List<PRLIWrapper> setLines(Id recordId) {
        List<PRLIWrapper> wrapperList = new List<PRLIWrapper>();
        if(recordId != null) {
            List<ProductRequestLineItem> childRecords = [Select Id, ProductRequestLineItemNumber, Part_Number__c, Product2.Name, Base_Service_Price__c, 
                                Total_Line_Cost__c, QuantityRequested From ProductRequestLineItem where ParentId =: recordId];

            for(ProductRequestLineItem prli: childRecords) {
                PRLIWrapper wrap = new PRLIWrapper(prli);
                wrapperList.add(wrap);
            }
        }
        lines = new List<PRLIWrapper>();
        lines.addAll(wrapperList);
        return lines;
    }

    // Only for Sales Org CN 4990
    public Decimal setFinalBasePrice(ProductRequest parentRecord) {
        finalBasePrice = (parentRecord != null && parentRecord.Total_Base_Service_Price__c != null) ? (parentRecord.Total_Base_Service_Price__c * Decimal.valueOf(Label.Sales_Org_4990_Threshold_Value)).setScale(2, RoundingMode.UP) : 0;
        return finalBasePrice;
    }
   
    global class PRLIWrapper {
        global ProductRequestLineItem line { get; set; }
        global Decimal basePrice { get; set; }
        
        global PRLIWrapper(ProductRequestLineItem prli) {
            this.line = prli;
            // Only for Sales Org CN 4990
            this.basePrice = prli.Base_Service_Price__c != null ? ( (prli.Base_Service_Price__c * prli.QuantityRequested) * Decimal.valueOf(Label.Sales_Org_4990_Threshold_Value)).setScale(2, RoundingMode.UP) : 0;
        }
    }
}
public with sharing class NWATrig<PERSON><PERSON><PERSON><PERSON> extends TriggerHandler {
    
    private Map<Id, ERP_NWA__c> newNWAMap;
    private Map<Id, ERP_NWA__c> oldNWAMap;
    private List<ERP_NWA__c> newNWAList;
    
    public NWATriggerHandler() {
        this.newNWAMap = (Map<Id, ERP_NWA__c>) Trigger.newMap;
        this.oldNWAMap = (Map<Id, ERP_NWA__c>) Trigger.oldMap;
        this.newNWAList = (List<ERP_NWA__c>) Trigger.new;
        
        //STRY0462726 - Moved trigger logic check to TriggerFlag__mdt
        //if(TriggerSettings__mdt.getInstance('Single_Row_for_Trigger_Value').NWAtriggerdisabled__c){
        
        //STRY0462726
        Map<String, TriggerFlag__mdt> mcs = TriggerFlag__mdt.getAll();
        if(mcs.get('ERP_NWA') != null && mcs.get('ERP_NWA').Disabled__c) {
            
            TriggerHandler.bypass('NWATriggerHandler');
            System.debug('handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bypassed: '+TriggerHandler.isBypassed('NWATriggerHandler'));
        }
    }
    
    //before insert - remove if not needed
    public override void beforeInsert(){
        if (!NWAServices.byPassFromWoUpdate)
        {   
            //VFSL_NWATriggerHandler objcls = new VFSL_NWATriggerHandler(Trigger.isExecuting, Trigger.size);
            NWAServices.populateFields(newNWAList, oldNWAMap);
            NWAServices.setSalesOrderNumber(newNWAList, oldNWAMap); 
            NWAServices.setSalesOrderItemNumber(newNWAList, oldNWAMap);
            NWAServices.updateTrainingCoordinator(newNWAList);
        }
    }
    
    //after insert - remove if not needed
    public override void afterInsert(){
        NWAServices.onInsertcloseWds(newNWAList, oldNWAMap, newNWAMap );
        NWAServices.sendAutomatedEmail(newNWAList, oldNWAMap);
        NWAServices.updatesplproductcounters(newNWAList, oldNWAMap);
        NWAServices.updateMissingWBS(newNWAList, oldNWAMap);
    }
    
    //before update - remove if not needed
    public override void beforeUpdate() {
        if (!NWAServices.byPassFromWoUpdate)
        {   
            //VFSL_NWATriggerHandler objcls = new VFSL_NWATriggerHandler(Trigger.isExecuting, Trigger.size);
            NWAServices.populateFields(newNWAList, oldNWAMap);
            ERPNWALog.CreateERPNWSTrace(newNWAMap,oldNWAMap);
            NWAServices.setSalesOrderNumber(newNWAList, oldNWAMap); 
            NWAServices.setSalesOrderItemNumber(newNWAList, oldNWAMap);
        }
        NWAServices.updateMissingWBS(newNWAList);
    }
    
    //after update - remove if not needed
    public override void afterUpdate() {
        NWAServices.onUpdatecloseWds(newNWAList, oldNWAMap, newNWAMap);
        NWAServices.switchERPNWAOnCounters(newNWAList, oldNWAMap);
        NWAServices.sendAutomatedEmail(newNWAList, oldNWAMap);
        NWAServices.updatesplproductcounters(newNWAList, oldNWAMap);
        NWAServices.updateMissingWBS(newNWAList, oldNWAMap); 
        //OLCCustomerEnrollmentUtility.onRegistrationNWAUpdate(newNWAList, oldNWAMap);//STRY0449351
    }
    
    //before delete - remove if not needed
    public override void beforeDelete() {
        
    }
    
    //after delete - remove if not needed
    public override void afterDelete() {
        
    }
    
}
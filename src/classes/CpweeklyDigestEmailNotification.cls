/*************************************************************************\
    @ Author        : <PERSON><PERSON><PERSON>
    @ Date      : 10-May-2013
    @ Description   : An Apex class to get data from Content that is to be displayed in 
    Visualforce Email Template that is sent to customers on Weekely basis.
    @ Last Modified By  :   Narmada Yelavaluri
    @ Last Modified On  :   27-May-2013
    @ Last Modified Reason  :   Updated Header Comments
    
Change Log:
7-Nov-2017 - <PERSON><PERSON><PERSON> - INC4701142\STSK0013311: My Notifications Email not being sent to MyVarian Users - added a filter to only process contents modified in last week
10-Oct-2020 - <PERSON><PERSON><PERSON> Chede -   updated query to refer Asset object instead of servicemax object
****************************************************************************/
global with sharing  class CpweeklyDigestEmailNotification
{
    public List<ContentVersion> documnets{get;set;}
    public boolean isARIAIncluded{get;set;}
    Public set<string> productGroupSet;
    Private set<String> documentTypes = new set<String>{'Safety Notifications', 'CTBs', 'Release Notes'};
    public string iscustomer{get;set;}
    public Id contactid{get;set;} 
    public string language{get;set;}
    public CpweeklyDigestEmailNotification() {
       documnets= new List<ContentVersion>();
       productGroupSet=new set<string>();
    }

    global List<ContentVersion> getCpweeklyDigest() 
    {
        documnets= new List<ContentVersion>();
        List<Contact> contactidfortest = new List<Contact>([Select id,accountid from contact where id=:contactid]);
            if(contactidfortest.size() > 0){
                iscustomer = 'Yes';
                Id accountId=[select id ,accountid from contact where id=:contactid].accountid;
                /*for(SVMXC__Installed_Product__c ip :[Select s.SVMXC__Product__r.Product_Group__c, 
                                                                s.SVMXC__Product__c, s.Id, s.SVMXC__Company__c 
                                                                From SVMXC__Installed_Product__c s 
                                                                where s.SVMXC__Company__c =: accountId and 
                                                                SVMXC__Product__r.Product_Group__c != null]
                                                                )
                {                                                                   
                    productGroupSet.addAll(ip.SVMXC__Product__r.Product_Group__c.split(';')); 
                }*/
                list<Id> AccIds = new list<Id>();
                //set<String> productGroupSet = new set<String>();
                
                //Installed products from Account
                for(Asset ip :[Select s.Product2.Product_Group__c,s.Product2Id, s.Id, s.AccountId From Asset s where s.AccountId =: AccountId and s.Product2.Product_Group__c != null])                                                                
                {	if(String.isNotEmpty(ip.Product2.Product_Group__c))	productGroupSet.addAll(ip.Product2.Product_Group__c.split(';'));	}
                System.debug('**productGroupSet 1** : ' +productGroupSet);
                
                //Products from AdminTool
                String AdminToolProducts = [Select Id, Selected_Groups__c from Account where Id =:accountId].Selected_Groups__c;
                if(AdminToolProducts<>null)   productGroupSet.addAll(AdminToolProducts.split(';'));
                System.debug('**productGroupSet 2** : ' +productGroupSet);
                
                //Products from Contact Role associations
                for (Contact_Role_Association__c loopvar : [Select id, Contact__c, Account__c from Contact_Role_Association__c where Contact__c = :contactid])
                {    AccIds.add(loopvar.Account__c);    }
                System.debug('**AccIds** : ' +AccIds);                
                if(AccIds.size() > 0){
                    for(Asset ip :[Select Product2.Product_Group__c,Product2Id, Id, AccountId From Asset where AccountId IN :AccIds and Product2.Product_Group__c != null])
                    {   if(string.isNotEmpty(ip.Product2.Product_Group__c)) productGroupSet.addAll(ip.Product2.Product_Group__c.split(';'));      }
                }
                System.debug('**productGroupSet 3** : ' +productGroupSet);
            }else{ 
                Schema.DescribeFieldResult fieldResult = Product2.Product_Group__c.getDescribe();
                List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
                for( Schema.PicklistEntry f : ple)
                {
                    productGroupSet.add(f.getValue());
                }       
            }
        //STRY0126429: Dipali: Add known issues upload text in email template if known issue is uploaded in last 1 week : Start
        set<String> knwnIssuePG = new set<String>();
        isARIAIncluded = false;
        List<KnownIssues_ProductVersions__mdt> ProdVersions = [select id,MasterLabel,DeveloperName,Versions__c from KnownIssues_ProductVersions__mdt];
            if(ProdVersions != null && ProdVersions.size() > 0){
                for(KnownIssues_ProductVersions__mdt var : ProdVersions){
                    knwnIssuePG.add(var.MasterLabel);
                  }
              }
        String ZoneId = [select Id from Community where Name = 'MyVarian Known Issues'].Id;
        List<Idea> knwnIssues = [select id from Idea where communityId =: ZoneId and Status != 'Internal' and (CreatedDate =Last_Week or LastModifiedDate =Last_Week)];
        for(String s: knwnIssuePG){
           if(productGroupSet.contains(s) && knwnIssues != null && !knwnIssues.Isempty())isARIAIncluded = true;
              
        }
        //STRY0126429: Dipali: Add known issues upload text in email template if known issue is uploaded in last 1 week : Start
        set<string> stContentDocId=new set<string> ();
        for(ContentWorkspaceDoc var:[Select c.SystemModstamp, c.IsOwner, c.IsDeleted, c.Id, c.CreatedDate, 
                                        c.ContentWorkspace.Name, c.ContentWorkspaceId, c.ContentDocumentId From ContentWorkspaceDoc c 
                                        where ContentWorkspace.Name IN :  productGroupSet
                                        and ContentDocument.ContentModifiedDate = Last_Week]){
            stContentDocId.add(var.ContentDocumentId);
        }

        if(stContentDocId.size()>0)
        {
            documnets = [select id,Title, Document_Type__c, Date__c 
                           from ContentVersion where 
                           recordtype.name != 'Marketing Kit' 
                           and Document_Language__c = 'English'
                           and islatest=true
                           and Document_Type__c in : documentTypes 
                           and (ContentDocumentId in : stContentDocId) 
                           and  (ContentModifiedDate =Last_Week OR CreatedDate =Last_Week) 
                           Order by  Date__c Desc];
        }
        
        return documnets ;
    }
}
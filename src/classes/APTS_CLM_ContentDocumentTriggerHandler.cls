/**
   @Class Name:     APTS_CLM_FeedItemTriggerHandlerTest
   @Company:        Standav
   @CreatedDate:    12/01/2020
   @Description:    Test class for for APTS_CLM_ContentDocumentTriggerHandler.
   Modification Log
   -----------------------------------------------------------
   Developer            Modification Date           Comments
   -----------------------------------------------------------
   <PERSON><PERSON><PERSON> M             12/01/2020                 Original Version
 */
public class APTS_CLM_ContentDocumentTriggerHandler {
   
    public static void FileNameCheck (List<ContentDocument> listOfContentDocuments){      
        integer i =0;        
         for (ContentDocument CD :listOfContentDocuments){
                     if(listOfContentDocuments[i].Title.contains('- ACTIVATED.')== true ){ 
                         listOfContentDocuments[i].addError('You don\'t have the permission to delete an Activated Document');
                     }
                     i++;
          } 
                                     
            
    }
}
// Ideally SeeAllData should not be but reason of setting it true is due to multiple validation which was failing the test class and due to 
// lack of time and deployment was near we have set SeeAllData as true
// TODO: Modify the test class and use Data Factory to create records.
@isTest(SeeAllData = true)
public class CurrentAmendmentControllerTest {
    
	public static testMethod void currentAmendmentAfterInsert_Test() {
        
        Id oppId;
        oppId = [SELECT Id, BigMachines__Opportunity__c FROM BigMachines__Quote__c WHERE Amendment__c =: true Limit 1].BigMachines__Opportunity__c;
        
        Set<Id> qSet = new Set<Id>();
        for(BigMachines__Quote__c q : [SELECT Id, Name, Current_Amendment__c, Amendment__c, BigMachines__Opportunity__c, BigMachines__Status__c,
                 								BigMachines__Quote__c.Parent_Quote__c  
                                        FROM BigMachines__Quote__c  WHERE BigMachines__Opportunity__c =: oppId]) {
			qSet.add(q.Id);                             
		}
        
        Test.startTest();
        currentAmendmentController.currentAmendmentAfterInsert(qSet, new Set<Id>{oppId});
        Test.stopTest();
        
    }
    
    public static testMethod void currentAmendmentAfterUpdate_Test() {
    	
        Id oppId;
        oppId = [SELECT Id, BigMachines__Opportunity__c FROM BigMachines__Quote__c WHERE Amendment__c =: true Limit 1].BigMachines__Opportunity__c;
        
        Set<String> qSet = new Set<String>();
        Map<String, BigMachines__Quote__c> qMap = new Map<String, BigMachines__Quote__c>();
        for(BigMachines__Quote__c q : [SELECT Id, Name, Current_Amendment__c, Amendment__c, BigMachines__Opportunity__c, BigMachines__Status__c,
                 								BigMachines__Quote__c.Parent_Quote__c  
                                        FROM BigMachines__Quote__c  WHERE BigMachines__Opportunity__c =: oppId]) {
			qMap.put(q.Name, q);
			qSet.add(q.Name);                             
		}
        
    	Test.startTest();
        currentAmendmentController.currentAmendmentAfterUpdate(qSet, qMap, new Set<Id>{'0064400000imYPy'});
        test.stopTest();
    
    }
    
    public static testMethod void currentAmendmentBeforeUpdate_Test() {
    
        Id oppId;
        oppId = [SELECT Id, BigMachines__Opportunity__c FROM BigMachines__Quote__c WHERE Amendment__c =: true Limit 1].BigMachines__Opportunity__c;
        
        Set<Id> qSet = new Set<Id>();
        Map<String, BigMachines__Quote__c> qMap = new Map<String, BigMachines__Quote__c>();
        for(BigMachines__Quote__c q : [SELECT Id, Name, Current_Amendment__c, Amendment__c, BigMachines__Opportunity__c, BigMachines__Status__c,
                 								BigMachines__Quote__c.Parent_Quote__c  
                                        FROM BigMachines__Quote__c  WHERE BigMachines__Opportunity__c =: oppId]) {
			qMap.put(q.Name, q);
			qSet.add(q.Id);                             
		}
    	
        test.startTest();
        currentAmendmentController.currentAmendmentBeforeUpdate(qMap, new Set<Id>{oppId});
        test.stopTest();
    
    }
}
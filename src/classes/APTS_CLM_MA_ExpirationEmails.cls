/*
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>
Invocable method for APTS_CLM_MA_Expiration_Flow, CLM Agreement Auto Renewal Notification flow

*/

public class APTS_CLM_MA_ExpirationEmails {
    @InvocableMethod(label='Get Emails of Additional Participant on NR' description='Iterate over Id of participant and create email list')
    public static void getEmail(List<InputVariables> inputVariable){
        //STRY0212054 - Added null check for input variable
        if(inputVariable.get(0).agmts != null){
            sendRenewalOrExpiryEmail(inputVariable.get(0).agmts);
        }
        
    }     
    public static void sendRenewalOrExpiryEmail(List<Apttus__APTS_Agreement__c> agreement){
        Negotiation_Request__c NegotiationRequest = new Negotiation_Request__c();
        Account account = new Account();
        Messaging.Email[] messages = new Messaging.Email[0];
        List<String> emailofParticipantList=new List<String>();
        //STRY0212058 - Trupti J - Split the email addresses stored in Label Default_To_Email_Addresses - Start
        emailofParticipantList.addAll(Label.Default_To_Email_Addresses?.split(','));
        //STRY0489719 - Aishwarya S - enable expiry emails for NASalesAdmin
        emailofParticipantList.addAll(Label.CLM_NA_Sales_Admin?.split(','));
        emailofParticipantList.addAll(Label.CLM_Service_Admin?.split(','));
        //STRY0212058 - Trupti J - Split the email addresses stored in Label Default_To_Email_Addresses - End
        emailofParticipantList.addAll(Label.CLM_NA_Service_Email?.split(','));
        emailofParticipantList.addAll(Label.CLM_Commercial_Legal_Email?.split(','));
            
        List<Apttus__APTS_Agreement__c> expiredAgmts = new List<Apttus__APTS_Agreement__c>();
        List<String> idList =new List<String>();
        Date endDate = System.Today();
        OrgWideEmailAddress[] owea = [select Id from OrgWideEmailAddress where Address = '<EMAIL>'];
        Id agmtId = null;    
        //Start Aishwarya S -STRY0210738 - Set Contact to default CLM contact and looping through the list 
        Id contactId = Label.Default_CLM_Contact_Id;
        Set<Id> agmtAccountIds = new Set<Id>();
        Set<Id> NrIds = new Set<Id>();
        For(Apttus__APTS_Agreement__c agmtNew: agreement){
            if(agmtNew.Negotiation_Request__c != null)
                NrIds.add(agmtNew.Negotiation_Request__c);
            
            if(agmtNew.Apttus__Account__c != null)
                agmtAccountIds.add(agmtNew.Apttus__Account__c);
            //STRY0222918 - If aggregate end date is in past, set status to expired - start
            if(agmtNew.Apttus__Status__c != 'Expired' && agmtNew.Aggregate_EndDate_with_Renewals__c < System.Today()){
                agmtNew.Apttus__Status__c = 'Expired';
                expiredAgmts.add(agmtNew);
             }        
        }
        if(!expiredAgmts.isEmpty()){
            Update expiredAgmts;
        }
        //STRY0222918 - If aggregate end date is in past, set status to expired - end
        Map<Id,Account> mapAcc = new Map<Id,Account>([Select ID,Regional_Director__c,District_Manager__c,OwnerId, 
                                                      Regional_Sales_Manager__c,District_Sales_Manager__c,Regional_Service_Manager__c, 
                                                      District_Service_Manager__c 
                                                      from Account where ID IN: agmtAccountIds]);
        Map<Id,Negotiation_Request__c> mapNegotiationRequests  = new Map<Id,Negotiation_Request__c>([Select ID,Customer_Contact_Name__c,Additional_Participants_Record_ids__c 
                                                                                                     from Negotiation_Request__c where Id IN: NrIds]);
        For(Apttus__APTS_Agreement__c agmtNew: agreement) {
            idList.clear();
            idList.add(agmtNew.CreatedById);
            //STRY0218118 - changed agreement end date  
            endDate = agmtNew.Current_Term_End_Date__c;
            agmtId = agmtNew.Id;
            
            if(agmtNew.Apttus__Account__c != null && mapAcc.containskey(agmtNew.Apttus__Account__c)){
                account = mapAcc.get(agmtNew.Apttus__Account__c);
                idList.add(account.Regional_Director__c);
                idList.add(account.District_Manager__c);
                idList.add(account.OwnerId);
                //Add these user Ids to list only when the coverage is Sales/Services/Blank
                //STRY0212054 - Aishwarya - removed if conditions for Coverage as requested by business since production data does not have Coverage field populated 
                // if(agmtNew.Coverage__c != null){
                //&& agmtNew.Coverage__c.trim() != '' && (agmtNew.Coverage__c.contains('MA Sales') || agmtNew.Coverage__c.contains('MA Support/Service'))) {
                idList.add(account.Regional_Sales_Manager__c);
                idList.add(account.District_Sales_Manager__c);
                idList.add(account.Regional_Service_Manager__c);
                idList.add(account.District_Service_Manager__c);
                // }
            }
            if(agmtNew.Negotiation_Request__c != null && mapNegotiationRequests.containskey(agmtNew.Negotiation_Request__c)){
                NegotiationRequest  = mapNegotiationRequests.get(agmtNew.Negotiation_Request__c);
                if(NegotiationRequest != null && NegotiationRequest.Customer_Contact_Name__c != null){
                    contactId = NegotiationRequest.Customer_Contact_Name__c;
                }
                if(NegotiationRequest.Additional_Participants_Record_ids__c != null){
                    idList.addAll(NegotiationRequest.Additional_Participants_Record_ids__c.split(',')); 
                }   
            }
            else if(agmtNew.Apttus__Primary_Contact__c != null)
                contactId = agmtNew.Apttus__Primary_Contact__c;
            
            for(User u:[select id,email from User where id in:idList AND IsActive = True]){
                emailofParticipantList.add(u.email);
            }  
            
            // Get the email template
            String selectedTemplateName = '';
            System.debug('---'+agmtNew.Expires_In_Days__c+'---'+agmtNew.Current_Term_End_Date__c+'---'+agmtNew.Aggregate_EndDate_with_Renewals__c);
            
            if (agmtNew.Expires_In_Days__c != -1 && agmtNew.Apttus__Auto_Renewal__c == true && agmtNew.Current_Term_End_Date__c == agmtNew.Aggregate_EndDate_with_Renewals__c){
                System.debug('selectedTemplateName 2 '+selectedTemplateName);
                selectedTemplateName = (endDate < System.Today())?'APTS_CLM_After_Agreement_Expiration_Notification_Sales':'APTS_CLM_Agreement_Expiration_Notification';
            }
            else if(agmtNew.Apttus__Auto_Renewal__c == true && agmtNew.Current_Term_End_Date__c<=agmtNew.Aggregate_EndDate_with_Renewals__c ){
                System.debug('selectedTemplateName 1'+selectedTemplateName);
                selectedTemplateName = (agmtNew.Expires_In_Days__c == 180?'APTS_CLM_MA_Up_For_Renewal':'APTS_CLM_MA_Renewed');
            }
            else if(agmtNew.Apttus__Auto_Renewal__c == true && agmtNew.Aggregate_EndDate_with_Renewals__c == null ){
                System.debug('selectedTemplateName 1'+selectedTemplateName);
                selectedTemplateName = (agmtNew.Expires_In_Days__c == 180?'APTS_CLM_MA_Up_For_Renewal':'APTS_CLM_MA_Renewed');
            }
            else if(agmtNew.Aggregate_EndDate_with_Renewals__c < System.Today()){
                selectedTemplateName  = 'APTS_CLM_After_Agreement_Expiration_Notification_Sales';
            } 
            System.debug('selectedTemplateName 3'+selectedTemplateName);
            if (agmtNew.Apttus__Auto_Renewal__c == false){
                selectedTemplateName = (endDate < System.Today())?'APTS_CLM_After_Agreement_Expiration_Notification_Sales':'APTS_CLM_Agreement_Expiration_Notification';
            }  
            /*else {
                selectedTemplateName  = 'APTS_CLM_After_Agreement_Expiration_Notification_Sales';
            } */    
            EmailTemplate selectedTemplate = [SELECT Id FROM EmailTemplate WHERE DeveloperName =: selectedTemplateName];       
            
            
            //Send email to the additional participants as well as other users from NR and Account
            Messaging.SingleEmailMessage mailToSend = new Messaging.SingleEmailMessage();
            if ( owea.size() > 0 ) {
                mailToSend.setOrgWideEmailAddressId(owea.get(0).Id);
            }
            mailToSend.setTargetObjectId(contactId);
            mailToSend.setWhatId(agmtId);
            //mailToSend.saveAsActivity = false;
            mailToSend.setTemplateID(selectedTemplate.Id);
            mailToSend.setToAddresses(new List<String>(emailofParticipantList));
            if(mailToSend != null)
                messages.add(mailToSend);
            
            //List<Messaging.SendEmailResult> sendResults = Messaging.sendEmail(new List<Messaging.Email> { mailToSend });
        }
        if(messages!= null)
            Messaging.sendEmail(messages);
    }
        //End Aishwarya S -STRY0210738 - Set Contact to default CLM contact and looping through the list 
    //}      

    public class InputVariables{
        @InvocableVariable
        public List<Apttus__APTS_Agreement__c> agmts;
    }
}
@isTest
private class VFSL_VIS_WorkOrderPartsLineCtrlTest {
    
    public static Schema.Location ecfLoc;
    public static ServiceTerritory serviceTerritory;
    public static ServiceResource serviceResource;
    public static Asset assetObj;
    public static WorkOrder wo;
    
    public static WorkOrderLineItem woli;
    public static Case caseRec;
    
    public static Product2 prod;
    public static ProductRequest productReq;
    public static ProductRequestLineItem lineItem;
    
    private static Boolean isVarianSupplied = false;
    
    private static final Id WOLI_HEADER_RT = 
      Schema.SObjectType.WorkOrderLineItem.getRecordTypeInfosByName().get('Header').getRecordTypeId();
    
    @testSetup
    private static void setup() {
        String devRecordTypeName = 'Installation';
        WorkOrder workOrder = new WorkOrder();
        SObjectType entity = workOrder.getSObjectType();
        Id devRecordTypeId =VFSL_TestDataUtility.getRecordTypeId(entity,devRecordTypeName);
    
        ERP_Pcode__c pcode = VFSL_TestDataUtility.getERPPCode();
        insert pcode;
         
        Product2 prod = VFSL_TestDataUtility.getProduct(pcode.Id);
        prod.Product_Type__c = 'Model';
        prod.Is_Top__c = true;
        prod.ProductCode = 'TestAssetCoverage';
        prod.Name = 'Test Asset Coverage';
        insert prod;
         
        assetObj = VFSL_TestDataUtility.createAsset('HTEST99A'+'wo342');  
        assetObj.Product2Id = prod.Id;
        insert assetObj;
        
        ecfLoc = VFSL_TestDataUtility.createLocation('My Location');
        insert ecfLoc;
        
        Test.startTest();
        serviceTerritory = VFSL_TestDataUtility.createServiceTerritory('[U3H] NORTHEAST MID ATLA', 'U3H','Resource_Territory');
        insert serviceTerritory;

        serviceResource = VFSL_TestDataUtility.createServiceResource('Nick Sauer',serviceTerritory,'Technician');
        insert serviceResource;
        
        wo = new Workorder(Status='Assigned', Dispatch_Status__c='Accepted', AssetId=assetObj.Id, 
                                    Direct_Assignment__c=false, FSR_Language_Code__c='ENG');
        
        wo.recordtypeId =devRecordTypeId;
        wo.LocationId =ecfLoc.Id;
        wo.StartDate = DateTime.Now();
        wo.EndDate = DateTime.Now().AddDays(+1);
        wo.OwnerId = UserInfo.getUserId();
        wo.SAP_Priority__c = '1 - Emergency';
        wo.ECF_Required__c = 'Yes from Work Order'; 
        wo.Case_Type__c = 'Incident';
        wo.Service_Resource__c = serviceResource.Id;
        insert wo;
        
        WorkOrderLineItem headerWoli = new WorkOrderLineItem();
        headerWoli.WorkOrderId = wo.Id;
        headerWoli.Activity_Group__c = 'Installation';
        headerWoli.Activity_Type__c = 'Installation';
        headerWoli.RecordTypeId = WOLI_HEADER_RT;
        insert headerWoli;
        Test.stopTest();
    }
    
    @isTest private static void createVSTest() {
        WorkOrder wo = [SELECT Id, AssetId 
                        FROM WorkOrder 
                        LIMIT 1];
        
        createProduct(wo);
            
        Test.startTest();
        
        ProductRequestLineItem prli = [SELECT Id, Product2Id 
                                       FROM ProductRequestLineItem 
                                       LIMIT 1];
        
        VFSL_VIS_WorkOrderPartsLineCtrl.WorkOrderDetails wrapper = VFSL_VIS_WorkOrderPartsLineCtrl.getWorkOrderDetails(wo.Id);
        isVarianSupplied = true;
        Map<String, String> fieldValueMap = createFieldValueMap(wrapper);
 		fieldValueMap.put('partsid', prli.Id);
        fieldValueMap.put('removedpartsid', prli.Product2Id);
        
        VFSL_VIS_WorkOrderPartsLineCtrl.postPartsLines(fieldValueMap);

        Test.stopTest();
    }
    
    @isTest private static void createCSTest() {
        WorkOrder wo = [SELECT Id, AssetId 
                        FROM WorkOrder 
                        LIMIT 1];
        
        createProduct(wo);
            
        Test.startTest();
        
        Product2 prod = [SELECT Id 
                         FROM Product2
                         LIMIT 1];

        VFSL_VIS_WorkOrderPartsLineCtrl.WorkOrderDetails wrapper = VFSL_VIS_WorkOrderPartsLineCtrl.getWorkOrderDetails(wo.Id);
        isVarianSupplied = true;
        Map<String, String> fieldValueMap = createFieldValueMap(wrapper);
 		fieldValueMap.put('partsid', prod.Id);
        fieldValueMap.put('removedpartsid', prod.Id);
        fieldValueMap.put('source', 'C - Customer Spares');
        
        VFSL_VIS_WorkOrderPartsLineCtrl.postPartsLines(fieldValueMap);

        Test.stopTest();
    }
    
     @isTest private static void createLPTest() {
        WorkOrder wo = [SELECT Id, AssetId 
                        FROM WorkOrder 
                        LIMIT 1];
        
        createProduct(wo);
            
        Test.startTest();
        List<Product2> prodList = VFSL_VIS_WorkOrderPartsLineCtrl.getProductByProductCode('AAA');
        
        Product2 prod = [SELECT Id 
                         FROM Product2
                         LIMIT 1];
        
        VFSL_VIS_WorkOrderPartsLineCtrl.WorkOrderDetails wrapper = VFSL_VIS_WorkOrderPartsLineCtrl.getWorkOrderDetails(wo.Id);
        isVarianSupplied = false;
        Map<String, String> fieldValueMap = createFieldValueMap(wrapper);
 		fieldValueMap.put('partsid', prod.Id);
        fieldValueMap.put('removedpartsid', prod.Id);
         fieldValueMap.put('lpprice', '2');
         fieldValueMap.put('lpdesc', 'test desc');
        
        VFSL_VIS_WorkOrderPartsLineCtrl.postPartsLines(fieldValueMap);

        Test.stopTest();
    }
    
    private static void createProduct(WorkOrder wo) {
        serviceTerritory = [SELECT Id FROM ServiceTerritory LIMIT 1];
        serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];
        
        prod = new Product2();
        prod.Name = 'Sample Product Optional';
        prod.Product_Cost__c = 5;
        prod.ERP_PCode_2__c = '11';
        prod.Is_Top__c = true;
        prod.Returnable__c = 'Temp Returnable';
        prod.ProductCode = 'AAA';
        insert prod;

        productReq = new ProductRequest();
        productReq.Status = 'Open';
        productReq.WorkOrderId = wo.Id;
        productReq.Asset__c = wo.AssetId;
        productReq.Service_Territory__c = serviceTerritory.Id;
        productReq.Service_Resource__c = serviceResource.Id;
        insert productReq;

        lineItem = new ProductRequestLineItem();
        lineItem.Product2Id = prod.Id;
        lineItem.ParentId = productReq.Id;
        lineItem.WorkOrderId = wo.Id;
        LineItem.QuantityRequested = 10;
        LineItem.Delivered_Qty__c  = 50;
        insert lineItem;

    }
    
    private static Map<String, String> createFieldValueMap(VFSL_VIS_WorkOrderPartsLineCtrl.WorkOrderDetails wrapper) {
        Map<String, String> fieldValueMap = new Map<String, String>();
        String source = isVarianSupplied ? 'V - Varian Supplied' : 'L - Locally Purchased';
        
        WorkOrderLineItem headerWoli = [SELECT Id 
                                        FROM WorkOrderLineItem 
                                        WHERE RecordTypeId = :WOLI_HEADER_RT 
                                        LIMIT 1];
        
        Asset assetRec = [SELECT Id
                          FROM Asset
                          LIMIT 1];
        
        fieldValueMap.put('Id', wrapper.WorkOrder.Id);
        fieldValueMap.put('asset', wrapper.WorkOrder.assetId);
        fieldValueMap.put('woliId', headerWoli.Id);
        fieldValueMap.put('quantity', '1');
        fieldValueMap.put('sn', '325323TEST');
        fieldValueMap.put('bn', '325323TESTBatch');
        fieldValueMap.put('toSpare', 'false');
        fieldValueMap.put('inWrnty', 'false');
        fieldValueMap.put('isMandatorySTB', 'false');
        fieldValueMap.put('source', source);
        fieldValueMap.put('sf6Count', '1');
        fieldValueMap.put('lpprice', '0');
        fieldValueMap.put('lpdesc', '');
        fieldValueMap.put('tospare', 'false');
        fieldValueMap.put('sec', '');
        fieldValueMap.put('desc', '');
        fieldValueMap.put('cause', 'Cable');
        fieldValueMap.put('delays', 'Acceptance');
        fieldValueMap.put('losttime', '2');
        fieldValueMap.put('batch', '23');
        fieldValueMap.put('noRemoval', 'false');
        fieldValueMap.put('assetId', assetRec.Id);

        return fieldValueMap;
    }
    
    private static Map<String, String> createFieldUpdateValueMap(VFSL_WorkOrderPartsLinesCtrl.WorkOrderDetails wrapper,
                                                                 WorkOrderLineItem woliRec) {
                                                                     
        Map<String, String> fieldValueMap = new Map<String, String>();
        String source = isVarianSupplied ? 'V - Varian Supplied' : 'L - Locally Purchased';
        
        fieldValueMap.put('woliId', woliRec.Id);
        fieldValueMap.put('prliId', woliRec.Product_Request_Line_Item__c);
        fieldValueMap.put('assetRecord', wrapper.WorkOrder.assetId);
        fieldValueMap.put('tospare', 'false');
        fieldValueMap.put('newQuantity', '2');
        fieldValueMap.put('serialNumber', '325323TEST');
        fieldValueMap.put('batchNumber', '325323TESTBatch');
        fieldValueMap.put('lpprice', '0');
        fieldValueMap.put('lpdesc', '');
        fieldValueMap.put('inWrnty', 'false');
        fieldValueMap.put('isMandatorySTB', 'true');
        
        return fieldValueMap;
    }

}
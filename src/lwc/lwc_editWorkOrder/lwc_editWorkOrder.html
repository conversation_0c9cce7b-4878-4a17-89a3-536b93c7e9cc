<template>
    <template if:true={loaded}>
        <lightning-spinner alternative-text="Loading"></lightning-spinner>
    </template>
    <div>
        <lightning-record-edit-form 
                                    record-id={recordId}
                                    object-api-name="WorkOrder"
                                    record-type-id={worecordtype}>
            <div class="slds-grid slds-gutters" style="padding-top: 10px">
                <div class="slds-col slds-size_1-of-2">
                    <span>
                        <lightning-output-field field-name="StartDate" value={startdate}></lightning-output-field>
                        <lightning-output-field field-name="EndDate" value={enddate}></lightning-output-field> 
                        <lightning-output-field field-name="Priority" value={priority}></lightning-output-field> 
                        <lightning-output-field field-name="Subject"></lightning-output-field>
                    </span>
                </div>
                <div class="slds-col slds-size_1-of-2">
                    <span>
                        <lightning-output-field field-name="Status" value={status}></lightning-output-field>
                        <lightning-output-field field-name="Top_Level_Asset__c"></lightning-output-field>  
                        <lightning-output-field field-name="AssetId" value={assetid}></lightning-output-field>       
                    </span>
                </div>
            </div>
            <div class="slds-grid slds-gutters" style="padding-top: 10px">
                <div class="slds-col slds-size_1-of-2">
                    <span>
                        <lightning-input-field field-name="Purchase_Order__c" value={purchaseorder}
                                               onchange={handlepurchaseorder}></lightning-input-field> 
                        <lightning-input-field field-name="Billing_Review_Requested__c" value={billingreviwrequested}
                                               onchange={handlebillingreviwrequestedchange}></lightning-input-field> 
                        <!--<lightning-input-field field-name="Escalated_to_DM__c" value={escalatedtodm}
                                               onchange={handleescalatedtodmchange}></lightning-input-field>--> 
                    </span>
                </div>
                <div class="slds-col slds-size_1-of-2">
                    <span>
                        <lightning-input-field field-name="Timezone_for_Timecard__c" value={timezone}
                                               onchange={handletimezonechange}></lightning-input-field> 
                        <lightning-input-field field-name="Purpose_of_Visit__c" value={purposeofvisit}
                                               onchange={handlepurposechange}></lightning-input-field> 
                    </span>
                </div>
            </div>
            <lightning-accordion allow-multiple-sections-open active-section-name={activeSections}>
                <lightning-accordion-section name="Comments Section" label="Comments">
                    <div class="slds-grid slds-gutters" style="padding-top: 10px">
                        <div class="slds-col slds-size_1-of-2">
                            <span>
                                <lightning-input-field field-name="Closure_Summary__c" value={closuresummary}
                                                       onchange={handleclosuresummarychange}></lightning-input-field> 
                                <lightning-input-field field-name="Internal_Comments__c" value={internalcomments}
                                                       onchange={handleinternalcomments}></lightning-input-field>
                            </span>
                        </div>
                    </div>
                </lightning-accordion-section>
                <lightning-accordion-section name="Service Section" label="Service Section">
                    <div class="slds-grid slds-gutters" style="padding-top: 10px">
                        <div class="slds-col slds-size_1-of-2">
                            <span>
                                <!-- STRY0192440 - Replaced field-name from Description_Case__c to Description -->
                                <lightning-input-field field-name="Description" value={description}
                                                       onchange={handledescriptionchange}></lightning-input-field> 
                                <lightning-input-field field-name="Heater_Hours__c" value={heaterhours}
                                                       onchange={handleheaterhourschange}></lightning-input-field> 
                                <!--<lightning-input-field field-name="High_Voltage_Hours__c" value={highvoltagehours}
                                                       onchange={handlehighvoltagehourschange}></lightning-input-field>-->  
                                <lightning-input-field field-name="Beam_Hours__c" value={beamhours}
                                                       onchange={handlebeamhourschange}></lightning-input-field>  
                                <lightning-input-field field-name="Non_Varian_Comments__c" value={nonvariancomments}
                                                       onchange={handlenonvariancommentschange}></lightning-input-field>  
                            </span>
                        </div>
                        <div class="slds-col slds-size_1-of-2">
                            <span>
                                <lightning-input-field field-name="Machine_Release__c" value={machinerelease}
                                                       onchange={handlemachinereleasechange}></lightning-input-field>
                                <lightning-input-field field-name="Agreed_Downtime__c" value={agreedowntime}
                                                       onchange={handleagreedowntimechange}></lightning-input-field> 
                                <lightning-input-field field-name="Non_Varian_Parts__c" value={nonvarianparts}
                                                       onchange={handlenonvarianpartschange}></lightning-input-field> 
                                <lightning-input-field field-name="Non_Varian_Service__c" value={nonvarianservice}
                                                       onchange={handlenonvarianservicechange}></lightning-input-field>  
                            </span>
                        </div>
                    </div>
                </lightning-accordion-section>
                <lightning-accordion-section name="Reschedule Section" label="Reschedule/Followup & notes">
                    <div class="slds-grid slds-gutters" style="padding-top: 10px">
                        <div class="slds-col slds-size_1-of-2">
                            <span>
                                <lightning-input-field field-name="Follow_Up_Required__c" value={followuprequired}
                                                       onchange={handlefollowuprequiredchange}></lightning-input-field> 
                                <lightning-input-field field-name="Follow_Up_Comments__c" value={followupnotables}
                                                       onchange={handlefollowupnotableschange}></lightning-input-field> 
                            </span>
                        </div>
                    </div>
                </lightning-accordion-section>
                <lightning-accordion-section name="Complaint Section" label="Complaint, PHI and Repercussions">
                    <div class="slds-grid slds-gutters" style="padding-top: 10px">
                        <div class="slds-col slds-size_1-of-3">
                            <span>
                                <lightning-input-field field-name="Complaint__c" disabled value={complaint}
                                                       onchange={handlecomplaintchange}></lightning-input-field>
                                <lightning-input-field field-name="Injured__c" value={injured}
                                                       onchange={handleinjuredchange}></lightning-input-field>
                                <lightning-input-field field-name="ECF_Required__c" value={ecfrequired}
                                                       onchange={handleecfrequiredchange}></lightning-input-field> 
                                <lightning-input-field field-name="Test_Specifications_Met__c" value={testspecificationsmet}
                                                       onchange={handletestspecificationsmetchange}></lightning-input-field>  
                            </span>
                        </div>
                        <div class="slds-col slds-size_1-of-3">
                            <span>
                                <!--<lightning-input-field field-name="PHI_Obtained__c" value={phiobtained}
                                                       onchange={handlephiobtainedchange}></lightning-input-field>-->
                                <lightning-input-field field-name="Process_Workflow__c" value={processworkflow}
                                                       onchange={handleprocessworkflowchange}></lightning-input-field>
                                <lightning-input-field field-name="Process_Code__c" value={processcode}
                                                       onchange={handleprocesscodechange}></lightning-input-field>
                                <!--STSK0024819-->
                                <lightning-input-field field-name="Case_Type__c" disabled value={casetype}
                                                       onchange={handlecasetypechange}></lightning-input-field>
                                <lightning-input-field field-name="Closure_Resolution__c" value={clresolution}
                                                       onchange={handleClosureResolChange}></lightning-input-field>
                            </span>
                        </div>
                        <div class="slds-col slds-size_1-of-3">
                            <span>
                                <lightning-input-field field-name="Repercussions__c" value={repercussions}
                                                       onchange={handlerepercussionschange}></lightning-input-field>
                                <lightning-input-field field-name="Received_From__c" value={receivedfrom}
                                                       onchange={handlereceivedfromchange}></lightning-input-field>
                                <lightning-input-field field-name="Received_Date__c" value={receiveddate}
                                                       onchange={handlereceiveddatechange}></lightning-input-field>
                            </span>
                        </div>
                    </div>
                </lightning-accordion-section>
            </lightning-accordion>  
        </lightning-record-edit-form>
        <div class="slds-grid slds-wrap slds-align_absolute-center slds-p-around_xx-small" style="margin-bottom:165px">
            <lightning-button class="slds-p-around_xx-small" icon-name="utility:trending"
                              onclick={savewo} label="Save" variant="brand">
            </lightning-button>
        </div>
    </div>
</template>
<!--<template>
    <lightning-accordion allow-multiple-sections-open
                            onsectiontoggle={handleSectionToggle}
                            active-section-name={activeSections}>
        <div>
            <lightning-record-view-form
                record-id={recordId}
                object-api-name={objectApiName}
                record-type-id={worecordtype}>
                <div class="slds-grid slds-gutters" style="padding-top: 10px">
                    <div class="slds-col slds-size_1-of-2">
                        <span>
                            <lightning-output-field field-name="StartDate" value={startdate}></lightning-output-field>
                            <lightning-output-field field-name="EndDate" value={enddate}></lightning-output-field> 
                            <lightning-output-field field-name="Priority" value={priority}></lightning-output-field> 
                            <lightning-output-field field-name="Subject"></lightning-output-field>
                        </span>
                    </div>
                    <div class="slds-col slds-size_1-of-2">
                        <span>
                            <lightning-output-field field-name="Status" value={status}></lightning-output-field>
                            <lightning-output-field field-name="Top_Level_Asset__c"></lightning-output-field>  
                            <lightning-output-field field-name="AssetId" value={assetid}></lightning-output-field>       
                        </span>
                    </div>
                </div>
            </lightning-record-view-form>
            <lightning-record-form
                record-id={recordId}
                object-api-name={objectApiName}
                fields={workorder}
                columns="2">
            </lightning-record-form>
        </div>  

        <lightning-accordion-section name="SummarySection" label="Comments">
            <div>
                <lightning-record-form
                    record-id={recordId}
                    object-api-name={objectApiName}
                    fields={summary}
                    columns="1">
                </lightning-record-form>
            </div>  
        </lightning-accordion-section>

        <lightning-accordion-section name="ServiceSection" label="Service Section">
            <div>
                <lightning-record-form
                    record-id={recordId}
                    object-api-name={objectApiName}
                    fields={servicesection}
                    columns="2">
                </lightning-record-form>
            </div>  
        </lightning-accordion-section>

        <lightning-accordion-section name="Reschedule" label="Reschedule/Followup & notes">
            <div>
                <lightning-record-form
                    record-id={recordId}
                    object-api-name={objectApiName}
                    fields={reschedule}
                    onsuccess={complaintupdate}
                    columns="1">
                </lightning-record-form>
            </div> 
        </lightning-accordion-section>
        
        <lightning-accordion-section name="Complaint" label="Complaint, PHI and Repercussions">
            <div>
                <lightning-record-form
                    record-id={recordId}
                    object-api-name={objectApiName}
                    fields={complaint}
                    onsuccess={complaintupdate}
                    columns="3">
                </lightning-record-form>
            </div> 
        </lightning-accordion-section>-->
<!--<lightning-accordion-section name="Complaint" label="Complaint, PHI and Repercussions">
            <div>
                <lightning-record-edit-form
                    record-id={recordId}
                    object-api-name={objectApiName}
                    onsuccess={complaintupdate}>
                    <div class="slds-grid slds-gutters" style="padding-top: 10px">
                        <div class="slds-col slds-size_1-of-2">
                            <span>
                                <lightning-input-field field-name="Complaint__c"
                                onchange={handlepurposeofphichange} disabled></lightning-input-field> 
                                <lightning-input-field field-name="Injured__c"
                                onchange={handlemethodofreceiptchange}></lightning-input-field>  
                                <lightning-input-field field-name="ECF_Required__c"
                                onchange={handledataformatchange}></lightning-input-field> 
                            </span>
                        </div>
                    </div>

                </lightning-record-edit-form>
            </div> 
        </lightning-accordion-section>-->
<!--</lightning-accordion>
</template>-->
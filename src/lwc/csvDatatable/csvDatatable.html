<!--
/**
 * BSD 3-Clause License
 *
 * Copyright (c) 2021, <EMAIL>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * - Redistributions of source code must retain the above copyright notice, this
 *   list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 *
 * - Neither the name of the copyright holder nor the names of its
 *   contributors may be used to endorse or promote products derived from
 *   this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
-->

<template>
  <c-message-service boundary={uniqueBoundary}></c-message-service>

  <div class="slds-is-relative">
    <template if:true={showSpinner}>
      <lightning-spinner alternative-text="Loading"></lightning-spinner>
    </template>
    <c-soql-datatable
      title={title}
      show-record-count={showRecordCount}
      checkbox-type="Multi"
      editable-fields={editableFields}
      sortable-fields={sortableFields}
      sorted-by={sortedBy}
      sorted-direction={sortedDirection}
      action-config-dev-name={actionConfigDevName}
      lookup-config-dev-name={lookupConfigDevName}
      use-relative-max-height={useRelativeMaxHeight}
      custom-relative-max-height={customRelativeMaxHeight}
      use-load-style-hack-for-overflow={useLoadStyleHackForOverflow}
    >
      <div slot="composedActions">
        <lightning-layout>
          <div class="slds-form-element">
            <div class="slds-form-element__control">
              <input
                type="file"
                class="slds-file-selector__input slds-assistive-text"
                accept=".csv"
                onclick={handleFileInputClicked}
                onchange={handleFileInputChanged}
                disabled={isFileInputDisabled}
                id={uniqueBoundary}
                aria-labelledby="file-selector-secondary-label"
              />
              <label class="slds-file-selector__body" for={uniqueBoundary} id="file-selector-secondary-label">
                <span class="slds-file-selector__button slds-button slds-button_neutral">
                  <lightning-icon
                    icon-name="utility:upload"
                    class="slds-button__icon slds-button__icon_left"
                    size="xx-small"
                    aria-hidden="true"
                  ></lightning-icon>
                  Select CSV
                </span>
              </label>
            </div>
          </div>
          <lightning-button
            class="slds-p-left_x-small"
            label="Apply CSV Values"
            onclick={handleApplyValuesClick}
            disabled={isApplyValuesDisabled}
          ></lightning-button>
        </lightning-layout>
      </div>
    </c-soql-datatable>
  </div>
</template>
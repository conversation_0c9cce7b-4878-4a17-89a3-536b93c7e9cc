<!-- sldsValidatorIgnore -->
<template>
    <div class="slds-page-header slds-var-m-bottom_x-small">
        <div class="slds-page-header__row">
            <div class="slds-page-header__col-title">
                <div class="slds-media">
                    <div class="slds-media__body">
                        <div class="slds-page-header__name">
                            <div class="slds-page-header__name-title">
                                <h1>
                                    <span class="slds-page-header__title slds-truncate" title="Chow Overview">Chow Overview</span>
                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <lightning-card>
        <lightning-layout multiple-rows="true">
            <lightning-layout-item size="3" padding="around-small">
                <div class="slds-grid slds-grid_vertical">
                    <div class="slds-col slds-border_bottom slds-p-bottom_small">
                        <div class="slds-grid slds-gutters">
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-combobox label="Geo Region"
                                                    value={selectedGeoRegion}
                                                    placeholder="Select Geo Region"
                                                    options={geoRegionOptions}
                                                    onchange={handleGeoRegionChange}
                                                    required="true"
                                                    class="requiredField">
                                </lightning-combobox>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-combobox label="Active Team"
                                                    value={selectedActiveTeam}
                                                    placeholder="Select Active Team"
                                                    options={activeTeamOptions}
                                                    onchange={handleActiveTeamChange}
                                                    required="true"
                                                    class="requiredField">
                                </lightning-combobox>
                            </div>
                        </div>
                    </div>
                    <div class="slds-col">
                        <lightning-dual-listbox label="Select Request Status"
                                   source-label="Available"
                                   selected-label="Selected"
                                   options={picklistRequestStatusOptions}
                                   value={selectedRequestStatusOptions}
                                   size="2"
                                   onchange={handleStatusChange}
                                   >
                        </lightning-dual-listbox>
                    </div>
                    <div class="slds-col">
                        <lightning-dual-listbox label="Select Priority Level"
                                   source-label="Available"
                                   selected-label="Selected"
                                   options={picklistPriorityOptions}
                                   value={selectedPriorityOptions}
                                   size="2"
                                   onchange={handlePriorityChange}>
                        </lightning-dual-listbox>
                    </div>
                    <div class="slds-col">
                        <lightning-dual-listbox label="Select Task Assignees"
                                   source-label="Available"
                                   selected-label="Selected"
                                   options={taskAssigneeList}
                                   value={selectedActiveTaskAssignees}
                                   size="2"
                                   onchange={handleAssigneeListChange}>
                        </lightning-dual-listbox>
                    </div>
                    <div class="slds-col slds-p-bottom_small">
                        <lightning-dual-listbox 
                         label="Select Order Type" 
                         source-label="Available" 
                         selected-label="Selected" 
                         options={picklistQuoteOrderTypes} 
                         value={selectedQuoteOrderTypes} 
                         size="2" 
                         onchange={handleOrderTypeChange}>
                        </lightning-dual-listbox>
                    </div>
                    <div class="slds-col">
                        <lightning-button variant="brand" label="Search" onclick={handleSearch} class="slds-align_absolute-center">Search</lightning-button>
                    </div>
                </div>
            </lightning-layout-item>
            <lightning-layout-item size="9" padding="around-small">
                <div class="slds-border_left">
                    <template if:true={chowRecords}>
                        <div class="tableFixHead  slds-p-right_small">
                            <table class="slds-table slds-table_bordered slds-table_col-bordered slds-table_fixed-layout">
                                <thead>
                                    <tr class="slds-text-title_caps">
                                        <th scope="col" class="slds-text-align_center" width="12.5%">
                                            <div class="slds-cell-wrap">Quote</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="6.25%">
                                            <div class="slds-cell-wrap">Chow Number</div>                                  
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="12.5%">
                                            <div class="slds-cell-wrap">Chow Title</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="12.5">
                                            <div class="slds-cell-wrap">Sold To Account</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="6.25%">
                                            <div class="slds-cell-wrap">Country</div>
                                        </th>
                                        <th data-id="reqDate" scope="col" class="slds-text-align_center" width="12.5%" onclick={sortDate}>
                                            <a href="javascript:void(0);" class="slds-th__action slds-text-link--reset">
                                                <span class="slds-assistive-text">Sort</span>
                                                <span class="slds-truncate" title="Requested Date">Requested Date </span>
                                                <div if:true={isDateSort}>
                                                    <div if:true={isAsc}>
                                                        &#9650;
                                                    </div>
                                                    <div if:true={isDsc}>
                                                        &#9660;
                                                    </div>                                                  
                                                </div>
                                            </a> 
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="12.5%">
                                            <div class="slds-cell-wrap">Task Assignee</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center slds-truncate"  width="6.25%">
                                            <div class="slds-cell-wrap">Days Chow is Open</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="6.25%">
                                            <div class="slds-cell-wrap">Chow Priority</div>
                                        </th>
                                        <th scope="col" class="slds-text-align_center" width="12.5%">
                                            <div class="slds-cell-wrap">Chow Status</div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template for:each={displayedChowRecords} for:item="chow" for:index="rowIndex">
                                        <tr key={chow.Id}>
                                            <td class="slds-text-align_center slds-truncate">
                                                <div class="slds-cell-wrap">
                                                    <lightning-formatted-url value={chow.QuoteLinkName} label={chow.Quote_Number} target="_blank"></lightning-formatted-url>
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center slds-truncate">
                                                <div class="slds-cell-wrap">
                                                    <lightning-formatted-url value={chow.linkChowName} label={chow.Name} target="_blank"></lightning-formatted-url>
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center chowTitle">
                                                <div class="slds-cell-wrap">
                                                    {chow.Title}
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center slds-truncate">
                                                <div class="slds-cell-wrap">
                                                    <lightning-formatted-url value={chow.soldToAccountLink} label={chow.soldToAccountName} target="_blank"></lightning-formatted-url>
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center slds-truncate">
                                                <div class="slds-cell-wrap">
                                                    {chow.soldToAccountCountry}
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center slds-truncate">
                                                <div class="slds-cell-wrap">
                                                    <lightning-formatted-date-time value={chow.CreatedDate}
                                                                               year="numeric"
                                                                               day="2-digit"
                                                                               month="short"
                                                                               hour="2-digit"
                                                                               minute="2-digit">
                                                    </lightning-formatted-date-time>
                                                </div>
                                            </td>
                                            <td class="slds-text-align_center">
                                                <lightning-combobox variant="label-hidden" 
                                                                value={chow.Active_Task_Assignee__c} 
                                                                options={taskAssigneeList} 
                                                                data-original-value={chow.Active_Task_Assignee__c} 
                                                                data-record-id={chow.Id} 
                                                                onchange={handleAssigneeChange}
                                                                class="taskAssignee"
                                                                dropdown-alignment="left"
                                                                disabled={chow.isDisabled}>                                   
                                                </lightning-combobox>
                                            </td>
                                            <td class="slds-text-align_center">{chow.NumberDays}</td>
                                            <td class="slds-text-align_center">{chow.Priority_Level__c}</td>
                                            <td class="slds-text-align_center">
                                                <div class="slds-cell-wrap">
                                                    {chow.Request_Status__c}
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </template>
                    <div class="slds-grid slds-gutters slds-p-top_small">
                        <div class="slds-col">
                            <template if:true={isButtons}>
                                <div class="slds-align_absolute-center">
                                    <button type="submit" class="slds-button slds-button_success" onclick={handleSave}>Save</button>
                                    <button type="submit" class="slds-button slds-button_destructive" onclick={handleCancel}>Cancel</button>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </lightning-layout-item>
        </lightning-layout>
    </lightning-card>
</template>
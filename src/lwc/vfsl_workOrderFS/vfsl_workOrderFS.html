<!--
  @description       : 
  <AUTHOR> bikram.bebarta
  @group             : 
  @last modified on  : 08-24-2021
  @last modified by  : bikram.bebarta
-->

<template>
    <lightning-card title="Work Order Dashboard" icon-name="standard:work_order">
      
        <template if:true={isassigned}>
                <template if:true={userIsResource}>
                <template if:false={IsEndocareWOAndNonVISCase}>
                    <lightning-button label="Add Labor" slot="actions" class="slds-m-left_x-small buttonwrap"
                         icon-name="utility:file" variant="brand" onclick={openLwcLaborModal}></lightning-button>
                </template>
                <lightning-button label="Add Parts" slot="actions" class="slds-m-left_x-small buttonwrap"
                    icon-name="utility:apex_plugin" variant="brand" onclick={openLwcPartsModal}></lightning-button>
                <lightning-button label="Remove Parts" slot="actions" class="slds-m-left_x-small buttonwrap"
                    icon-name="utility:cut" variant="brand" onclick={openLwcRemovePartsModal}></lightning-button>
                <lightning-button label="Apply Tools" slot="actions" class="slds-m-left_x-small buttonwrap"
                    icon-name="utility:anchor" variant="brand" onclick={openmodaltools}></lightning-button>
            </template>
            <template if:false={userIsResource}>
                <lightning-formatted-text slot="actions" value="Actions Hidden as User is Not the Service Resource"></lightning-formatted-text>
            </template>
        </template>
        <lightning-tabset variant="vertical">
            <lightning-tab name="WorkOrderSummary" label="Summary">
                    <div>
                        <c-lwc_work-order-summary object-api-name={objectApiName} record-id={recordId}></c-lwc_work-order-summary>
                    </div>
            </lightning-tab>
            <lightning-tab name="EditWorkOrder" label="Work Order">
                <div>
                    <c-lwc_edit-work-order object-api-name={objectApiName} record-id={recordId}
                    onsavewo={updatesummarytab}></c-lwc_edit-work-order>
                </div>
            </lightning-tab>
            <lightning-tab name="LaborLines" label="Labor Lines">
                <div>
                    <c-lwc_work-order-line-labor-table object-api-name={objectApiName} record-id={recordId}
                    ondeletelabor={updatesummarytab}>
                    </c-lwc_work-order-line-labor-table>
                </div>
            </lightning-tab>
            <lightning-tab name="PartsLinesInstalled" label="Installed Parts">
                <div>
                    <c-lwc_work-order-line-parts-table object-api-name={objectApiName} record-id={recordId}
                    ondeleteparts={updatesummarytab}>
                    </c-lwc_work-order-line-parts-table>
                </div>
            </lightning-tab>
            <lightning-tab name="PartsLinesRemoved" label="Removed Parts">
                <div>
                    <c-lwc_work-order-line-parts-remove-table object-api-name={objectApiName} record-id={recordId}>
                    </c-lwc_work-order-line-parts-remove-table>
                </div>
            </lightning-tab>
            <lightning-tab name="Tools" label="Tools">
                <c-lwc_work-order-line-tools-table onaddtool ={refreshtable} object-api-name={objectApiName} record-id={recordId}>
                </c-lwc_work-order-line-tools-table>
            </lightning-tab>
        </lightning-tabset>

        <template if:true={openparts}>
            <div class="partsmodal" style="height: 400px">
                <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                    aria-describedby="modal-content-id-3" class="slds-modal_large slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <div class="slds-modal__content slds-p-around_x-small" id="modalcontentparts">
                            <c-lwc_work-order-add-parts object-api-name={objectApiName} record-id={recordId}
                                onclosefromparts={closemodal} onsaveparts={savepartsline} onrefreshsummary={updatesummarytab}>
                            </c-lwc_work-order-add-parts>
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button label="Cancel" variant="destructive" onclick={closemodal}></lightning-button>
                    </footer>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </template>


        <template if:true={opentools}>
            <div class="toolsmodal" style="height: 400px">
                <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                    aria-describedby="modal-content-id-4" class="slds-modal_large slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <header class="slds-modal__header">
                            <h2 id="modal-heading-04" class="slds-text-heading_medium slds-hyphenate">Add Tools</h2>
                        </header>
                        <div class="slds-modal__content slds-p-around_x-small" id="modalcontenttools">
                            <c-lwc_work-order-add-tools object-api-name={objectApiName} record-id={recordId}
                                onclosefromtools={closemodal} onsavetool={savelineitem}></c-lwc_work-order-add-tools>
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button label="Cancel" variant="destructive" onclick={closemodal}></lightning-button>
                    </footer>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </template>

        <template if:true={removeparts}>
            <div class="partsremovemodal" style="height: 400px">
                <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                    aria-describedby="modal-content-id-5" class="slds-modal_large slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <div class="slds-modal__content slds-p-around_x-small" id="modalcontentpartsremoval">
                            <c-lwc_work-order-add-removal-parts onsavepartsremoval={updateremovepartstable} object-api-name={objectApiName} record-id={recordId}
                            onclosefrompartsremoval={closemodal}>

                            </c-lwc_work-order-add-removal-parts>
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button label="Cancel" variant="destructive" onclick={closemodal}></lightning-button>
                    </footer>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </div>
        </template>

    </lightning-card>
</template>
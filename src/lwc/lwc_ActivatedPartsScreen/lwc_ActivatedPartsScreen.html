<!-- STRY0170742 - New Flow Screen to Add Radioactive field Input for ROLI -->
<template>

    <template if:true={isToolsCheckboxVisible}>
        <div class="ToolsNotAvailableHelpText">
            Only check ‘Radiation Safety Team Assistance Needed’ if unable to provide the requested data.
        </div><br/>
    </template>

    <div class="TableDiv">  
        <table>
            <tr>
                <th>Part Number</th>
                <th>Activation/Contamination Survey Meter</th>
                <th>Calibration due date of meter (day/month/year)</th>
                <th>Background measure</th>
                <th>Max Reading on any part</th>
                <th>Unit of Measure for Part & Background</th>
            </tr>
            <template for:each={wrapperPartsList} for:item="item" for:index="index">
                <tr key={item.ProductId}>
                    <td>
                        <lightning-input type="text" value={item.partNumber} variant="label-hidden" disabled></lightning-input>
                    </td>
                    <td>
                        <lightning-combobox
                        name="SurveyMeter" 
                        data-index={index}
                        variant="label-hidden" 
                        value={item.actSurveyMeter} 
                        options={smOptions}
                        onchange={handleSurveyMeterChange} ></lightning-combobox>
                    </td>
                    <td>
                        <lightning-input type="date" data-index={index} name="dueDate" value={item.calDueDate} onchange={handleDateChange} variant="label-hidden" ></lightning-input>
                    </td>
                    <td>
                        <lightning-input type="number" name="bmPart" 
                        value={item.bgMeasure} data-index={index} onchange={handleBMChange} 
                        step="0.1"
                        variant="label-hidden"   ></lightning-input>
                    </td>
                    <td>
                        <lightning-input type="number" name="readingPart" 
                        value={item.maxReading} data-index={index} onchange={handleRCChange} 
                        step="0.1"
                        variant="label-hidden"   ></lightning-input>
                    </td>
                    <td>
                        <lightning-combobox
                        name="UMPPart" 
                        variant="label-hidden" 
                        value={item.UOMPart} 
                        data-index={index}
                        options={umpOptions} 
                        onchange={handleUMPartChange} ></lightning-combobox>
                    </td>
                </tr>
            </template>
        </table>
    </div>

    <template if:true={isToolsCheckboxVisible}>
        <br/>
        <div class="ToolsNotAvailable">
            <lightning-input type="checkbox" label="Radiation Safety Team Assistance Needed" name="input1" value={toolsNotAvailable} onchange={handleToolsChange}></lightning-input>
        </div>
        <br/>
    </template>
</template>
<!--
  @Component Name     : lwcWorkOrderLineLaborTable.html
  @Description        : 
  <AUTHOR> <EMAIL>
  @Group              : 
  @Last Modified By   : <EMAIL>
  @Last Modified On   : 7/2/2019, 11:10:42 PM
  @Modification Log   : 
  ==============================================================================
  Ver         Date                     Author      		      Modification
  ==============================================================================
  1.0    5/30/2019, 4:16:47 PM   <EMAIL>     Initial Version
-->
<template>
    <template if:true={loaded}>
        <lightning-spinner alternative-text="Loading"></lightning-spinner>
    </template>
    <div style="height: 400px;">
        <lightning-datatable
            key-field="Id"
            data={data}
            columns={columns}
            onrowaction={handleRowAction}
            sorted-by={sortBy}
            sorted-direction={sortDirection}
            onsort={doSorting}>
        </lightning-datatable>
    </div>
    <!--<lightning-button label="Refresh Table" onclick={handlerefresh} class="slds-m-left_x-small buttonwrap" variant="brand"></lightning-button>-->  
    
    
    <template if:true={editmodal}>
        <div class="labormodal" style="height: 800px">
            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                aria-describedby="modal-content-id-2" class="slds-modal slds-fade-in-open">
                <template if:true={loaded}>
                    <lightning-spinner alternative-text="Loading"></lightning-spinner>
                </template>
                <div class="slds-modal__container" style="height: 800px">
                    <header class="slds-modal__header">
                        <!--<h2 id="modalheading" class="slds-text-heading_medium slds-hyphenate"></h2>-->
                        <p class="slds-text-heading_medium slds-hyphenate">{modalheading}</p>
                    </header>
                    <div class="slds-modal__content slds-p-around_x-small" id="modalcontentlabor">
                        <lightning-record-edit-form object-api-name="WorkOrderLineItem"
                            record-type-id={worecordtype} onsuccess={handlesuccesslabor}>
                            <lightning-messages></lightning-messages>
                            <div
                                class="slds-col slds-size_1-of-1 slds-medium-size_2-of-4 slds-p-left_xx-small slds-p-right_xx-small">
                                <!--<h2 style="margin-bottom:10px">Asset {assetname}</h2>-->
                                <lightning-input-field field-name="Installed_Removed_WOLI__c" value={parentWoliId} disabled></lightning-input-field>
                                <div>
                                    <lightning-button class="slds-p-around_xx-small" onclick={selectwoli} variant= "brand" label="Select WOLI">
                                    </lightning-button> 
                                </div>
                                <!--VPS Changes-->
                                <template if:false={vpsWO}>
                                    <lightning-input-field field-name="AssetId" value={assetid} disabled></lightning-input-field>
                                </template>
                                
                                <!--<div>
                                    <lightning-button class="slds-p-around_xx-small" onclick={selectasset} variant="brand" label="Select Asset">
                                    </lightning-button> 
                                </div> -->
                                <template if:false={vpsWO}>
                                    <lightning-input-field required field-name="Activity_Group__c" value={group}
                                    onchange={handlegroupchange}></lightning-input-field>
                                    <lightning-input-field required field-name="Activity_Type__c" value={type}
                                    onchange={handletypechange}></lightning-input-field> 
                                </template>

                                <template if:true={vpsWO}>
                                    <lightning-input-field required field-name="Activity_Group__c" value={group}
                                    onchange={handlegroupchange} disabled></lightning-input-field>
                                    <lightning-input-field required field-name="Activity_Type__c" value={type}
                                    onchange={handletypechange} disabled></lightning-input-field>
                                </template>
                                
                                <template if:true={stbvisible}>
                                    <lightning-input-field field-name="Mandatory_STB__c" value={stbid} disabled></lightning-input-field>
                                    <lightning-input-field field-name="Optional_STB__c" value={stbid} disabled></lightning-input-field>
                                    <!--<h2 style="margin-bottom:10px">Selected STB {stbname}</h2>-->
                                    <div>
                                        <lightning-button class="slds-p-around_xx-small" onclick={selectstb} variant="brand" label="Select STB">
                                        </lightning-button> 
                                    </div>
                                    <lightning-input-field required field-name="STB_Mod_Status__c" value={stbcomplete}
                                    onchange={handlestbcomplete}></lightning-input-field> 
                                </template>
                                <!--<template if:true={stbvisible}>
                                    <template if:true={ismandatory}>
                                        <lightning-input-field field-name="Mandatory_STB__c" value={mandatorystb}></lightning-input-field>
                                    </template>
                                    <template if:true={isoptional}>
                                        <lightning-input-field field-name="Optional_STB__c" value={optionalstb}></lightning-input-field> 
                                    </template>
                                    <template if:true={changevisible}>
                                        <div>
                                            <lightning-button class="slds-p-around_xx-small" onclick={changestb} label="Select STB">
                                            </lightning-button> 
                                        </div>
                                    </template>
                                    <template if:true={stbvaluechange}>
                                        <div>
                                            <lightning-radio-group name="StbSelection"
                                                label="STB Selection"
                                                options={options}
                                                value={stbvalue}
                                                type="button"
                                                onchange={handlestbchange}>
                                            </lightning-radio-group>
                                        </div>
                                        <template if:true={mandatorystbtable}>
                                            <div style="height:350px;">
                                                <lightning-datatable
                                                    key-field="Id"
                                                    data={stbData}
                                                    onrowselection={getselectedid}
                                                    columns={stbmandatorycolumn}>
                                                </lightning-datatable>
                                            </div>
                                        </template>
                                        <template if:true={optionalstbtable}>
                                            <div style="height: 350px;">
                                                <lightning-datatable
                                                    key-field="Id"
                                                    data={stbData}
                                                    onrowselection={getselectedid}
                                                    columns={stboptionalcolumn}>
                                                </lightning-datatable> 
                                            </div>
                                        </template>
                                        <div>
                                            <lightning-button class="slds-p-around_xx-small" onclick={cancelstbchange} label="Cancel selection">
                                            </lightning-button> 
                                            </div>
                                    </template>                                 
                                    <lightning-input-field required field-name="STB_Mod_Status__c" value={stbcomplete}
                                    onchange={handlestbcomplete}></lightning-input-field>  
                                </template>-->

                                <template if:true={methodvisible}>
                                    <lightning-input-field required field-name="Method__c" value={method}
                                        onchange={handlemethodchange}></lightning-input-field> 
                                   <!-- <lightning-input-field required field-name="Delays__c" value={delays}
                                        onchange={handledelayschange}></lightning-input-field> 
                                    <lightning-input-field required field-name="Cause__c" value={cause}
                                        onchange={handlecausechange}></lightning-input-field> 
                                    <lightning-input-field field-name="Lost_Time__c" value={losttime}
                                        onchange={handlelosttimechange}></lightning-input-field>-->     
                                </template>     

								<!-- STRY0124142 - Start - Add Delay, Symptom, Cause field on Installation Activity Group -->

                                <!--VPS Changes-->
                                <template if:false={vpsWO}>
                                    <template if:true={activityGroupInstallation}>
                                        <lightning-input-field field-name="Delays__c" value={delays} 
                                            onchange={handledelayschange}></lightning-input-field>
                                        <lightning-input-field field-name="Symptom_Error_Code__c" value={sec}
                                            onchange={handlesecchange}></lightning-input-field>
                                            <lightning-input-field field-name="Cause__c" value={cause}
                                            onchange={handlecausechange} ></lightning-input-field>
                                    </template>
                                </template>
                                
								<!-- STRY0124142 - End - Add Delay, Symptom, Cause field on Installation Activity Group -->

                                <!--VPS Changes-->
                                <template if:false={vpsWO}>
                                    <lightning-input-field field-name="Holiday_Labor__c" value={holidayLabor}
                                    onchange={handleholidaylaborchange}></lightning-input-field> 
                                </template>
                                
                                                     
                            </div>
                            <div class="slds-grid slds-wrap">
                                <div class="slds-col slds-size_1-of-1 slds-medium-size_2-of-3 slds-p-left_xx-small">
                                    <lightning-input-field required field-name="StartDate" value={start}
                                        onchange={handlestartchange}></lightning-input-field>
                                    <lightning-input-field required field-name="EndDate" value={end}
                                        onchange={handleendchange}>
                                    </lightning-input-field>
                                </div>
                            </div>
                            <div
                                class="slds-col slds-size_1-of-1 slds-medium-size_2-of-4 slds-p-left_xx-small slds-p-right_xx-small">
                                <!--VPS Changes-->
                                <template if:true={vpsInternalOrder}>
                                    <lightning-combobox label="Job Order" value={jobOrder} placeholder="Select Job Order"
                                    options={jobOrderOptions} onchange={handleJobOrderChange} ></lightning-combobox>
                                </template>
                                <template if:false={vpsWO}>
                                    <template if:true={stborinstlvisible}>
                                        <lightning-input-field field-name="Lost_Time__c" value={losttime}
                                            onchange={handlelosttimechange}></lightning-input-field>
                                    </template>
                                    <lightning-input-field  field-name="SAP_Service_Order__c" value={sapserviceorder}
                                        onchange={handlesapserviceorderchange}></lightning-input-field>
                                    <lightning-input-field  field-name="On_Job_Training__c" value={ojt}
                                        onchange={handleojtchange}></lightning-input-field>    
                                </template>
                                
                            </div>
                            <template if:true={descvisible}>
                                    <lightning-input-field  field-name="Description" value={description}
                                    onchange={handledescriptionchange}></lightning-input-field>   
                            </template>
                            <br />
                            <div class="slds-grid slds-wrap slds-align_absolute-center">
                                <template if:true={isedit}>
                                    <lightning-button class="slds-p-around_xx-small" icon-name="utility:record_update"
                                    onclick={handleupdate} label="Update" variant="brand"></lightning-button>
                                </template>
                                <template if:true={isclone}>
                                        <lightning-button class="slds-p-around_xx-small" icon-name="utility:record_update"
                                        onclick={handleclone} label="Save" variant="brand"></lightning-button>
                                </template>
                                
                                <lightning-button class="slds-p-around_xx-small" icon-name="utility:close"
                                    label="Cancel" variant="destructive" onclick={handlecancel}></lightning-button>
                            </div>
                        </lightning-record-edit-form>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>

    <template if:true={assetmodal}>
        <div class="assetassetmodal" style="height: 800px">
            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                aria-describedby="modal-content-id-2" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="height: 800px">
                    <header class="slds-modal__header">
                        <h2 id="modal-heading-06" class="slds-text-heading_medium slds-hyphenate">Select Asset</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_x-small" id="assetcontentmodal">
                        <lightning-layout-item size="4">
                            <div class="slds-size_2-of-4 slds-p-left_xx-small" style="margin-left: 20px;">
                                <lightning-input type="search"
                                    class="slds-m-bottom_small"
                                    label="Name of Asset to search"
                                    placeholder="Asset to Search"
                                    value={assetsearch}
                                    onchange={checkasset}
                                    onkeyup={handleassetsearch}>
                                </lightning-input>
                                <div class="slds-size_1-of-4 slds-p-left_xx-small" style="margin-bottom: 10px;">
                                    <lightning-button label="Search" onclick={selectasset}></lightning-button>
                                </div>
                            </div>
                        </lightning-layout-item>
                        <div class="slds-col slds-size_1-of-1 slds-p-left_xx-small slds-p-right_xx-small">
                            <lightning-datatable
                                key-field="Id"
                                data={assetdata}
                                onrowselection={getselectedasset}
                                columns={assetcolumn}
                                max-row-selection= "1">
                            </lightning-datatable>      
                            
                        </div>
                        <br />
                        <div class="slds-grid slds-wrap slds-align_absolute-center">
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:save"
                                onclick={handleassetselect} label="Select" variant="brand">
                            </lightning-button>
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:close"
                                label="Cancel" variant="destructive" onclick={handleassetcancel}></lightning-button>
                        </div>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>

    <template if:true={stbmodal}>
        <div class="stbmodal" style="height: 800px">
            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                aria-describedby="modal-content-id-2" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="height: 800px">
                    <header class="slds-modal__header">
                        <h2 id="modal-heading-07" class="slds-text-heading_medium slds-hyphenate">Select STB</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_x-small" id="stbcontentmodal">
                            <div>
                                <lightning-radio-group name="StbSelection"
                                    label="STB Selection"
                                    options={options}
                                    value={stbvalue}
                                    type="button"
                                    onchange={handlestbchange}>
                                </lightning-radio-group>
                            </div>

                            <template if:true={mandatorystbtable}>
                                <lightning-layout-item size="4">
                                    <div class="slds-size_2-of-4 slds-p-left_xx-small" style="margin-left: 20px;">
                                        <lightning-input type="search"
                                            class="slds-m-bottom_small"
                                            label="Name of Mandatory STB"
                                            placeholder="Mandatory STB to Search"
                                            value={manstbsearch}
                                            onchange={checkmanstbsearch}
                                            onkeyup={handlemanstbsearch}>
                                        </lightning-input>
                                        <div class="slds-size_1-of-4 slds-p-left_xx-small" style="margin-bottom: 10px;">
                                            <lightning-button label="Search" onclick={selectmandatorystb}></lightning-button>
                                        </div>
                                    </div>
                                </lightning-layout-item>
                                <div style="height:350px;">
                                    <lightning-datatable
                                        key-field="Id"
                                        data={stbData}
                                        onrowselection={getselectedid}
                                        columns={stbmandatorycolumn}
                                        max-row-selection= "1">
                                    </lightning-datatable>
                                </div>
                            </template>

                            <template if:true={optionalstbtable}>
                                <lightning-layout-item size="4">
                                    <div class="slds-size_2-of-4 slds-p-left_xx-small" style="margin-left: 20px;">
                                        <lightning-input type="search"
                                            class="slds-m-bottom_small"
                                            label="Name of Optional STB"
                                            placeholder="Optional STB to Search"
                                            value={optstbsearch}
                                            onchange={checkoptstbsearch}
                                            onkeyup={handleoptstbsearch}>
                                        </lightning-input>
                                        <div class="slds-size_1-of-4 slds-p-left_xx-small" style="margin-bottom: 10px;">
                                            <lightning-button label="Search" onclick={selectoptionalstb}></lightning-button>
                                        </div>
                                    </div>
                                </lightning-layout-item>
                                <div style="height: 350px;">
                                    <lightning-datatable
                                        key-field="Id"
                                        data={stbData}
                                        onrowselection={getselectedid}
                                        columns={stboptionalcolumn}
                                        max-row-selection= "1">
                                    </lightning-datatable>
                                </div>
                            </template>

                            <!--<div>
                                <lightning-radio-group name="Stbcomplete"
                                    label="STB Complete"
                                    options={isstbcomplete}
                                    onchange={handlestbcomplete}>
                                </lightning-radio-group>
                            </div>-->
                            
                        <br />
                        <div class="slds-grid slds-wrap slds-align_absolute-center">
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:save"
                                onclick={handlestbselect} label="Select" variant="brand">
                            </lightning-button>
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:close"
                                label="Cancel" variant="destructive" onclick={handlestbcancel}></lightning-button>
                        </div>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>
    <template if:true={wolimodal}>
        <div class="wolimodal" style="height: 800px">
            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                aria-describedby="modal-content-id-2" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="height: 800px">
                    <header class="slds-modal__header">
                        <h2 id="modal-heading-17" class="slds-text-heading_medium slds-hyphenate">Select Work Order Line Item</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_x-small" id="wolicontentmodal">
                        <lightning-layout-item size="4">
                            <div class="slds-size_2-of-4 slds-p-left_xx-small" style="margin-left: 20px;">
                                <lightning-input type="search"
                                    class="slds-m-bottom_small"
                                    label="Name of Work Order Line Item to search"
                                    placeholder="Work Order Line Item to Search"
                                    value={wolisearch}
                                    onchange={checkwoli}
                                    onkeyup={handlewolisearch}>
                                </lightning-input>
                                <div class="slds-size_1-of-4 slds-p-left_xx-small" style="margin-bottom: 10px;">
                                    <lightning-button label="Search" onclick={selectwoli}></lightning-button>
                                </div>
                            </div>
                        </lightning-layout-item>
                        <div class="slds-col slds-size_1-of-1 slds-p-left_xx-small slds-p-right_xx-small">
                            <lightning-datatable
                                key-field="Id"
                                data={wolidata}
                                onrowselection={getselectedwoli}
                                columns={wolicolumn}
                                max-row-selection= "1">
                            </lightning-datatable>      
                        </div>
                        <br />
                        <div class="slds-grid slds-wrap slds-align_absolute-center">
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:save"
                                onclick={handlewoliselect} label="Select" variant="brand">
                            </lightning-button>
                            <lightning-button class="slds-p-around_xx-small" icon-name="utility:close"
                                label="Cancel" variant="destructive" onclick={handlewolicancel}></lightning-button>
                        </div>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </template>
</template>
import { LightningElement, api, wire, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent'

import getAttachments from '@salesforce/apex/emailProposalCtrl.getAttachments';
import getMostRecentAttachment from '@salesforce/apex/emailProposalCtrl.getMostRecentAttachment';
import getContacts from '@salesforce/apex/emailProposalCtrl.getContacts';
import getTrainingCoordinator from '@salesforce/apex/emailProposalCtrl.getTrainingCoordinator';
import getContactWrapperList from '@salesforce/apex/emailProposalCtrl.getContactWrapperList';
import sendEmail from '@salesforce/apex/emailProposalCtrl.sendEmail';
import getCoordinator from '@salesforce/apex/ProposalUtility.getCoordinator';

export default class EmailProposal extends NavigationMixin(LightningElement) {
    @api proposalId;
    attachmentList;
    contactsList;
    emailSubject;
    emailBody;
    
    primaryContactName = '';
    primaryContactEmail = '';
    selectedAttachments = [];
    selectedContacts = [];

    sendToList = [];
    CoordList = [];
    includeSelf = false;
    additionalEmails = '';
    sendAttachmentList = [];

    contactWrapperList;
    trainingCoordList;
    
    trainingCoordName;
    trainingCoordEmail;
    trainingCoordTitle; 
    trainingCoordPhone;
    trainingCoordExt;

    currentUser;
    mostRecentAttachmentId;

    @track contactsColumn = [
        {label: 'Name', fieldName: 'Name', type:'text'},
        {label: 'Email', fieldName: 'Email', type:'text'}
    ];

    @track attachmentsColumn = [
        {label: 'File Name', fieldName: 'Name', type:'text'},
        {label: 'Last Modified', fieldName: 'LastModifiedDate', type:'date'}
    ]

    @wire(getAttachments, {proposalId:'$proposalId'})
    wiredAttachment(result){
        if (result.data){
            //console.log('AL Wire result ' + JSON.stringify(result.data.Attachments));
            this.attachmentList = result.data.Attachments;
        }   
            else if (result.error) {
            this.error = result.error;
            this.data = undefined;
            console.log('AL WIre result fail' + JSON.stringify(this.error));
        }
    }

    @wire(getMostRecentAttachment, {proposalId:'$proposalId'})
    wiredRecentAttachment(result){
        if (result.data){
            console.log('AL Wire result ' + JSON.stringify(result.data.Attachments));
            this.mostRecentAttachmentId = result.data.Attachments[0].Id;
            console.log(this.mostRecentAttachmentId);
        }   
            else if (result.error) {
            this.error = result.error;
            this.data = undefined;
            console.log('AL WIre result fail' + JSON.stringify(this.error));
        }
    }


    @wire(getContacts, {proposalId:'$proposalId'})
    wiredContacts(result){
        if (result.data){
            this.trainingCoordName = result.data.Coordinator__r.Name;
            this.trainingCoordEmail = result.data.Coordinator__r.OCSUGC_Email__c;
            this.trainingCoordTitle = result.data.Coordinator__r.Title;
            this.trainingCoordPhone = result.data.Coordinator__r.Phone;
            this.trainingCoordExt = result.data.Coordinator__r.Extension;
            this.buildEmailSubject(result.data.Account__r.Name);
            this.buildEmailBody(this.primaryContactName, this.trainingCoordName, this.trainingCoordTitle, this.trainingCoordEmail, this.trainingCoordPhone, this.trainingCoordExt);
        }   
            else if (result.error) {
            this.error = result.error;
            this.data = undefined;
            console.log('AL WIre result fail' + JSON.stringify(this.error));
        }
    }
    /*
    @wire(getTrainingCoordinator, {proposalId:'$proposalId'})
    wiredTrainingCoord(result){
        if (result.data){
            console.log('wrapper suc');
            console.log(result.data);
            this.trainingCoordList = result.data;

        }
        else if (result.error) {
            this.error = result.error;
            this.data = undefined;
            console.log('con wrapper wire result fail' + JSON.stringify(this.error));
        }
    }
*/
    @wire(getContactWrapperList, {proposalId:'$proposalId'})
    wiredContactWrapper(result){
        if (result.data){
            console.log('wrapper suc');
            console.log(result.data);
            this.contactWrapperList = result.data;

        }
        else if (result.error) {
            this.error = result.error;
            this.data = undefined;
            console.log('con wrapper wire result fail' + JSON.stringify(this.error));
        }
    }

    @wire(getCoordinator, {userId: ''})
    wiredCurrentUser(result){
        if(result.data){
            console.log(result.data);
            this.currentUser = result.data;
        }
    }

    buildEmailSubject(accName){
        this.emailSubject = "Varian Training Proposal For " + accName;
    }

    buildEmailBody(clientName, coordName, coordTitle, coordEmail, coordPhone, coordExt){
        this.emailBody = "<p>Dear " + clientName  + ",<br></br></p>"
        +   "<p>Attached is your customized Training Proposal for your Department.</p>"
        +   "<p>Your proposal provides all Standard Training included with the product(s) purchased and any additional training events associated with the order.</p>"
        +   "<p>The proposal is a comprehensive planning guide outlining your training events with key details.</p>"
        +   "<p>In addition, the proposal includes our information on our general training policy and delivery process.</p>"
        +   "<p>For questions regarding the registration process for off-site training, please contact our registrar team via <NAME_EMAIL> or by phone at 1-888-Varian5 (**************), option 3, then option 2.</p>"
        +   "<p>Please take a moment to review to ensure the proposal aligns with your expectations. Please let me know if any changes are required.<br></br><br></br></p>"
        +   "<p>Best Regards,<br></br></p>"
        +   "<p>" + coordName +"</p>"
        +   "<p>" + coordTitle+"</p>"
        +   "<p>" + coordPhone+"</p>"
        //+   "<p>" + coordExt+"</p>"
        +   "<p>" + coordEmail+"</p>";
    }

    cancelEmail(){
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: '/lightning/n/Create_Proposal'
            }
        })
    }

    selectAttachments(event){
        this.selectedAttachments = event.detail.selectedRows;
        console.log(this.selectedAttachments);
        this.sendAttachmentList = [];

        for (var i = 0; i < this.selectedAttachments.length; i++){
            console.log(this.selectedAttachments[i]);
            this.sendAttachmentList.push(this.selectedAttachments[i].Id);
        }

        console.log(this.sendAttachmentList);
    }

    selectCoord(event){
        this.CoordList = event.detail.selectedRows;
    }

    selectContact(event){
        console.log(event.detail.selectedRows[0]);
        this.primaryContactEmail = event.detail.selectedRows[0].Email;
        this.primaryContactName = event.detail.selectedRows[0].Name;
        this.buildEmailBody(this.primaryContactName, this.trainingCoordName, this.trainingCoordTitle, this.trainingCoordEmail, this.trainingCoordPhone, this.trainingCoordExt);
    }
    
    emailBodyChange(event){
        this.emailBody = event.detail.value;
    }

    emailSubjectChange(event){
        this.emailSubject = event.detail.value
    }

    selectAdditionalEmail(event){
        this.additionalEmails = event.detail.value;
    }

    handleIncludeSelf(event){
        console.log(event.target.checked);

        this.includeSelf = event.target.checked;
    }

    sendEmail(){

        var ccList = [];

        console.log(this.contactWrapperList);

        for (var i = 0; i < this.contactWrapperList.length; i++){
            ccList.push(this.contactWrapperList[i].Email);
        }
        if (this.additionalEmails.length > 0){
            ccList = ccList.concat(this.additionalEmails.split(";"));
        }
        

        if (this.includeSelf === true){
            ccList.push(this.currentUser.Email);
        }

        console.log(this.primaryContactEmail);
        console.log(ccList);
        
        
        sendEmail({ toAddress: this.primaryContactEmail,
                    ccAddress: ccList,
                    replyToAddress: this.trainingCoordEmail,
                    subject: this.emailSubject,
                    body: this.emailBody,
                    attachmentId: this.mostRecentAttachmentId,
                    proposalId: this.proposalId
        }).then(result => {
                console.log(result);
                this.redirectToHomePage();
            }).catch(error => {
                console.log('error details:' + JSON.stringify(error));
                this.sendErrorToast();
            });
    }


    
    redirectToHomePage(){
        var compDefinition = {
            componentDef: "c:proposalHome"
        };
        var encodedCompDef = btoa(JSON.stringify(compDefinition));

        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: '/one/one.app#' + encodedCompDef
            }
        })
    }

    sendErrorToast() {
        const evt = new ShowToastEvent({
            title: 'Error',
            message: 'Check Email Addresses for Errors',
            variant: 'error'
        });
        this.dispatchEvent(evt);
    }

}
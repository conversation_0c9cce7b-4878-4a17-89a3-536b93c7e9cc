<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <isExposed>true</isExposed>

    <!-- * Assign a Label to show in the Custom Components section of the Flow Screen Editor -->
    <masterLabel>CP - Sort Collection</masterLabel>

    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen">

            <!-- * Define all of the input and output attributes needed to pass from the LWC to the Invocable Action  -->
            <propertyType name="T" extends="SObject" label="Object API Name" description="Select the API Name of the SObject for the Record Collections"/>
            <property name="inputCollection" type="{T[]}" role="inputOnly" label="Input Collection" description="An SObject collection. Be sure that it includes any field referenced in the Sort Keys." required="true"/>
            <property name="sortKeys" type="String" role="inputOnly" label = "Sort Keys" description="One or more comma separated sort keys formatted as [Field API Name]:[ASC|DESC]." required="true"/>
            <property name="outputCollection" type="{T[]}" role="outputOnly" label="Output Collection" description="Sorted record collection"/>                
            
            <!-- Include an attribute to hold an error message -->
            <property name="error" type="String" role="outputOnly" description="Error message when there is an error output from this component"/>

        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
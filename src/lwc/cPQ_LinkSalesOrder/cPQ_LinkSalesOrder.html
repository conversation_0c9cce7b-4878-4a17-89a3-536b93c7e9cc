<template>
    <lightning-card title="">
        <lightning-layout multiple-rows="true" vertical-align="end">
            <lightning-layout-item size="4" padding="around-small">
                 <!--Input text to Search Account-->
                <lightning-input type="text" label="Enter Account Name To Search"
                    value={accountsearchKey} 
                    onchange={handelSearchKeyAcc}> 
                </lightning-input>
            </lightning-layout-item >

            <lightning-layout-item size="2" padding="around-small">
                <!--Search Button-->
                <lightning-button label="Search" variant="brand" disabled={disableBtnAccSearch} onclick={SearchAccountHandler}>

                </lightning-button>     
            </lightning-layout-item>          

            <lightning-layout-item size="4" padding="around-small">
                 <!--Input text to Search Sales Order-->
                <lightning-input type="text" label="Enter Sales Order Number To Search"
                    value={salesOrdersearchKey} 
                    onchange={handelSearchKeySO}> 
                </lightning-input>
            </lightning-layout-item >
            <lightning-layout-item size="2" padding="around-small">
                <!--Search Button-->
                <lightning-button label="Search" variant="brand" disabled={disableBtnSOSearch} onclick={SearchSalesOrderHandler} >
                </lightning-button>
            </lightning-layout-item>            
            <template if:true={emptyresult}>
            <div>
            <h4 style="color:red;font-size:15px;margin-left: 20px;" > <b>{emptyresult}</b></h4>
            </div>
            </template>
            <lightning-layout-item size="12">
                <!--List Of Sales Order Using data Tabel-->
                <template if:true={salesOrders}>
                    <lightning-datatable 
                                max-row-selection="1"
                                key-field="id" data={salesOrders} columns ={cols}> 
                    </lightning-datatable>

                    <div class="slds-grid slds-grid_vertical-align-center slds-grid_align-spread">                       
                        <!--PAGE NAVIGATION-->
                        <div class="slds-align_absolute-center" style="height:5rem">
                            <lightning-button disabled={bDisableFirst} icon-name="utility:jump_to_left" label="First" class="slds-p-horizontal_x-small" alternative-text="first page" onclick={firstPage}></lightning-button>
                            <lightning-button disabled={bDisableFirst} icon-name="utility:chevronleft" label="Previous" alternative-text="Previous" onclick={previousPage}></lightning-button>
                            &nbsp;
                            <span class="slds-badge">Showing {pageNumber} &nbsp;of&nbsp; {totalPages} &nbsp;&nbsp;Page(s)</span>
                            &nbsp;
                            <lightning-button disabled={bDisableLast} icon-name="utility:chevronright" label="Next" alternative-text="Next" onclick={nextPage} class="slds-p-horizontal_x-small" icon-position="right"></lightning-button>
                            <lightning-button disabled={bDisableLast} icon-name="utility:jump_to_right" label="Last" alternative-text="last page" onclick={lastPage} icon-position="right"></lightning-button>
                        </div>
                        <!--TOTAL RECORDS-->
                        <div class="slds-clearfix">
                            <div class="slds-float_right">
                                <span class="slds-badge"> Total Records: {totalRecords}</span>
                            </div>
                        </div>
                    </div>
                </template>
            </lightning-layout-item>
        </lightning-layout>               
    </lightning-card>
    <template if:true={salesOrders}>
    <div align="right"> 
    <lightning-button   variant="brand"
                            label="Link Sales Order"
                            title="Link Sales Order"
                            onclick={getSelectedRec} 
                            slot="actions"
                            disabled={disableButton}
                            icon-name=""
                            >
        </lightning-button>
    </div>
    </template>
</template>
import { LightningElement, track, api } from 'lwc';
import checkIfCurrentUserIsTeamMember from '@salesforce/apex/CPO_AllPlanningRequestsPageController.checkIfCurrentUserIsTeamMember';
import getPlanningRequests from '@salesforce/apex/CPO_AllPlanningRequestsPageController.getPlanningRequests';
import getStatusPicklistOptions from '@salesforce/apex/CPO_AllPlanningRequestsPageController.getStatusPicklistOptions';
import getRegionPicklistOptions from '@salesforce/apex/CPO_AllPlanningRequestsPageController.getRegionPicklistOptions';
import getPlanningTeamPicklistOptions from '@salesforce/apex/CPO_AllPlanningRequestsPageController.getPlanningTeamPicklistOptions';
import getRequestingGeoOptions from '@salesforce/apex/CPO_AllPlanningRequestsPageController.getRequestingGeoOptions';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';


// Columns of Planning Requests Table
const columns = [
    {
        label: 'SPR#', fieldName: 'planningRequestURL', type: 'url',
        initialWidth: 90,
        typeAttributes: {
            label: {
                fieldName: 'planningRequestName'
            },
            target: '_blank'
        },  
        sortable: true,
        hideDefaultActions: true,
        wrapText: true,
    },
    {
        label: 'Account', fieldName: 'accountURL', type: 'url',
        initialWidth: 170,
        typeAttributes: {
            label: {
                fieldName: 'accountName'
            },
            target: '_blank'
        },
        hideDefaultActions: true,
        wrapText: true,
        sortable: true,
    },
    {
        label: 'Primary Opportunity', fieldName: 'primaryOpportunityURL', type: 'url',
        initialWidth: 220,
        typeAttributes: {
            label: {
                fieldName: 'primaryOpportunityName'
            },
            target: '_blank'
        },
        hideDefaultActions: true,
        wrapText: true,
        sortable: true,
    },
     {
        label: 'Country', fieldName: 'Country', type: 'text', 
        initialWidth: 100,
        hideDefaultActions: true,
        wrapText: true,
        sortable: true,
    },
    {
        label: 'City', fieldName: 'City', type: 'text',
        initialWidth:100,
        hideDefaultActions: true,
        wrapText: true,
        sortable: true,
    },
    {
        label: 'Request Date', fieldName: 'requestDate', type: 'text',
        initialWidth:100,
        hideDefaultActions: true,
        wrapText: true,
        sortable: true,
    },
    {
        label: 'Owner', fieldName: 'ownerURL', type: 'url',
        typeAttributes: {
            label: {
                fieldName: 'ownerName'
            },
            target: '_blank'
        },
        hideDefaultActions: true,
        initialWidth: 100,
        wrapText: true,
        sortable: true
    },
    {
        label: 'Associated Opps', fieldName: '', type: 'multipleUrl',
        hideDefaultActions: 'true',
        typeAttributes: { links: { fieldName: 'AssociatedOpportunities' } },
        initialWidth: 150,
        wrapText: true
    },

    {
        label: 'SPCM',
        fieldName: '',
        initialWidth: 60,
        hideDefaultActions: 'true',
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'SPCMTitle' }, iconName: { fieldName: 'SPCM' }, alignment: 'right' },
        cellAttributes: { alignment: 'right' },
    },
    {
        label: 'SWP',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'SWPTitle' }, iconName: { fieldName: 'SWP' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'SWUP',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'SWUPTitle' }, iconName: { fieldName: 'SWUP' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'HWUP',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'HWUPTitle' }, iconName: { fieldName: 'HWUP' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'IOP',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'IOPTitle' }, iconName: { fieldName: 'IOP' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'DBSP',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'DBSPTitle' }, iconName: { fieldName: 'DBSP' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'PS',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'PSTitle' }, iconName: { fieldName: 'PS' } },
        cellAttributes: { alignment: 'center' },
    },
    {
        label: 'AOS',
        fieldName: '',
        hideDefaultActions: 'true',
        initialWidth: 60,
        type: 'customIcon',
        typeAttributes: { iconTitle: { fieldName: 'AOSTitle' }, iconName: { fieldName: 'AOS' } },
        cellAttributes: { alignment: 'center' },
    },

];

export default class CPO_AllPlanningRequestsPage extends LightningElement {
    activeSections = ['FiltersSection'];
    columns = columns;
    allPlanningRequestsData = [];
    planningRequestsDataToShowInTable = [];
    allFilteredPlanningRequests = [];
    @track searchPlanningRequestValue = '';
    @track isFiltered = false;
    @track statusOptions = [];
    @track regionOptions = [];
    @track planningTeamOptions = [];
    @track selectedRequestingGeo = [];
    @api preselectedGeosvalues = [];//Preselected Geos Values to send in Multiselected Picklist
    @track selectedPRStatus = '';
    @track selectedRegion = '';
    @track selectedPlanningTeams = [];
    @track selectedOwnerId = '';



    //pagination 
    @track pageNumber = 1;
    @track recordSize = 15;
    @track recordSizeValue = 15;
    @track totalRecords = 0;
    @track totalPages = 0;
    @track navigationPages = [];
    isTableLoading = false;
    isTableLoaded = false;

    //Sorting
    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;


    get recordSizeOptions() {
        return [
            { label: '15', value: 15 },
            { label: '25', value: 25 },
            { label: '50', value: 50 },
            { label: '75', value: 75 },

        ]
    }


    get disablePreviousButtons() {
        //Allows us to disable the "First" and "Previous" buttons when it's needed(ie when we are on page 1)
        return this.pageNumber === 1;
    }

    get disableNextButtons() {
        //Allows us to disable the "Next" and "Last" buttons when it's needed(ie when we are on the last page for example)
        return (
            this.pageNumber === this.totalPages ||
            this.totalPages === 0
        );
    }



    connectedCallback() {
        this.searchPlanningRequestValue = '';

        getRequestingGeoOptions().then(result => {
            this.planningGeosOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });

        getStatusPicklistOptions().then(result => {
            this.statusOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });

        getPlanningTeamPicklistOptions().then(result => {
            this.planningTeamOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });

        checkIfCurrentUserIsTeamMember().then(result => {
            this.pageLoadedFirstTime = true;
            let memberGeo = [];
            this.preselectedGeosvalues = result;
            this.preselectedGeosvalues.forEach(option => {
                memberGeo.push(option);
            })
            this.selectedRequestingGeo = [...memberGeo];
            this.getPlanningRequestsData();
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });

        getRegionPicklistOptions({ geo: this.selectedRequestingGeo }).then(result => {
            this.regionOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });

    }

    getTableDataClientPagination() {
        //Set existing selected things to null here
        this.getPlanningRequestsData();
    }

    //Set Dynamic Icons based on the status value
     getDynamicIcon(statusValue) {
        // if (statusValue == -1) {
        //     return 'action:apex';
        // }
        // else if (statusValue == 0) {
        //     return 'action:fallback';
        // }
        // else if (statusValue != 0 && statusValue != 1 && statusValue != -1) {
        //     return 'action:new_note';
        // }
        // else if (statusValue == 1) {
        //     return 'action:approval';
        // }
         if(statusValue=="Completed"){
             return 'action:approval';
        }
        else if(statusValue=="Not Applicable"){
             //return 'action:apex';

             return 'utility:routing_offline';
        }
        else if(statusValue=="In Progress"){
            return 'action:new_note';
        }
        else if(statusValue=="New"){
            return 'action:fallback';
        }
        else if(statusValue=="Not Started"){
             return 'action:record';
        }
        else if(statusValue=="On Hold"){
             return 'action:new_custom12';
        }
        else if(statusValue=="Cancelled"){
            return 'action:remove';
        }
        else if(statusValue=="Waiting on Someone else"){
            return 'action:defer';
        }
        else{
            return 'action:remove';
        }

    }
  
    //Set Dynamic Icons based on the status value
    getDynamicIconTitle(name, statusValue) {
        // if (statusValue == -1) {
        //     return name + " : " + 'Not Applicable';
        // } else if (statusValue == 0) {
        //     return name + " : " + 'New';
        // } else if (statusValue != 0 && statusValue != 1 && statusValue != -1) {
        //     return name + " : " + 'In Progress';
        // } else if (statusValue == 1) {
        //     return name + " : " + 'Completed';
        // }
         if(statusValue=="Completed"){
             return name+" : "+'Completed';
        }
        else if(statusValue=="Not Applicable"){
             return name+" : "+'Not Applicable';
        }
        else if(statusValue=="In Progress"){
            return name+" : "+'In Progress';
        }
        else if(statusValue=="New"){
            return name+" : "+'New';
        }
        else if(statusValue=="Not Started"){
             return name+" : "+'Not Started';
        }
        else if(statusValue=="On Hold"){
            return name+" : "+'On Hold';
        }
        else if(statusValue=="Cancelled"){
            return name+" : "+'Cancelled';
        }
        else if(statusValue=="Waiting on Someone else"){
            return name+" : "+'Waiting on Someone else';
        }
        else{
            return name+" : Not Applicable"
        }
    }

    
    handleSearchPlanningRequestChange(event) {
        this.searchPlanningRequestValue = event.target.value;
        if (this.searchPlanningRequestValue === '') {
            this.isFiltered = false;
            this.getPlanningRequestsData();
            return;
        }
        this.allFilteredPlanningRequests = this.allPlanningRequestsData.filter(row => {
            return (row.planningRequestName.toLowerCase().includes(this.searchPlanningRequestValue.toLowerCase()) ||
                row.accountName.toLowerCase().includes(this.searchPlanningRequestValue.toLowerCase()) ||
                row.primaryOpportunityName.toLowerCase().includes(this.searchPlanningRequestValue.toLowerCase()) ||
                row.City.toLowerCase().includes(this.searchPlanningRequestValue.toLowerCase()));

        });

        if (this.allFilteredPlanningRequests.length > 0) {
            this.isFiltered = true;
            this.totalRecords = this.allFilteredPlanningRequests.length;
            this.pageNumber = 1;
            this.recordSize = 15;
            this.totalPages = Math.ceil(this.totalRecords / Number(this.recordSize));
            this.navigationPages = [...Array(this.totalPages + 1).keys()].slice(1).map((e) => {
                return {
                    label: String(e),
                    value: e
                }
            });
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, 1, this.recordSize);

        }
        else {
            this.toastMessage('Alert', 'Records not found!', 'error');
        }
    }

    handleGeoChange(event) {
        this.selectedRequestingGeo = event.detail;
        if (this.selectedRequestingGeo.length === 0) {
            this.selectAllPlanningTeamOptions();
        }
        getRegionPicklistOptions({ geo: this.selectedRequestingGeo }).then(result => {
            this.selectedRegion = '';
            this.regionOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - 000', error.body.message, 'error');
        });
    }

    handleRegionChange(event) {
        this.selectedRegion = event.target.value;
    }

    selectAllGeoOptions() {
        this.planningGeosOptions.forEach(option => {
            this.selectedPlanningTeams.push(option.value);
        })
    }

    //If All is selected as an option in Product Bundle Picklist, send all the values to search orderlist
    selectAllPlanningTeamOptions() {
        this.planningTeamOptions.forEach(option => {
            this.selectedPlanningTeams.push(option.value);
        })
    }

    handlePlanningTeamChange(event) {
        this.selectedPlanningTeams = event.detail;
        if (this.selectedPlanningTeams.length === 0) {
            this.selectAllPlanningTeamOptions();
        }
    }

    handleStatusChange(event) {
        this.selectedPRStatus = event.target.value;
    }

    handleOwnerChange(event) {
        if(event.target.lookupLabel == 'Owner')
        this.selectedOwnerId = event.detail.selectedRecordId;
    }

   
    handleSearch(event) {
        this.searchPlanningRequestValue = '';
        this.getPlanningRequestsData();
    }

    handleResetFilters(event) {
        this.selectedRequestingGeo = [];
        this.selectedRegion = '';
        this.selectedPRStatus = '';
        this.selectedPlanningTeams = [];
        this.selectedOwnerId = '';
        this.template.querySelector('c-multi-select-picklist-lwc').clear();
        this.template.querySelector('c-multi-select-picklist-with-preselected-options-lwc').clear();
        this.template.querySelector('c-lookup-web-component').removeRecordOnLookup();
        getRegionPicklistOptions({ geo: this.selectedRequestingGeo }).then(result => {
            this.selectedRegion = '';
            this.regionOptions = result;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });
    }

    getPlanningRequestsData() {
        this.isTableLoading = true;
        this.isTableLoaded=false;
        this.pageNumber = 1;
        this.recordSizeValue = 15;
        this.recordSize = 15;
        //Parameters to pass to Apex method
        let currentGeo = this.selectedRequestingGeo;
        let currentRegion = this.selectedRegion;
        let currentPlanningTeam = this.selectedPlanningTeams;
        let currentPRStatus = this.selectedPRStatus;
        let currentOwnerId = this.selectedOwnerId;
        //Calling Apex method
        getPlanningRequests({ geo: currentGeo, region: currentRegion, planningTeam: currentPlanningTeam, status: currentPRStatus, ownerId: currentOwnerId }).then(result => {
            let prTableData = result.planningRequestRecords.map((row) => {
                let associatedOpportunities;
                if (row.associatedOpportunitiesList != null && row.associatedOpportunitiesList.length > 0) {
                    associatedOpportunities = row.associatedOpportunitiesList.map(opportunity => ({
                        ...opportunity,
                        label: opportunity.opportunityName,
                        value: '/' + opportunity.opportunityId,
                    }));
                }
                return {
                    Id: row.pr.Id,
                    planningRequestURL: (row.pr.Id != null) ? '/lightning/n/CPO_PUBLIC_PAGE?c__id=' + row.pr.Id : '',
                    planningRequestName: (row.prName != null) ? row.prName : '',
                    accountURL: (row.acctid != null) ? '/' + row.acctid : '',
                    accountName: (row.acctName != null) ? row.acctName : '',
                    primaryOpportunityURL: (row.opptyId != null) ? '/' + row.opptyId : '',
                    primaryOpportunityName: (row.opptyName != null) ? row.opptyName : '',
                    City: (row.cityName != null) ? row.cityName : '',
                    Country: (row.countryName != null) ? row.countryName : '',
                    requestDate: (row.requestDate != null) ? row.requestDate : '',
                    ownerURL: (row.ownerId != null) ? '/' + row.ownerId : '',
                    ownerName: (row.ownerName != null) ? row.ownerName : '',
                    SPCM: this.getDynamicIcon(row.siteStatus),
                    SPCMTitle: this.getDynamicIconTitle("Site Planning", row.siteStatus),
                    SWP: this.getDynamicIcon(row.softStatus),
                    SWPTitle: this.getDynamicIconTitle("Software Planning", row.softStatus),
                    SWUP: this.getDynamicIcon(row.softpStatus),
                    SWUPTitle: this.getDynamicIconTitle("Software UG Planning", row.softpStatus),
                    HWUP: this.getDynamicIcon(row.hwupStatus),
                    HWUPTitle: this.getDynamicIconTitle("Upgrade Planning-Hardware", row.hwupStatus),
                    IOP: this.getDynamicIcon(row.IOPstatus),
                    IOPTitle: this.getDynamicIconTitle("Interoperability Planning", row.IOPstatus),
                    DBSP: this.getDynamicIcon(row.dbspStatus),
                    DBSPTitle: this.getDynamicIconTitle("Database Services Planning", row.dbspStatus),
                    PS: this.getDynamicIcon(row.osmStatus),
                    PSTitle: this.getDynamicIconTitle("Professional Services", row.osmStatus),
                    AOS: this.getDynamicIcon(row.aosStatus),
                    AOSTitle: this.getDynamicIconTitle("Advanced Oncology Solutions", row.aosStatus),
                    AssociatedOpportunities: associatedOpportunities
                }
            });
            this.totalRecords = result.totalRecords;
            this.totalPages = Math.ceil(result.totalRecords / Number(this.recordSize));
            this.navigationPages = [...Array(this.totalPages + 1).keys()].slice(1).map((e) => {
                return {
                    label: String(e),
                    value: e
                }
            });
            this.allPlanningRequestsData = JSON.parse(JSON.stringify(prTableData));
            this.allFilteredPlanningRequests = this.allPlanningRequestsData;
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, 1, this.recordSize);
            this.isTableLoading = false;
            this.isTableLoaded = true;
        }).catch(error => {
            this.toastMessage('Error Occurred - ', error.body.message, 'error');
        });
    }


    recordSizeChange(event) {
        this.recordSize = Number(event.detail.value);
        this.recordSizeValue = Number(event.detail.value);
        this.totalPages = Math.ceil(this.totalRecords / Number(this.recordSize));
        this.navigationPages = [...Array(this.totalPages + 1).keys()].slice(1).map((e) => {
            return {
                label: String(e),
                value: e
            }
        });
        this.pageNumber = 1;
        if (this.isFiltered === true) {
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, this.pageNumber, this.recordSize);
        }
        else {
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.pageNumber, this.recordSize);
        }
    }

    getPageNumberRecords(allRecords, pageNumber, recordSize) {
        const offset = (Number(pageNumber) - 1) * Number(recordSize);
        if (offset > allRecords.length) {
            this.toastMessage('Pagination Error', 'Selected Page Number and Record Size goes beyond Table Size', 'error');
            return;
        }
        return (allRecords).slice(offset, offset + Number(recordSize));
    }

    toastMessage(title, message, variant) {
        const evt = new ShowToastEvent({ title, message, variant });
        this.dispatchEvent(evt);
    }

    getPaginationData(event) {
        const buttonName = event.target.title;
        if (buttonName == 'First') {
            if (this.totalPages != 0) {
                this.pageNumber = 1;
                if (this.isFiltered === true) {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, 1, this.recordSize);
                }
                else {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, 1, this.recordSize);
                }

            }
        } else if (buttonName == 'Last') {
            if (this.totalPages != 0) {
                this.pageNumber = this.totalPages;
                if (this.isFiltered === true) {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, this.pageNumber, this.recordSize);
                }
                else {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.totalPages, this.recordSize);
                }
            }
        } else if (buttonName == 'Next') {
            if (this.pageNumber < this.totalPages) {
                this.pageNumber = Number(this.pageNumber) + 1;

                if (this.isFiltered === true) {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, this.pageNumber, this.recordSize);
                }
                else {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.pageNumber, this.recordSize);
                }
            }
        } else if (buttonName == 'Previous') {
            if (this.pageNumber > 1 && (this.totalPages != 0)) {
                this.pageNumber = Number(this.pageNumber) - 1;

                if (this.isFiltered === true) {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, this.pageNumber, this.recordSize);
                }
                else {
                    this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.pageNumber, this.recordSize);
                }
            }
        }
    }

    get rowNumberOffset() {
        return Number(this.recordSize) * (Number(this.pageNumber) - 1);
    }

    get recordViewMessage() {
        if (this.totalRecords != undefined && this.totalPages != undefined)
            return 'Total Records - ' + this.totalRecords + ' | Current Page - ' + this.pageNumber + '/' + this.totalPages + ' | Page Size - ' + this.recordSize;
        else
            return ''
    }

    navigateToPage(event) {
        this.pageNumber = Number(event.detail.value);
        if (this.isFiltered === true) {
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allFilteredPlanningRequests, this.pageNumber, this.recordSize);
        }
        else {
            this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.pageNumber, this.recordSize);
        }

    }

    sortBy(field, reverse) {
        this.allPlanningRequestsData = (this.allFilteredPlanningRequests).sort((a, b) => {
            let s1 = 0;
            if (field == "planningRequestURL") {
                s1 = (a.planningRequestName).localeCompare((b.planningRequestName), 'en');
            }
            if (field == "accountURL") {
                s1 = (a.accountName).localeCompare((b.accountName), 'en');
            }
            if (field == "primaryOpportunityURL") {
                s1 = (a.primaryOpportunityName).localeCompare((b.primaryOpportunityName), 'en');
            }
            if (field == "City") {
                s1 = (a.City).localeCompare((b.City), 'en');
            }
            if(field=="Country"){
            s1 = (a.Country).localeCompare((b.Country), 'en');
            }
            if (field == "ownerURL") {
                s1 = (a.ownerName).localeCompare((b.ownerName), 'en');
            }
            if (field == 'requestDate') {
                return (new Date(a.requestDate)) > (new Date(b.requestDate)) ? (reverse) * 1 : (reverse) * (-1);
            }
            if (s1 !== 0) {
                return ((reverse)) * s1;
            }
        });
        return this.allPlanningRequestsData;
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        this.allPlanningRequestsData = this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1)
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
        this.planningRequestsDataToShowInTable = this.getPageNumberRecords(this.allPlanningRequestsData, this.pageNumber, this.recordSize);
    }

}
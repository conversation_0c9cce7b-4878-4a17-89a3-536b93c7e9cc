import { LightningElement, api } from 'lwc';

export default class Lib_dialog extends LightningElement {
    @api dtitle;
    @api dok;
    @api dcancel;
    @api dmessage;
    @api disclose = false;

    handleclickok(event) {
        const devent = CustomEvent('handleclickok', {
            composed: true,
            bubbles: true,
            cancelable: true,
            detail: {},
        });
        this.dispatchEvent(devent);
    }

    handleclickcancel(event) {
        const devent = CustomEvent('handleclickcancel', {
            composed: true,
            bubbles: true,
            cancelable: true,
            detail: {},
        });
        this.dispatchEvent(devent);
    }
}
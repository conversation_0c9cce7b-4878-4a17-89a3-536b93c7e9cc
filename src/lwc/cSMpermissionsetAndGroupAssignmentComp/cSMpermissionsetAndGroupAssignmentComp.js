import { LightningElement, wire, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import GetGroupAndPermissionSetList from '@salesforce/apex/CSMDashboardPermissionAssignment.GetGroupAndPermissionSetList';
import getUsersAccessDetails from '@salesforce/apex/CSMDashboardPermissionAssignment.getUsersAccessDetails';
import SetGroupAndPermissionSetMethod from '@salesforce/apex/CSMDashboardPermissionAssignment.SetGroupAndPermissionSetMethod';
import SystemModstamp from '@salesforce/schema/Account.SystemModstamp';
import BusinessPerformanceLabel from '@salesforce/label/c.CSM_BusinessPerformanceDashboardUsers';
import BusinessPerformanceWithRevenueLabel from '@salesforce/label/c.CSM_BizPerfDashboardUsersRevenue';


export default class PermissionsetAndGroupAssignmentComp extends LightningElement {
    userId;
    BusinessPerformanceDashboardUsersID;
    CSMBizPerfDashboardUsersID;
    inputflag = true;
    messagePG = false;
    messagePS = false;
    title = '';
    message = '';
    variant = '';
    PermissionSet = [];
    PublicGroup = [];
    isOptions = false;
    PublicGroupOptions=[];
    PermissionSetOptions=[];
    PublicGroupID=[];
    PermissionSetID=[];
    PGvalue=[];
    PSvalue=[];
    AssignedOldPGvalue=[];
    AssignedPSvalue=[];
    isAOS = false;
    AOSPublicGroupOptions =[];
    CSMPublicGroupOptions =[];

    CSMPGvalue=[];
    AOSPGvalue=[]; 

    @wire(GetGroupAndPermissionSetList)
    wiredGroupAndPermissionSet({ error, data }) {
        if (data) {
            this.PermissionSet = data.PermissionSetList;
            this.PublicGroup = data.PublicGroupList;
            this.setOptions();
        } else if (error) {
            console.log(error);
        }
    }


    /*setOptions() {
        let PGlist = [];
        let PSlist = [];
        for(let PG of this.PublicGroup){
            PGlist.push({ label: PG.Name, value: PG.Id });
            this.PublicGroupID.push(PG.Id);
            if(PG.Name == BusinessPerformanceLabel){
                this.BusinessPerformanceDashboardUsersID =  PG.Id;
            }
            if(PG.Name == BusinessPerformanceWithRevenueLabel){
                this.CSMBizPerfDashboardUsersID = PG.Id;
            }
        }
        for(let PS of this.PermissionSet){
            PSlist.push({ label: PS.Label, value: PS.Id });
            this.PermissionSetID.push(PS.Id);
        }
        this.PublicGroupOptions = PGlist;
        this.PermissionSetOptions = PSlist;
        this.isOptions = true;

    }*/

    setOptions() {
    let AOSGroups = [];
    let CSMGroups = [];
    let PSlist = [];
    
    // Constants for AOS group identifiers
    const AOS_GROUP_IDENTIFIERS = ['AOS Backlog', 'AOS Sales'];
    
    for(let PG of this.PublicGroup){
        // Create base option object
        const groupOption = { label: PG.Name, value: PG.Id };
        
        // Add ID to the general PublicGroupID array
        this.PublicGroupID.push(PG.Id);
        
        // Sort into appropriate list based on group name
        if(AOS_GROUP_IDENTIFIERS.some(identifier => PG.Name.includes(identifier))) {
            AOSGroups.push(groupOption);
        } else {
            CSMGroups.push(groupOption);
        }
        
        // Keep existing special handling for specific groups
        if(PG.Name == BusinessPerformanceLabel){
            this.BusinessPerformanceDashboardUsersID = PG.Id;
        }
        if(PG.Name == BusinessPerformanceWithRevenueLabel){
            this.CSMBizPerfDashboardUsersID = PG.Id;
        }
    }
    
    // Handle Permission Sets
    for(let PS of this.PermissionSet){
        PSlist.push({ label: PS.Label, value: PS.Id });
        this.PermissionSetID.push(PS.Id);
    }
    
    // Store the separated lists
    this.AOSPublicGroupOptions = AOSGroups;     // New property for AOS groups
    this.CSMPublicGroupOptions = CSMGroups;     // New property for CSM/Business groups
    this.PublicGroupOptions = [...AOSGroups, ...CSMGroups]; // Keep original combined list if needed
    this.PermissionSetOptions = PSlist;
    console.log('this.PermissionSetOptions'+ JSON.stringify(this.PermissionSetOptions));
    this.isOptions = true;
}
    
    get selectedValues() {
        // Combine both group values for total selection
        return [...this.CSMPGvalue, ...this.AOSPGvalue].join(',');
    }
    /*handleChangePG(e) {
        this.PGvalue = e.detail.value;
        this.messagePG = false;
        if(!this.PGvalue.includes(this.BusinessPerformanceDashboardUsersID) && this.PGvalue.includes(this.CSMBizPerfDashboardUsersID)){
            this.messagePG = true;
            this.PGvalue = this.PGvalue.filter(option => {
                return option != this.CSMBizPerfDashboardUsersID;
            });
        }
    }

    handleChangePG(e) {
    this.PGvalue = e.detail.value;
    this.messagePG = false;
    
    this.isAOS = this.PGvalue.some(selectedId => {
        return this.AOSPublicGroupOptions.some(aosOption => aosOption.value === selectedId);
    });

    if(!this.PGvalue.includes(this.BusinessPerformanceDashboardUsersID) && 
       this.PGvalue.includes(this.CSMBizPerfDashboardUsersID)) {
        this.messagePG = true;
        this.PGvalue = this.PGvalue.filter(option => {
            return option != this.CSMBizPerfDashboardUsersID;
        });
    }
    }
    */
    handleChangePS(e){
        this.PSvalue = e.detail.value;
    }
   

   handleChangeCSMPG(e) {
        this.CSMPGvalue = e.detail.value;
        this.messagePG = false;
        if(!this.CSMPGvalue.includes(this.BusinessPerformanceDashboardUsersID) && this.CSMPGvalue.includes(this.CSMBizPerfDashboardUsersID)) {
            this.messagePG = true;
            this.CSMPGvalue = this.CSMPGvalue.filter(option => {
                return option != this.CSMBizPerfDashboardUsersID;
            });
        }
    }

    handleChangeAOSPG(e) {
        this.AOSPGvalue = e.detail.value;
        this.isAOS = this.AOSPGvalue.length > 0;
    }
    
    handleClick() {

        const combinedPGValue = [...this.CSMPGvalue, ...this.AOSPGvalue];
         console.log("PSvalue"+ this.PSvalue);
        SetGroupAndPermissionSetMethod({
            PGList: combinedPGValue,
            PSList: this.PSvalue,
            AssignedPGList: this.AssignedOldPGvalue,
            AssignedPSList: this.AssignedPSvalue,
            ListOfPG: this.PublicGroupID,
            ListOfPS: this.PermissionSetID,
            usrId: this.userId,
            isAOSDashboard: this.isAOS
        })
        .then(result => {
            console.log("Save Console"+result);
            this.template.querySelector('.userId').value = null;
            this.CSMPGvalue = [];
            this.AOSPGvalue = [];
            this.PSvalue = [];
            this.inputflag = true;
            this.title = 'Success';
            this.message = 'Operation Successful';
            this.variant = 'success';
            this.showToast();
        })
        .catch(error => {
            console.log("Error Console"+error.body.message);
            this.title = 'Error';
            this.message = error.body.message;
            this.variant = 'error';
            this.showToast();
        });
    }

    handleUserChange( event ) {

        let selectedUserId = event.target.value;
        console.log( 'Selected User Id is ' + selectedUserId );

        if ( selectedUserId ) {
            this.userId = selectedUserId;
            this.inputflag = false;
            getUsersAccessDetails({ usrId: this.userId })
		        .then(result => {
			    let SelectedPG = result.SelectedPublicGroupList;
                let SelectedPS = result.SelectedPermissionSetList;

                console.log("Inside Handle Change 1"+JSON.stringify(SelectedPG));
                console.log("SelectedPermissionSetList"+JSON.stringify(SelectedPS));
                /*for(let PG of SelectedPG){
                    this.PGvalue.push(PG.GroupId);
                    console.log("Selected Public Group"+this.PGvalue);
                }*/

                 for (let PG of SelectedPG) {
                    if (PG.Group.Name.includes('AOS')) {
                        this.AOSPGvalue.push(PG.GroupId);
                    } else {
                        this.CSMPGvalue.push(PG.GroupId);
                    }
                    }


                for(let PS of SelectedPS){
                    this.PSvalue.push(PS.PermissionSetId);
                    console.log("Selected Permission set"+PS.PermissionSetId);
                }
                
               console.log("AOS Public Groups:", JSON.stringify(this.AOSPGvalue));
                console.log("CSM Public Groups:", JSON.stringify(this.CSMPGvalue));


                this.AOSPGvalue = [...this.AOSPGvalue];
                this.CSMPGvalue = [...this.CSMPGvalue];

                this.PGvalue = [...this.PGvalue];
                  this.AssignedOldPGvalue = [...this.AOSPGvalue, ...this.CSMPGvalue];
//                this.AssignedOldPGvalue=this.PGvalue;
                console.log('PSvalue'+ JSON.stringify(this.PSvalue));
                this.PSvalue = [...this.PSvalue];
                this.AssignedPSvalue=this.PSvalue;
		    })
		    .catch(error => {
			    this.error = error;
                console.log("Error Message"+error);
                console.log("Error Message"+error.body.message);
		})
            
        } else {
            this.PGvalue = [];
            this.PSvalue = [];
            this.userId = null;
            this.inputflag = true;
            console.log( 'Selected User Id is Null');
        }
    }

    showToast() {
        const evt = new ShowToastEvent({
            title: this.title,
            message: this.message,
            variant: this.variant,
            mode: 'dismissable'
        });
        this.dispatchEvent(evt);
    }

    
}
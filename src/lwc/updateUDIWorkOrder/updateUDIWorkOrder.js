import { LightningElement, api, track }  from 'lwc';
import getAssetOptions from '@salesforce/apex/UpdateUDIWorkOrder.getAssetOptions';
import updateAssetRecords from '@salesforce/apex/UpdateUDIWorkOrder.updateAssetRecords';
import getSelectedAssetInformation from '@salesforce/apex/UpdateUDIWorkOrder.getSelectedAssetInformation';
import checkForErrors from '@salesforce/apex/UpdateUDIWorkOrder.checkForErrors';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CloseActionScreenEvent } from 'lightning/actions';
import { RefreshEvent } from 'lightning/refresh';

export default class UpdateUDIWorkOrder extends LightningElement {
    @api recordId;  
    @track isLoading = true; 
    @track deviceNumber = '';
    @track deviceDate = '';
    @track serialNumber = '';
    @track existingProductVersion = '';
    @track UDIProductiVersionHelpText = 'Enter the number starting after (01)';
    @track UDIDateHelpText = 'Enter the date starting after (11)';
    @track UDISerialHelpText = 'Enter the serial number starting after (21)';
    @track isLoading = false;
    @track showScreen1 = true;
    @track showScreen2 = false;
    @track assetOptionsMap = {};
    @track assetOptionsList = []; 
    @track selectedAssetValue = '';
    @track selectedValue = '';
    @track selectedRecordValue = '';
    @api assetRec;
    @api assetName;
    @api product;
    @api productversion;    
    @track noUDILabelChecked;
    @track apexErrorMessage;

    connectedCallback() {
        //console.log('recordId-------- ' + this.recordId);
        this.fetchAssetRecords();
    }

    fetchAssetRecords() {
        console.log('Fetching asset records...');
        
        // Use a timestamp to prevent caching issues
        const timestamp = new Date().getTime();
    
        getAssetOptions({ WorkOrderId: this.recordId, cacheBuster: timestamp })  
            .then((result) => {
                console.log('Fetched Assets:', JSON.stringify(result));
    
                // Reset and repopulate asset options
                this.assetOptionsMap = result;
                this.assetOptionsList = Object.keys(result).map(key => ({
                    label: key,
                    value: result[key]
                }));
    
                console.log('Updated assetOptionsList:', JSON.stringify(this.assetOptionsList));
    
                this.isLoading = false;
            })
            .catch((error) => {
                console.error('Error fetching assets:', error);
                this.isLoading = false;
            });
    }
    

    handleAssetSelection(event) {
        this.selectedValue = event.detail.value; 
        let inputString =this.selectedValue;
        let splitString = inputString.split("_");

        this.selectedAssetValue = splitString[0];
        this.selectedRecordValue = splitString[1];
        console.log('selectedValue: ', this.selectedValue);
        console.log('selectedAssetValue: ', this.selectedAssetValue);
        console.log('selectedRecordValue: ', this.selectedRecordValue);      
    }

    handleNext(event) {
        console.log('selectedAssetValue:', this.selectedAssetValue);
        if(this.selectedAssetValue == '') {
            this.showToast('Error', 'Please select an Asset', 'error');
            return;
        }
        else{
            this.showScreen1 = false;
            this.showScreen2 = true;
            this.getSelectedAssetInfo();
        }
        
    }

    getSelectedAssetInfo(){
        //debugger;
        getSelectedAssetInformation({assetRecId : this.selectedAssetValue})
        .then((result) => {
            console.log('78: ' , JSON.stringify(result));
            this.assetRec = result;
            this.assetName = result.Name;
            this.product = result.Product2.Name;
            this.productversion = result.Product_Version__r.Name;
            console.log('assetRec: ' , assetRec);
            console.log('Name: ', this.assetName);
            console.log('Product2: ', this.product);
            console.log('Product_Version__c: ', this.productversion);  
            })
        .catch((error) => {
            this.error = error;
            this.accounts = undefined;
            this.isLoading = false;
        });

         
    }

    handleBack() {
        // Reset inputs and switch back to Screen 1
        this.deviceNumber = '';
        this.deviceDate = '';
        this.serialNumber = '';
        this.selectedAssetValue = '';
        this.showScreen2 = false;
        this.showScreen1 = true;
    }

    handleDeviceNumberChange(event) {
        this.deviceNumber = event.target.value;
        console.log('Device Number:', this.deviceNumber); // Logs the device number to console
    }

    // Handler for device date input change
    handleDeviceDateChange(event) {
        this.deviceDate = event.target.value;
        console.log('Device Date:', this.deviceDate); // Logs the device date to console
    }

    // Handler for serial number input change
    handleSerialNumberChange(event) {
        this.serialNumber = event.target.value;
        console.log('Serial Number:', this.serialNumber); // Logs the serial number to console
    }

    async handleSave(event) {
        //debugger;
        this.isLoading = true;
    
        try {
            
            const result = await checkForErrors({
                assetRecord: this.assetRec,
                deviceNumber: this.deviceNumber,
                deviceDate: this.deviceDate,
                serialNumber: this.serialNumber
            });
    
            console.log('checkForErrors Response:', result);
            this.apexErrorMessage = result;
            console.log('apexErrorMessage:', this.apexErrorMessage);
    
            
            if (this.apexErrorMessage && this.apexErrorMessage !== 'No Errors') {
                this.showToast('Error', this.apexErrorMessage, 'error');
                this.isLoading = false;
                return;
            }
    
           
            if ((this.deviceNumber === '' && !this.noUDILabelChecked) || this.deviceDate === '' || this.serialNumber === '') {
                this.showToast('Error', 'Please enter all the required fields', 'error');
                this.isLoading = false;
                return;
            }
    
            var assetRecId = this.assetOptionsMap[this.selectedAssetValue];

            /*let str = this.serialNumber;
            var upperCaseSerialNumber;
            
            if (/[a-z]/.test(str)) {
                upperCaseSerialNumber = str.toUpperCase();
            }*/
            
            var varDeviceNumber = '01' + this.deviceNumber;
            var varDeviceDate = '11' + this.deviceDate;
            var varSerialNumber = '21' + this.serialNumber;
            var varUdiLabel = varDeviceNumber + varDeviceDate + varSerialNumber;
            var varUdiPI = '11' + this.deviceDate + '21' + this.serialNumber;
    
            
           const resultUpdate =  await updateAssetRecords({
                assetRecId: this.selectedAssetValue,
                udiLabel: varUdiLabel,
                udiPI: varUdiPI,
                deviceNumber: this.deviceNumber,
                RecId: this.selectedRecordValue
            });

            if (resultUpdate === 'success') {
                this.showToast('Success', 'Asset records updated successfully', 'success');
                this.dispatchEvent(new RefreshEvent());
                this.navigateToSelectionScreen();
            } else {
                this.showToast('Error', 'Failed to update asset records', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.isLoading = false;
            this.showToast('Error', 'Failed to update asset records', 'error');
        } finally {
            this.isLoading = false;
        }

    }
    

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant, // 'success', 'error', 'warning', 'info'
            mode: 'dismissable'
        });
        this.dispatchEvent(event);
    }

    closeComponent() {
        const closeActionEvent = new CloseActionScreenEvent();
        this.dispatchEvent(new closeActionEvent());
    }

    navigateToSelectionScreen(){
        console.log('inside navigation');
        this.assetOptionsMap = '';
        this.assetOptionsList = '';
        this.selectedAssetValue = '';
        this.deviceNumber = '';
        this.deviceDate = '';
        this.serialNumber = '';
        this.selectedAssetValue = '';
        this.showScreen2 = false;
        this.showScreen1 = true;
        this.isLoading = true;

        this.fetchAssetRecords();
    }

    handleNoUDILabelChange(event){
        this.noUDILabelChecked = event.target.checked;
        console.log('Checked value ' + this.noUDILabelChecked);;
        if (this.noUDILabelChecked) {
            this.deviceNumber = '';
        }
    }
}
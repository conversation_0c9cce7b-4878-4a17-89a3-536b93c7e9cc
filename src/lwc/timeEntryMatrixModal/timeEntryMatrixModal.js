import { api } from 'lwc';
import LightningModal from 'lightning/modal';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import setProlongedAbsence from '@salesforce/apex/TEM_ProlongedAbsence_Controller.setProlongedAbsence';
import getProlongedAbsenceTEM from '@salesforce/apex/TEM_ProlongedAbsence_Controller.getProlongedAbsenceTEM';
import updateExistingProlongedAbsenceTEM from '@salesforce/apex/TEM_ProlongedAbsence_Controller.updateExistingProlongedAbsenceTEM';
import deleteExistingProlongedAbsenceTEM from '@salesforce/apex/TEM_ProlongedAbsence_Controller.deleteExistingProlongedAbsenceTEM';

export default class TimeEntryMatrixModal extends LightningModal  {
    @api serviceResourceId;
    temData = [];
    firstDayOfWeek;
    temColumns = [
        {
            label: 'Monday', 
            type: 'temDatatype',
            fieldName: 'maxHours_Monday',
            typeAttributes: { 
                day: {
                    fieldName: 'monday'
                },
                limitHours: {
                    fieldName: 'limit_Monday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Tuesday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Tuesday',
            typeAttributes: { 
                day: {
                    fieldName: 'tuesday'
                },
                limitHours: {
                    fieldName: 'limit_Tuesday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Wednesday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Wednesday',
            typeAttributes: { 
                day: {
                    fieldName: 'wednesday'
                },
                limitHours: {
                    fieldName: 'limit_Wednesday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Thursday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Thursday',
            typeAttributes: { 
                day: {
                    fieldName: 'thursday'
                },
                limitHours: {
                    fieldName: 'limit_Thursday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Friday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Friday',
            typeAttributes: { 
                day: {
                    fieldName: 'friday'
                },
                limitHours: {
                    fieldName: 'limit_Friday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Saturday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Saturday',
            typeAttributes: { 
                day: {
                    fieldName: 'saturday'
                },
                limitHours: {
                    fieldName: 'limit_Saturday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        },
        {
            label: 'Sunday', 
            type: 'temDatatype', 
            fieldName: 'maxHours_Sunday',
            typeAttributes: { 
                day: {
                    fieldName: 'sunday'
                },
                limitHours: {
                    fieldName: 'limit_Sunday'
                }
            },
            cellAttributes: { alignment: 'center' },
            editable: true
        }
    ];

    prolongedAbsenceStartDate;
    prolongedAbsenceEndDate;
    comments;

    draftValues;

    selectedTEMRecords = [];
    showDeleteButton = false;

    async connectedCallback(){
        console.log('this.serviceResourceId',this.serviceResourceId);
        await this.getExistingTEMRecords();
    }

    async getExistingTEMRecords(){
        var prolongedAbsenceList = await getProlongedAbsenceTEM({
            serviceResourceId: this.serviceResourceId
        });
        console.dir(prolongedAbsenceList, { depth: null });

        this.firstDayOfWeek = prolongedAbsenceList.firstDayOfWeek;
        var weekArr = ['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'];
        var firstIndex = weekArr.findIndex(element => element == this.firstDayOfWeek);
        weekArr = weekArr.concat(weekArr.splice(0,firstIndex));
        var temColumn_copy = JSON.parse(JSON.stringify(this.temColumns));
        var tempArr = temColumn_copy.splice(0,firstIndex);
        temColumn_copy = temColumn_copy.concat(tempArr);
        console.log('temColumn_copy :');
        console.dir(temColumn_copy,{depth: null});
            
        var finalData = [];
        for(var recordIndex = 0;recordIndex < prolongedAbsenceList.temList.length; recordIndex++){
            var result = {};
            for(var index = 0;index < 7;index++){
                console.log(weekArr[index],weekArr[index].toLowerCase());
                result.Id = prolongedAbsenceList.temList[recordIndex].Id;
                result[weekArr[index].toLowerCase()] = prolongedAbsenceList.temList[recordIndex]['Day_' + (index + 1) + '_Start_Date_Time__c'].slice(0,10);
                result['maxHours_' + weekArr[index]] = Number(prolongedAbsenceList.temList[recordIndex]['Day_' + (index + 1) + '_Hours__c']);
                result['limit_' + weekArr[index]] = Number(prolongedAbsenceList['maxHoursDay' + (index + 1)]);
            }
            finalData.push(result);
        }
        this.temColumns = temColumn_copy;
        this.temData = finalData;
    }

    handleRowSelection(event){
        var selectedRows = JSON.parse(JSON.stringify(event.detail)).selectedRows;
        if(selectedRows.length > 0){
            this.showDeleteButton = true;
        }else{
            this.showDeleteButton = false;
        }
    }

    async handleDelete(event){
        var selectedRows = JSON.parse(JSON.stringify(this.template.querySelector('lightning-datatable').selectedRows));
        console.log('selectedRows : ');
        console.log(selectedRows);

        var temJSON = selectedRows.map(row => {
            return {
                Id: row
            }
        });

        try{
            await deleteExistingProlongedAbsenceTEM({
                existingJSONTEM: JSON.stringify(temJSON)
            });
            this.showToast('Success','Selected TEM Records have been successfully deleted','success');

            this.template.querySelector('lightning-datatable').selectedRows = [];
            var existingTableData = JSON.parse(JSON.stringify(this.temData));
            existingTableData = existingTableData.filter(data => !selectedRows.includes(data.Id));
            this.temData = existingTableData;
            this.showDeleteButton = false;
        }catch(error){
            this.showToast('Error','Error occurred while deleting Prolonged Absence : ' + error.body.message,'error')
            console.dir(error, { depth: null });
        }
    }

    async handleSave(event){
        var weekArr = ['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'];
        var firstIndex = weekArr.findIndex(element => element.label == this.firstDayOfWeek);
        weekArr = weekArr.concat(weekArr.splice(0,firstIndex));
        weekArr = weekArr.map(value => 'maxHours_' + value);
        var hoursArr = ['Day_1_Hours__c','Day_2_Hours__c','Day_3_Hours__c','Day_4_Hours__c','Day_5_Hours__c','Day_6_Hours__c','Day_7_Hours__c'];
        var fieldMap = new Map(weekArr.map((field,index) => [field,hoursArr[index]]));


        var draftValues = JSON.parse(JSON.stringify(event.detail.draftValues));

        var updatedTEMData = [];
        for(var index = 0;index < draftValues.length; index++){
            var newTEM = {};
            var objKeys = Object.keys(draftValues[index]);
            for(var keyIndex = 0;keyIndex < objKeys.length; keyIndex++){
                if(objKeys[keyIndex] == 'Id'){
                    newTEM.Id = draftValues[index]['Id'];
                }else{
                    newTEM[fieldMap.get(objKeys[keyIndex])] = draftValues[index][objKeys[keyIndex]];
                }
            }
            updatedTEMData.push(newTEM);
        }

        try{
            var result = await updateExistingProlongedAbsenceTEM({
                existingJSONTEM: JSON.stringify(updatedTEMData)
            });
            console.log('updateExistingProlongedAbsenceTEM result :');
            console.dir(result,{depth: null});

            this.showToast('Success','Existing Prolonged Absence Records were successfully updated','success');

            var existingTableData = JSON.parse(JSON.stringify(this.temData));
            draftValues.forEach(draftValue => {
                var rowId = draftValue.Id;
                var objKeys = Object.keys(draftValue).filter(key => key != 'Id');

                var rowIndex = existingTableData.findIndex(data => data.Id == rowId);
                objKeys.forEach(key => {
                    existingTableData[rowIndex][key] = draftValue[key];
                })
            });

            this.template.querySelector('lightning-datatable').draftValues = [];
            this.temData = existingTableData;
        }catch(error){
            this.showToast('Error','Error occurred while updating Prolonged Absence : ' + error.body.message,'error')
            console.dir(error, { depth: null });
        }
    }

    handleCellChange(event){
        var originalData = JSON.parse(JSON.stringify(this.temData));
        var cellDraftValue = JSON.parse(JSON.stringify(event.detail.draftValues[0]));
        var rowId = cellDraftValue.Id;
        var objectKeys = Object.keys(cellDraftValue);
        var dayVar = objectKeys.filter(key => key != 'Id')[0];
        var dayValue = cellDraftValue[dayVar];

        var originalRow = originalData.filter(data => data.Id == rowId)[0];
        if(originalRow[dayVar] == dayValue){
            var draftValues = JSON.parse(JSON.stringify(this.template.querySelector('lightning-datatable').draftValues));

            var rowIndex = draftValues.findIndex(value => value.Id == rowId);
            delete draftValues[rowIndex][dayVar];

            var remainingKeys = Object.keys(draftValues[rowIndex]);
            if(remainingKeys.length == 1 && remainingKeys[0] == 'Id'){
                draftValues = draftValues.filter(value => value.Id != rowId);
            }

            this.template.querySelector('lightning-datatable').draftValues = draftValues;
        }

        console.log('this.template.querySelector(\'lightning-datatable\').draftValues after changes');
        console.log(JSON.parse(JSON.stringify(this.template.querySelector('lightning-datatable').draftValues)));
    }

    handleCommentChange(event){
        this.comments = event.target.value;
    }

    compareDates(date1,date2){
        if(
            (date1 == null || date1 == undefined) &&
            (date2 != null && date2 != undefined)
        ){
            return -1;
        }

        if(
            (date1 != null && date1 != undefined) &&
            (date2 == null || date2 == undefined)
        ){
            return 1;
        }

        if(
            (
                date1.getYear() < date2.getYear()
            ) ||
            (
                date1.getYear() == date2.getYear() &&
                date1.getMonth() < date2.getMonth()
            ) ||
            (
                date1.getYear() == date2.getYear() &&
                date1.getMonth() == date2.getMonth() &&
                date1.getDate() < date2.getDate()
            )
        ){
            return -1;
        }else if(
            date1.getYear() == date2.getYear() &&
            date1.getMonth() == date2.getMonth() &&
            date1.getDate() == date2.getDate()
        ){
            return 0;
        }else{
            return 1;
        }
    }

    handleChangeDate(event) {
        var field = event.target;
        var inputValue = field.value;
        var [dateValue, startDate, endDate] = [inputValue, this.prolongedAbsenceStartDate, this.prolongedAbsenceEndDate].map(dateString => {
            if (dateString != null) {
                var [year, month, day] = dateString.split('-').map(stringValue => Number(stringValue));
                month = month - 1;
                return new Date(year, month, day);
            } else {
                return null;
            }
        })

        switch (field.title) {
            case 'prolongedAbsenceStartDate':
                if (dateValue != null && (this.compareDates(dateValue,new Date()) < 0)) {
                    field.setCustomValidity('Start Date cannot be lower than Current Date');
                } else {
                    this.prolongedAbsenceStartDate = inputValue;
                    field.setCustomValidity('');

                    if(this.compareDates(dateValue,endDate) > 0) {
                        var endDateField = this.template.querySelector('[title="prolongedAbsenceEndDate"]');
                        endDateField.setCustomValidity('End Date cannot be lower than Start Date');
                        endDateField.reportValidity();
                    }
                }
                field.reportValidity();
                break;
            case 'prolongedAbsenceEndDate':
                if (
                    dateValue != null && this.prolongedAbsenceStartDate != null &&
                    (this.compareDates(dateValue,startDate) < 0)
                ) {
                    field.setCustomValidity('End Date cannot be lower than Start Date');
                } else if (
                    dateValue != null &&
                    (this.compareDates(dateValue,new Date()) < 0)
                ) {
                    field.setCustomValidity('End Date cannot be lower than Current Date');
                } else {
                    this.prolongedAbsenceEndDate = inputValue;
                    field.setCustomValidity('');
                }
                field.reportValidity();
                break;
            default:
                break;
        }
    }

    validateProlongedAbsenceModal() {
        var startDateField = this.template.querySelector('[title="prolongedAbsenceStartDate"]');
        var endDateField = this.template.querySelector('[title="prolongedAbsenceEndDate"]');
        if (startDateField.value == null || startDateField.value == '') {
            console.log(startDateField);
            startDateField.setCustomValidity('Start Date is required');
        }
        if (endDateField.value == null || endDateField.value == '') {
            endDateField.setCustomValidity('End Date is required');
        }
        var startDateValidity = startDateField.reportValidity();
        var endDateValidity = endDateField.reportValidity();
        return startDateValidity && endDateValidity;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
          title: title,
          message: message,
          variant: variant
        });
        this.dispatchEvent(event);
    }

    async handleSaveProlongedAbsence(event) {
        var inputValidity = this.validateProlongedAbsenceModal();

        if (inputValidity) {
            /*Save Logic*/
            try {
                var prolongedAbsenceStartDate = this.template.querySelector('[title="prolongedAbsenceStartDate"]').value;
                var prolongedAbsenceEndDate = this.template.querySelector('[title="prolongedAbsenceEndDate"]').value;

                var result = await setProlongedAbsence({
                    prolongedAbsenceStartDate: prolongedAbsenceStartDate,
                    prolongedAbsenceEndDate: prolongedAbsenceEndDate,
                    serviceResourceId: this.serviceResourceId,
                    comments: this.comments
                });
                
                this.showToast('Success','Prolonged Absence has been set successfully','success')
                console.log('result from Modal :');
                console.log(JSON.parse(result));
                
                this.close(JSON.parse(result));
            } catch (error) {
                this.showToast('Error','Error occurred while setting Prolonged Absence : ' + error.body.message,'error')
                console.dir(error, { depth: null });
            }
            this.prolongedAbsenceStartDate = null;
            this.prolongedAbsenceEndDate = null;
        }
    }

    closeModal(){
        this.close();
    }
}
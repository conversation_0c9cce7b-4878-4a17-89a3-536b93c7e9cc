import { LightningElement,api,track } from 'lwc';
import getSOIEventNWAs from '@salesforce/apex/TE_SOIEventNWAListController.getSOIEventNWAs';

export default class Te_soiEventNWAListHistory extends LightningElement {
    @api soId;
    @api soIdList;
    @api saasList;
    spinner = false;
    @track sortBy='StartDateTime';
    @track sortDirection='desc';
    data = [];

    columns = [
        {
            label: 'Name',
            fieldName: 'SOIEventId',
            type: 'url',
            typeAttributes: {label: { fieldName: 'SOIEventName' }, 
            target: '_blank'},
            sortable: true
        },
        {
            label: 'Product', fieldName: 'ProductName',type: 'text', hideDefaultActions: true, sortable: true, initialWidth: 200
        },
        {
            label: 'Status',  fieldName: 'Status',type: 'text',initialWidth: 100, sortable: true
        },
        {
            label: 'Budgeted Hours', fieldName: 'budgetedHours',  type: 'number', hideDefaultActions: true, initialWidth: 90, sortable: true
        },
        {
            label: 'Forecast End Date', fieldName: 'ForecastEndDate',type: 'text', hideDefaultActions: true , sortable: true
        },
        {
            label: 'Start Date Time', fieldName: 'StartDateTime',type: 'text', hideDefaultActions: true , sortable: true
        },
        {
            label: 'End Date Time', fieldName: 'EndDateTime',type: 'text', hideDefaultActions: true , sortable: true
        },
        {
            label: 'Expiration Date', fieldName: 'ExpirationDate',type: 'text', hideDefaultActions: true , sortable: true
        },
        {
            label: 'Project Manager',  fieldName: 'ProjectManager',type: 'text',sortable: true
        },
        {
            label: 'Participant Name',  fieldName: 'ParticipantName',type: 'text',initialWidth: 200, sortable: true
        },
        {
            label: 'Comments',  fieldName: 'Comments',type: 'text',initialWidth: 200, sortable: true
        }
    ];

    connectedCallback(){
        console.log('##::'+this.soIdList+'::'+this.saasList);
        if(this.soIdList != null && this.soIdList.length > 0){
            this.soId = this.soIdList[0];
        }
        this.fetchSOIEventNWAs();
    }

    fetchSOIEventNWAs(){
        this.spinner = true;
        this.data = [];
        getSOIEventNWAs({soId : this.soId}) 
        .then(result => {
            this.data = result;
            this.spinner = false;
        })
        .catch(error => {           
           console.log(this.error);
           this.spinner = false;
        });
    }

    doSorting(event) {
        this.sortBy = event.detail.fieldName;
        this.sortDirection = event.detail.sortDirection;
        console.log('##event.detail:'+JSON.stringify(event.detail));
        this.sortData(this.sortBy, this.sortDirection);
    }
    
    sortData(fieldname, direction) {
        let parseData = JSON.parse(JSON.stringify(this.data));
        console.log('##businesses before:'+JSON.stringify(this.data));
        fieldname = (fieldname == 'SOIEventId')?'SOIEventName':fieldname;        
        let keyValue = (a) => {
            return a[fieldname];
        };
        let isReverse = direction === 'asc' ? 1: -1;
        console.log('##isReverse:'+isReverse);
        console.log('##keyValue:'+keyValue);
        // sorting data
        parseData.sort((x, y) => {
            x = keyValue(x) ? keyValue(x) : ''; 
            y = keyValue(y) ? keyValue(y) : '';
            return isReverse * ((x > y) - (y > x));
        });
        this.data = parseData;
        console.log('##this.data:'+JSON.stringify(this.data));
    }
}
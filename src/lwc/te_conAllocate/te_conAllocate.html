<template>
    <template if:true={isLocationModalOpen}>
        <section
            role="dialog"
            tabindex="-1"
            aria-labelledby="modal-heading-01"
            aria-modal="true"
            aria-describedby="modal-content-id-1"
            class="slds-modal slds-fade-in-open"
        >
            <div class="slds-modal__container">
                <!-- Modal/Popup Box LWC header here -->
                <header class="slds-modal__header">
                    <button
                        class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close"
                        onclick={closeLocModal}
                    >
                        <lightning-icon
                            icon-name="utility:close"
                            alternative-text="close"
                            variant="inverse"
                            size="small"
                        >
                        </lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2
                        id="modal-heading-000s1"
                        class="slds-text-heading_medium slds-hyphenate"
                    >
                        How many hours to be allocated
                    </h2>
                </header>
                <div
                    class="slds-modal__content slds-p-around_medium"
                    id="modal-content-id-1s11"
                >
                    <div class="slds-grid slds-wrap">
                        <div class="slds-col slds-size_2-of-3">
                            <lightning-radio-group
                                name="locations"
                                label="Locations"
                                options={locationOptn}
                                value={selectedlocation}
                                onchange={selLocationChange}
                                type="radio"
                            >
                            </lightning-radio-group>
                        </div>
                        <div class="slds-col slds-size_1-of-3">
                            <label
                                class="slds-form-element__label"
                                for="text-input-id-47"
                            >
                                <abbr class="slds-required" title="required"
                                    >*
                                </abbr>
                                Hours
                            </label>
                            <input
                                type="number"
                                class="slds-input"
                                min="1"
                                max="5"
                                value={quantity}
                                onchange={qntyChange}
                            />
                        </div>
                        <div if:false={isSOIECreated}
                                class="slds-is-relative c-spinnerStyle">
                                <lightning-spinner
                                alternative-text="Loading..."
                                size="large"
                                ></lightning-spinner>
                            </div>
                    </div>
                </div>
                <!-- Modal/Popup Box LWC footer starts here -->
                <footer class="slds-modal__footer">
                    <button
                        class="slds-button slds-button_neutral"
                        onclick={closeLocModal}
                        title="Cancel"
                    >
                        Cancel
                    </button>
                    <lightning-button
                                    label="Create SOI Event"
                                    variant="brand"
                                    class="slds-p-around_x-small"
                                    onclick={insertSOIEvents}
                                    disabled={isDisabled}
                                    if:true={isShowCreateSOI}
                                >
                                </lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>
import { LightningElement, track, wire } from 'lwc';
/*import getRecordsByNumber from '@salesforce/apex/SelectNumbersController.getRecordsByNumber'; */

export default class SelectNumbers extends LightningElement {
    @track selectedValue = '';
    @track records = [];
    
    numberOptions = [
        { label: '--None--', value: '' },
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
        { label: '4a', value: '4a' },
        { label: '4b', value: '4b' }
    ];

    handleChange(event) {
        this.selectedValue = event.detail.value;
    }

    runQuery() {
        if (this.selectedValue) {
          /*  getRecordsByNumber({ selectedValue: this.selectedValue })
                .then(result => {
                    this.records = result;
                })
                .catch(error => {
                    console.error('Error running SOQL query', error);
                }); */
        } else {
            alert('Please select a value first.');
        }
    }
}